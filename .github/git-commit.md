# GitHub Copilot Commit Message 指南

## 目的

本指南用于指导 GitHub Copilot 生成规范、清晰且信息丰富的 commit message，以便于团队协作和代码历史追踪。

## 格式要求

请按照以下格式生成 commit message：

```
<类型>(<范围>): <简短描述>

<详细描述>
```

## 类型参考

- **feat**: 新功能
- **fix**: 修复问题
- **docs**: 文档更改
- **style**: 不影响代码逻辑的格式修改
- **refactor**: 代码重构，既不修复错误也不添加功能
- **perf**: 性能优化
- **test**: 测试相关
- **build**: 构建系统或外部依赖项更改
- **ci**: CI配置文件和脚本更改
- **chore**: 其他杂项变更

## 示例

```
feat(用户模块): 添加用户注册功能

实现了手机号注册流程，包括短信验证码发送与校验
添加了用户基础信息的录入表单
```

```
fix(数据库): 修复连接池泄漏问题

修复了长时间运行后数据库连接无法释放的问题
添加了连接超时自动回收机制
```

## 注意事项

1. 使用中文描述提交内容，保持团队一致性
2. 描述要具体明确，避免过于笼统的表述
3. 必要时提供详细解释，尤其是复杂变更
4. 破坏性变更需在类型后加感叹号，如 `[feat]!`: 重构认证系统
5. 相关的 issue 或任务请务必引用