# Asset Syncer - AI Coding Assistant Instructions

## 项目概述

Asset Syncer 是一个多云资产同步服务，用于从阿里云、AWS、GCP、UCloud、火山引擎等云厂商同步资产信息到 OMA (Operations Management API) 系统。

## 核心命令

### 构建命令
- `task build-dist` / `task bd` - 构建生产环境二进制文件
- `task build-local` / `task bl` - 构建开发环境二进制文件  
- `go-task --color=false build-dist` - CI/CD 环境构建
- `./build.sh` - 完整构建脚本（清理 + 构建）

### 开发工具
- `task install-tools` / `task t` - 安装开发工具
- `task install-linters` / `task il` - 安装代码检查工具
- `task generate-models` / `task gm` - 从 protobuf 生成模型代码
- `task update` / `task u` / `task up` - 更新 Go 模块

### 清理命令
- `task clean` / `task c` - 清理日志文件和构建产物

### Docker 构建
- `task docker-build` / `task d` - 构建 Docker 镜像

### 测试命令
- `go test ./...` - 运行所有测试
- `go test ./internal/category/...` - 运行分类相关测试
- `go test ./internal/util/...` - 运行工具函数测试

## 架构概览

### 主要包结构
- `cmd/syncer/` - 主程序入口
- `internal/app/` - 应用程序框架和 CLI 命令
- `internal/restclient/` - OMA API 客户端封装
- `internal/factory/` - 各云厂商客户端工厂
- `internal/job/` - 同步任务调度和执行
- `internal/task/` - 各云厂商具体同步任务实现
- `internal/category/` - 资产分类和匹配逻辑
- `internal/beauty/` - 数据美化和格式化
- `internal/util/` - 通用工具函数
- `config/` - 配置管理
- `proto/models/` - Protobuf 模型定义

### 外部依赖
- **OMA API**: 资产管理系统后端 API (`https://oma-api.lilithgame.com/api/`)
- **云厂商 SDK**: 阿里云、AWS、GCP、UCloud、火山引擎等
- **KMS**: 密钥管理服务
- **飞书**: 通知服务

### 数据存储
- 无持久化存储，通过 REST API 与 OMA 系统交互
- 支持内存缓存优化性能

## 代码规范

### 通用规范
- 始终添加注释，使用 `google-code-style` 格式的 doc-string
- 函数参数和返回值必须有类型标注
- 使用 `go fmt` 和 `goimports` 格式化代码
- 错误处理使用 `gitlab.lilithgame.com/yunwei/pkg/wrap` 包

### 包导入规范
```go
import (
    // 标准库
    "context"
    "fmt"
    
    // 第三方库
    "github.com/gin-gonic/gin"
    
    // 内部包
    "gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/config"
    "gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
)
```

### 命名规范
- 包名使用小写单词，不使用下划线
- 接口名以 `er` 结尾（如 `Resourcer`）
- 常量使用大写字母和下划线
- 结构体字段使用 camelCase，带 JSON/TOML 标签

### 错误处理
- 使用 `gitlab.lilithgame.com/yunwei/pkg/wrap` 进行错误包装
- 预定义错误变量以 `Err` 开头（如 `ErrResourceNotFound`）
- 函数返回错误时使用 `(result, error)` 模式

### HTTP 客户端规范
- 使用 `internal/restclient` 包进行 API 调用
- 配置合理的超时时间和连接池参数
- 实现重试机制处理网络异常
- 使用泛型简化资源操作代码

## 关键组件

### RESTClient 配置
```go
// 正确配置 HTTP 客户端以避免连接问题
transport := &http.Transport{
    DialContext: (&net.Dialer{
        Timeout:   30 * time.Second,
        KeepAlive: 30 * time.Second,
    }).DialContext,
    ForceAttemptHTTP2:     true,
    MaxIdleConns:          100,
    IdleConnTimeout:       90 * time.Second,
    TLSHandshakeTimeout:   10 * time.Second,
    ExpectContinueTimeout: 1 * time.Second,
    MaxIdleConnsPerHost:   20,
    ResponseHeaderTimeout: 30 * time.Second,
}
```

### 模型生成
- 使用 protobuf 定义数据模型
- 运行 `task generate-models` 从 proto 文件生成 Go 代码
- 模型文件位于 `internal/restclient/models/`

### 云厂商集成
- 每个云厂商在 `internal/factory/` 有对应的工厂类
- 具体同步逻辑在 `internal/task/{provider}/` 实现
- 支持多账号、多区域并发同步

## 配置管理

### 主配置文件
- `conf.toml` - 主配置文件
- 支持环境变量覆盖
- 包含 OMA API、云厂商凭证、日志等配置

### 关键配置项
- `oma.url` - OMA API 基础 URL
- `oma.token` - API 访问令牌
- `log.level` - 日志级别
- `event_auth.token_values` - 事件认证令牌列表

## 部署信息

### Docker 构建
- 基于 Alpine Linux 的多阶段构建
- 静态链接二进制文件，支持跨平台部署
- 暴露 8080 端口提供 HTTP API

### Jenkins 集成
- `Jenkinsfile` 定义 CI/CD 流水线
- 支持自动构建和部署
- 使用 Docker Compose 进行部署

## 常见问题

### HTTP/2 连接问题
- 确保 `RESTClient` 配置了合适的超时和连接池参数
- 实现重试机制处理临时网络故障
- 避免使用默认的 `http.Client{}`

### 模型更新
- 修改 proto 文件后必须运行 `task generate-models`
- 更新模型后检查相关的同步逻辑是否需要调整

### 性能优化
- 使用 `internal/restclient/cache` 进行数据缓存
- 合理设置并发数量避免 API 限流
- 监控资源使用情况和同步性能
