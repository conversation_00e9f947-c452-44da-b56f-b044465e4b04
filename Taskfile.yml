# https://taskfile.dev

version: "3"

set: [pipefail, errexit, nounset]

vars:
  BUILD_DIR: ./targets
  BIN_DIR: ./bin
  EXE_NAME: syncer

tasks:
  build-dist:
    desc: Build product ready binary file
    aliases: [bd]
    cmds:
      - >-
        go build {{.GOFLAGS}} {{.GCFLAGS}}
        -ldflags "-s -w -extldflags '-static' -X 'gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/cmd.Version={{.Version}}'"
        -o {{.BUILD_DIR}}/${GOOS}-${GOARCH}/
        ./cmd/{{.EXE_NAME}}/
    env:
      CGO_ENABLED: 0
      GOOS: linux
      GOARCH: amd64

    vars:
      GOFLAGS: -trimpath
      LDFLAGS: -ldflags "-s -w"
      GCFLAGS: -gcflags "-N -l"
      Version:
        sh: git describe --tags --always --dirty

  build-local:
    desc: Build developing binary file
    aliases: [bl]
    # platforms: [darwin, linux, windows]
    dotenv: [".env"]
    cmds:
      - >-
        go build {{.LDFLAGS}}
        -trimpath
        -ldflags "-X 'gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/cmd.Version={{.Version}}'"
        -o {{.BUILD_DIR}}/{{OS}}-{{ARCH}}/
        ./cmd/{{.EXE_NAME}}/
    vars:
      LDFLAGS: -ldflags "-s -w"
      Version:
        sh: git describe --tags --always --dirty

  install-tools:
    desc: Install all tools
    aliases: [t]
    cmds:
      - echo "Install tools"
      - go install ./tools/...
    silent: true

  docker-build:
    desc: Build docker image
    aliases: [d]
    dotenv: [".env"]
    cmds:
      - docker build --build-arg TOKEN="${GL_TOKEN}" -t as:v0.4 .
    silent: true

  update:
    desc: Update go modules
    aliases: [u, up]
    cmds:
      - go get -u -v ./...
      - go mod download
      - go mod verify
      - go mod tidy

  install-linters:
    desc: Install linters
    aliases: [il]
    cmds:
      - go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest
      - go install mvdan.cc/gofumpt@latest
      - go install golang.org/x/tools/cmd/goimports@latest
      - go install honnef.co/go/tools/cmd/staticcheck@latest
      - go install github.com/Antonboom/errname@latest

  generate-models:
    desc: Generate models
    aliases: [gm]
    cmds:
      # - mkdir -p ./internal/restclient/models/v1
      # - protoc --oma_out=./internal/restclient/models/v1 --oma_opt=paths=source_relative -I./proto -I./proto/models proto/models/*.proto
      - protoc --oma_out=./internal/restclient/ --oma_opt=paths=source_relative,better_go_name=true -I./proto -I./proto/models proto/models/*.proto
      # - golangci-lint run --fix ./internal/restclient/models/v1

  clean:
    desc: Cleanup log file
    aliases: [c]
    cmds:
      - find . -type f -name "*.log" -print0 | xargs -0 rm -f -v
      - rm -rf asset-syncer syncer log/
      - rm -rf {{.BUILD_DIR}} {{.BIN_DIR}}
