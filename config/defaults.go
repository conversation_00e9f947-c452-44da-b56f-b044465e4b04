package config

import "time"

var defaultConfig = config{
	Log: LogConfig{
		Path:  "./log/app.log",
		Level: "debug",
	},

	EventAuth: EventAuth{
		TokenName: "apitoken",
	},

	EventRequestCooldown: 3 * time.Second,

	OMA: OMAConfig{
		BaseURL: "https://oma-api.lilithgame.com/api/",
	},

	KMS: KMSConfig{
		Endpoint: "kms.cn-shanghai.aliyuncs.com",
	},

	Feishu: FeishuConfig{
		BaseURL: "https://open.feishu.cn/open-apis",
	},

	Registry: RegistryConfig{
		BaseUrl:   "https://oma-registry.lilithgame.com/api/admin/jobs",
		TokenName: "oma-registry-token",
	},
}
