package config

import "time"

type config struct {
	Log      LogConfig      `mapstructure:"log" json:"log"`
	OMA      OMAConfig      `mapstructure:"oma" json:"oma"`
	KMS      KMSConfig      `mapstructure:"kms" json:"kms"`
	Feishu   FeishuConfig   `mapstructure:"feishu" json:"feishu"`
	Registry RegistryConfig `mapstructure:"registry" json:"registry"`

	EventAuth            EventAuth     `mapstructure:"event_auth" json:"event_auth"`
	EventRequestCooldown time.Duration `mapstructure:"event_request_cooldown" json:"event_request_cooldown"`
}

type EventAuth struct {
	TokenName   string   `mapstructure:"token_name" json:"token_name"`
	TokenValues []string `mapstructure:"token_values" json:"token_values"`
}

type LogConfig struct {
	Path  string `mapstructure:"path" json:"path"`
	Level string `mapstructure:"level" json:"level"`
}

type OMAConfig struct {
	BaseURL  string `mapstructure:"url"`
	Username string `mapstructure:"username"`
	Password string `mapstructure:"password"`
	Token    string `mapstructure:"token"`
}

type KMSConfig struct {
	Endpoint        string `mapstructure:"endpoint"`
	AccessKeyID     string `mapstructure:"access_key"`
	SecretAccessKey string `mapstructure:"secret_key"`
}

type FeishuConfig struct {
	BaseURL   string `mapstructure:"base_url"`
	AppID     string `mapstructure:"app_id"`
	AppSecret string `mapstructure:"app_secret"`
}

type RegistryConfig struct {
	BaseUrl   string `json:"base_url"`
	TokenName string `mapstructure:"token_name"`
	Token     string `mapstructure:"token"`
}
