package config

import (
	"log"
	"sync"

	"github.com/spf13/viper"
)

var (
	cfg       *config
	once      sync.Once
	cfgLocker = new(sync.RWMutex)
)

func Init() {
	once.Do(loadconfig)
}

func Config() *config {
	// ensure config loaded
	once.Do(loadconfig)
	cfgLocker.RLock()
	defer cfgLocker.RUnlock()
	return cfg
}

func loadconfig() {
	viper.SetConfigName("conf")
	viper.AddConfigPath(".")
	viper.AddConfigPath("./config")

	if err := viper.ReadInConfig(); err != nil {
		if _, ok := err.(viper.ConfigFileNotFoundError); !ok {
			log.Fatalf("read config failed: %s", err.Error())
		}
	}

	tmpConfig := defaultConfig
	if err := viper.Unmarshal(&tmpConfig); err != nil {
		log.Fatalf("unable decode config file, %v", err)
	}

	cfgLocker.Lock()
	cfg = &tmpConfig
	cfgLocker.Unlock()
}
