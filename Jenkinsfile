// node('master') {
node {
    wrap([$class: 'BuildUser']) {
        user_email = env.BUILD_USER_EMAIL
    }
    stage('Prepare') {
        echo '1.Prepare Stage'
        checkout scm
        sh "git checkout ${params.Branch}"
        BRANCH_NAME = params.Branch
        if (BRANCH_NAME ==~ /origin.*/) {
            BRANCH_NAME = BRANCH_NAME.replace('origin/', '').replace('/', '-')
            BUILD_TIME = sh(returnStdout: true, script: 'date "+%Y%m%d%H%M%S"').trim()
            SHORT_COMMIT = sh(returnStdout: true, script: 'git rev-parse --short HEAD').trim()
            APP_TAG = "${BRANCH_NAME}-${SHORT_COMMIT}-${BUILD_TIME}"
        } else {
            APP_TAG = BRANCH_NAME
        }
        echo 'APP_TAG:' + APP_TAG
        echo "user email: ${user_email}"

        Cluster = params.Cluster
        Ns = params.Namespace
        NAMESPACE = 'platform'
        Mesh = 'no-mesh'
        //ops_server = 'http://*************'
        ops_server = 'https://kma.lilith.com'
        ops_token = 'ops-access-token'

        if (!(Cluster =~ /.*public*/)) {
            ops_server = 'https://kma.farlightgames.com'
            ops_token = 'ops-access-token-farlight'
        } else if (Cluster ==~ /aor*/) {
            ops_server = 'https://kma-aws.lilithgame.com'
            ops_token = 'ops-access-token-aor'
        }

        if (Ns ==~ /.*mesh/) {
            Mesh = 'mesh'
        }

        IMAGE_BASE = 'lilith-registry.cn-shanghai.cr.aliyuncs.com'
        REGISTRY = 'lilith-registry.cn-shanghai.cr.aliyuncs.com'
        CHART_BASE = 'acr://lilith-chart.cn-shanghai.cr.aliyuncs.com'
        APP_NAME = env.JOB_BASE_NAME

        script {
            withCredentials([string(credentialsId: ops_token, variable: 'SECRET')]) {
                HELM_VERSION = sh(
                    returnStdout: true,
                    script: "curl -H 'Authorization: Token $SECRET' ${ops_server}/api/fetch_chart_version/$APP_NAME",
                )
            }
            IMAGE_NAME = "${IMAGE_BASE}/${NAMESPACE}/${APP_NAME}"
            echo "IMAGE_NAME: ${IMAGE_NAME}"
            CHART_REPO = "${CHART_BASE}/${NAMESPACE}/${APP_NAME}"
            echo "CHART_REPO: ${CHART_REPO}"
        }
    }

    stage('Build') {
        echo '3.Build Docker Image Stage'
        withCredentials([string(credentialsId: 'gear-jenkins-token', variable: 'TOKEN')]) {
            sh """
                echo 'machine gitlab.lilithgame.com login gear-jenkins-token password $TOKEN' > netrc
                chmod 600 netrc
                docker build --secret id=netrc,src=netrc --build-arg APP_VSN=${APP_TAG} --build-arg APP_NAME=${APP_NAME} -t ${IMAGE_NAME}:${APP_TAG} .
                rm -f netrc
            """
            // sh "docker build --build-arg TOKEN_NAME=gear-jenkins-token --build-arg TOKEN=$TOKEN --build-arg APP_VSN=${APP_TAG} --build-arg APP_NAME=${APP_NAME} -t ${IMAGE_NAME}:${APP_TAG} ."
        }
    }

    stage('Docker Login') {
        echo '4. Login docker registry...'
        withCredentials([usernamePassword(
                credentialsId: 'dockerHub2',
                passwordVariable: 'dockerHubPassword',
                usernameVariable: 'dockerHubUser',
                )]) {
            sh "echo $dockerHubPassword | docker login $REGISTRY -u $dockerHubUser --password-stdin"
                }
    }

    stage('Push Image') {
        echo '5. Push Docker Image'
        sh "docker push $IMAGE_NAME:$APP_TAG"
    }

    stage('helm-push') {
        echo '6. Helm Push'
        withCredentials([string(credentialsId: ops_token, variable: 'TOKEN')]) {
            try {
                CURL_RESULT = sh(
                    script: "curl -H 'Authorization: Token ${TOKEN}' -s -w %{http_code} -F projectName=$APP_NAME -F branchName=$APP_TAG -F image=$IMAGE_NAME -F tag=$APP_TAG -F repo=${CHART_REPO} -F chartVersion=${HELM_VERSION} -F cluster=${Cluster} -F namespace=${Ns} -F email=${user_email}  -F mesh=${Mesh} ${ops_server}/api/build/",
                    returnStdout: true
                )
                sh "echo $CURL_RESULT | grep 201"
            } catch (exc) {
                echo 'build_aborted'
                sh(
                    script: "curl -H 'Authorization: Token ${TOKEN}' -s -w %{http_code} -F projectName=$APP_NAME -F chartVersion=$HELM_VERSION -F cluster=$Cluster -F namespace=$Ns -F email=$user_email $ops_server/api/build_aborted/",
                    returnStdout: true
                )
                sh 'exit 1'
            }
        }
    }
}
