module gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer

go 1.24.0

toolchain go1.24.4

require (
	cloud.google.com/go/compute v1.39.0
	cloud.google.com/go/container v1.43.0
	cloud.google.com/go/osconfig v1.14.6
	cloud.google.com/go/redis v1.18.2
	cloud.google.com/go/resourcemanager v1.10.6
	github.com/BurntSushi/toml v1.5.0
	github.com/alibabacloud-go/alb-20200616/v2 v2.2.9
	github.com/alibabacloud-go/cs-20151215/v5 v5.9.4
	github.com/alibabacloud-go/darabonba-openapi/v2 v2.1.7
	github.com/alibabacloud-go/dds-20151201/v4 v4.2.0
	github.com/alibabacloud-go/ecs-20140526/v3 v3.0.14
	github.com/alibabacloud-go/eipanycast-20200309/v2 v2.1.3
	github.com/alibabacloud-go/emr-20210320/v2 v2.3.0
	github.com/alibabacloud-go/hbase-20190101/v3 v3.1.3
	github.com/alibabacloud-go/kms-20160120/v3 v3.2.3
	github.com/alibabacloud-go/nlb-20220430/v3 v3.1.1
	github.com/alibabacloud-go/polardb-20170801/v3 v3.0.2
	github.com/alibabacloud-go/r-kvstore-20150101/v3 v3.5.5
	github.com/alibabacloud-go/rds-20140815/v3 v3.4.0
	github.com/alibabacloud-go/slb-20140515/v4 v4.0.10
	github.com/alibabacloud-go/sls-20201230/v6 v6.9.2
	github.com/alibabacloud-go/tea v1.3.9
	github.com/alibabacloud-go/vpc-20160428/v2 v2.0.117
	github.com/alibabacloud-go/vpc-20160428/v6 v6.12.7
	github.com/aws/aws-sdk-go v1.55.7
	github.com/cloudevents/sdk-go/v2 v2.16.1
	github.com/fatih/color v1.18.0
	github.com/gin-gonic/gin v1.10.1
	github.com/googleapis/gax-go/v2 v2.14.2
	github.com/pkg/errors v0.9.1
	github.com/robfig/cron/v3 v3.0.1
	github.com/spf13/viper v1.20.1
	github.com/tencentcloud/tencentcloud-sdk-go v3.0.233+incompatible
	github.com/ucloud/ucloud-sdk-go v0.22.44
	github.com/urfave/cli/v2 v2.27.7
	github.com/volcengine/volcengine-go-sdk v1.1.19
	gitlab.lilithgame.com/yunwei/cloudmeta v0.0.0-20240723104210-181d4cbb7ebd
	gitlab.lilithgame.com/yunwei/pkg v0.2.7
	golang.org/x/exp v0.0.0-20250620022241-b7579e27df2b
	golang.org/x/sync v0.15.0
	golang.org/x/text v0.26.0
	google.golang.org/api v0.239.0
	google.golang.org/protobuf v1.36.6
)

require (
	cloud.google.com/go v0.121.3 // indirect
	cloud.google.com/go/auth v0.16.2 // indirect
	cloud.google.com/go/auth/oauth2adapt v0.2.8 // indirect
	cloud.google.com/go/compute/metadata v0.7.0 // indirect
	cloud.google.com/go/iam v1.5.2 // indirect
	cloud.google.com/go/longrunning v0.6.7 // indirect
	github.com/alibabacloud-go/alibabacloud-gateway-pop v0.0.8 // indirect
	github.com/alibabacloud-go/alibabacloud-gateway-sls v0.3.0 // indirect
	github.com/alibabacloud-go/alibabacloud-gateway-sls-util v0.3.0 // indirect
	github.com/alibabacloud-go/alibabacloud-gateway-spi v0.0.5 // indirect
	github.com/alibabacloud-go/darabonba-array v0.1.0 // indirect
	github.com/alibabacloud-go/darabonba-encode-util v0.0.2 // indirect
	github.com/alibabacloud-go/darabonba-map v0.0.2 // indirect
	github.com/alibabacloud-go/darabonba-signature-util v0.0.7 // indirect
	github.com/alibabacloud-go/darabonba-string v1.0.2 // indirect
	github.com/alibabacloud-go/debug v1.0.1 // indirect
	github.com/alibabacloud-go/endpoint-util v1.1.1 // indirect
	github.com/alibabacloud-go/openapi-util v0.1.1 // indirect
	github.com/alibabacloud-go/tea-utils/v2 v2.0.7 // indirect
	github.com/aliyun/credentials-go v1.4.6 // indirect
	github.com/bytedance/sonic v1.13.3 // indirect
	github.com/bytedance/sonic/loader v0.2.4 // indirect
	github.com/cenkalti/backoff/v4 v4.3.0 // indirect
	github.com/clbanning/mxj/v2 v2.7.0 // indirect
	github.com/cloudwego/base64x v0.1.5 // indirect
	github.com/cpuguy83/go-md2man/v2 v2.0.7 // indirect
	github.com/felixge/httpsnoop v1.0.4 // indirect
	github.com/fsnotify/fsnotify v1.9.0 // indirect
	github.com/gabriel-vasile/mimetype v1.4.9 // indirect
	github.com/gin-contrib/sse v1.1.0 // indirect
	github.com/go-logr/logr v1.4.3 // indirect
	github.com/go-logr/stdr v1.2.2 // indirect
	github.com/go-playground/locales v0.14.1 // indirect
	github.com/go-playground/universal-translator v0.18.1 // indirect
	github.com/go-playground/validator/v10 v10.26.0 // indirect
	github.com/go-viper/mapstructure/v2 v2.3.0 // indirect
	github.com/goccy/go-json v0.10.5 // indirect
	github.com/google/s2a-go v0.1.9 // indirect
	github.com/google/uuid v1.6.0 // indirect
	github.com/googleapis/enterprise-certificate-proxy v0.3.6 // indirect
	github.com/jmespath/go-jmespath v0.4.0 // indirect
	github.com/json-iterator/go v1.1.12 // indirect
	github.com/klauspost/compress v1.18.0 // indirect
	github.com/klauspost/cpuid/v2 v2.2.11 // indirect
	github.com/leodido/go-urn v1.4.0 // indirect
	github.com/mattn/go-colorable v0.1.14 // indirect
	github.com/mattn/go-isatty v0.0.20 // indirect
	github.com/modern-go/concurrent v0.0.0-20180306012644-bacd9c7ef1dd // indirect
	github.com/modern-go/reflect2 v1.0.2 // indirect
	github.com/pelletier/go-toml/v2 v2.2.4 // indirect
	github.com/pierrec/lz4 v2.6.1+incompatible // indirect
	github.com/russross/blackfriday/v2 v2.1.0 // indirect
	github.com/sagikazarmark/locafero v0.9.0 // indirect
	github.com/sirupsen/logrus v1.9.3 // indirect
	github.com/sourcegraph/conc v0.3.0 // indirect
	github.com/spf13/afero v1.14.0 // indirect
	github.com/spf13/cast v1.9.2 // indirect
	github.com/spf13/pflag v1.0.6 // indirect
	github.com/subosito/gotenv v1.6.0 // indirect
	github.com/tjfoc/gmsm v1.4.1 // indirect
	github.com/twitchyliquid64/golang-asm v0.15.1 // indirect
	github.com/ugorji/go/codec v1.3.0 // indirect
	github.com/volcengine/volc-sdk-golang v1.0.213 // indirect
	github.com/xrash/smetrics v0.0.0-20240521201337-686a1a2994c1 // indirect
	go.opentelemetry.io/auto/sdk v1.1.0 // indirect
	go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc v0.62.0 // indirect
	go.opentelemetry.io/contrib/instrumentation/net/http/otelhttp v0.62.0 // indirect
	go.opentelemetry.io/otel v1.37.0 // indirect
	go.opentelemetry.io/otel/metric v1.37.0 // indirect
	go.opentelemetry.io/otel/trace v1.37.0 // indirect
	go.uber.org/multierr v1.11.0 // indirect
	go.uber.org/zap v1.27.0 // indirect
	golang.org/x/arch v0.18.0 // indirect
	golang.org/x/crypto v0.39.0 // indirect
	golang.org/x/net v0.41.0 // indirect
	golang.org/x/oauth2 v0.30.0 // indirect
	golang.org/x/sys v0.33.0 // indirect
	golang.org/x/time v0.12.0 // indirect
	google.golang.org/genproto v0.0.0-20250603155806-513f23925822 // indirect
	google.golang.org/genproto/googleapis/api v0.0.0-20250603155806-513f23925822 // indirect
	google.golang.org/genproto/googleapis/rpc v0.0.0-20250603155806-513f23925822 // indirect
	google.golang.org/grpc v1.73.0 // indirect
	gopkg.in/ini.v1 v1.67.0 // indirect
	gopkg.in/natefinch/lumberjack.v2 v2.2.1 // indirect
	gopkg.in/yaml.v2 v2.4.0 // indirect
	gopkg.in/yaml.v3 v3.0.1 // indirect
)
