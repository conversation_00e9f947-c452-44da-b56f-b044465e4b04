# declare arguments' defaults (optional)
ARG APP_NAME=main
ARG APP_VSN=unknown
ARG TOKEN_NAME=anonymous
ARG TOKEN=gitlab-access-token

# ARG **********************registry-vpc.cn-shanghai.cr.aliyuncs.com/devtest/golang
# ARG IMAGE_REGISTRY=dockerproxy.com/library/golang
ARG IMAGE_REGISTRY=golang
ARG GO_VERSION=1.24.4-alpine

# builder
FROM ${IMAGE_REGISTRY}:${GO_VERSION} AS builder

# use external arguments(import!)
# ref: https://docs.docker.com/engine/reference/builder/#understand-how-arg-and-from-interact
# ARG TOKEN_NAME
# ARG TOKEN
ARG GO_VERSION

LABEL authors="clydeyu <<EMAIL>>, Tyven <<EMAIL>>"

ENV TZ=Asia/Shanghai \
    GOPRIVATE="gitlab.lilithgame.com"
# GOPROXY="https://goproxy.cn,direct" \

ADD . /build
WORKDIR /build

# echo "machine gitlab.lilithgame.com login ${TOKEN_NAME} password ${TOKEN}" > ~/.netrc && \
RUN \
    --mount=type=secret,id=netrc,target=/root/.netrc \
    sed -i 's/dl-cdn.alpinelinux.org/mirrors.tuna.tsinghua.edu.cn/g' /etc/apk/repositories && \
    apk update && \
    apk --no-cache --no-progress add -U tzdata ca-certificates git go-task && \
    go-task --color=false build-dist

# ref: github.com/GoogleContainerTools/distroless
# FROM gcr.io/distroless/static-debian12 as runner
FROM alpine AS runner

ENV TZ=Asia/Shanghai

WORKDIR /app

COPY --from=builder /build/targets/linux-amd64/syncer /app/
COPY --from=builder /etc/ssl/certs/ca-certificates.crt /etc/ssl/certs/ca-certificates.crt
COPY --from=builder /usr/share/zoneinfo/Asia/Shanghai /usr/share/zoneinfo/Asia/Shanghai
# conf.ini will injected by configmap at /app/config/conf.ini

EXPOSE 8080

ENTRYPOINT ["/app/syncer"]

CMD ["run"]
