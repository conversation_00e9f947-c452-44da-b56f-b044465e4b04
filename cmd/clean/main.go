package main

import (
	"fmt"

	"github.com/fatih/color"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
)

func main() {
	machines, err := restclient.ListAll[models.Machine](
		fields.FactoryField(16),
		fields.IsNull("account"),
		fields.LessThan("updated_before", **********),
	)
	if err != nil {
		panic(err)
	}

	color.Blue("found %d machines to delete\n", machines.Count)

	var total int
	for _, m := range machines.Results {
		if err := restclient.Delete(m); err != nil {
			color.Red("delete %s error %v", m.ExternalName, err)
		} else {
			total += 1
			color.Green("%s deleted", m.ExternalName)
		}
	}

	fmt.Printf("delete %d/%d machines\n", total, machines.Count)
}
