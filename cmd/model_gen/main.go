package model_gen

import (
	"fmt"
	"log/slog"
	"os"

	"google.golang.org/protobuf/compiler/protogen"
	"google.golang.org/protobuf/reflect/protoreflect"
)

const (
	generaterName = "oma-generator"
	fileHeader    = "// Code generated by " + generaterName + ". DO NOT EDIT."
)

// GenerateVersionMarkers specifies whether to generate version markers.
var GenerateVersionMarkers = false

func init() {
	slog.SetDefault(slog.New(slog.NewTextHandler(os.Stderr, &slog.HandlerOptions{
		Level: slog.LevelDebug,
	})))
}

func GenerateModelCode() {
	protogen.Options{}.Run(func(p *protogen.Plugin) error {
		for _, f := range p.Files {
			if !f.Generate {
				slog.Warn("Skipping file", "file", f.Desc.Path())
				continue
			}
			slog.Info("Generate file", "file", f.Desc.Path())
			generateCustomFile(p, f)
		}

		return nil
	})
}

func genFileHeader(p *protogen.Plugin, g *protogen.GeneratedFile, file *protogen.File) {
	g.P(fileHeader)

	// protoc version
	if GenerateVersionMarkers {
		protocVersion := "(unknown)"
		if v := p.Request.GetCompilerVersion(); v != nil {
			protocVersion = fmt.Sprintf("v%v.%v.%v", v.GetMajor(), v.GetMinor(), v.GetPatch())
		}
		g.P("// protoc version: ", protocVersion)
	}

	if file.Proto.GetOptions().GetDeprecated() {
		g.P("// ", file.Desc.Path(), " is a deprecated file.")
	} else {
		g.P("// source: ", file.Desc.Path())
	}
	// g.P()
}

func generateCustomFile(p *protogen.Plugin, file *protogen.File) {
	filename := file.GeneratedFilenamePrefix + ".go"
	gf := p.NewGeneratedFile(filename, file.GoImportPath)

	genFileHeader(p, gf, file)

	// Add package declaration
	gf.P("package ", file.GoPackageName)

	// Generate structs from messages
	for _, msg := range file.Messages {
		generateStructForMessage(gf, msg)
	}

	for _, svc := range file.Services {
		// generateClientForService(g, svc)
		slog.Debug("Generating service", "service", svc.GoName)
	}
}

func generateStructForMessage(g *protogen.GeneratedFile, msg *protogen.Message) {
	g.P("type ", msg.GoIdent, " struct {")

	subMessages := make([]*protogen.Message, 0)

	for _, field := range msg.Fields {
		goType := field.Desc.Kind().String()

		isPointer := field.Desc.HasPresence()

		switch field.Desc.Kind() {
		case protoreflect.StringKind:
			goType = "string"
		case protoreflect.BytesKind:
			goType = "[]byte"

		case protoreflect.BoolKind:
			goType = "bool"

		case protoreflect.EnumKind:
			goType = g.QualifiedGoIdent(field.Enum.GoIdent)

		case protoreflect.Int32Kind:
			goType = "int32"
		case protoreflect.Uint32Kind:
			goType = "uint32"
		case protoreflect.Int64Kind:
			goType = "int64"
		case protoreflect.Uint64Kind:
			goType = "uint64"

		case protoreflect.DoubleKind, protoreflect.FloatKind:
			goType = "float64"

		case protoreflect.MessageKind:
			goType = "*" + g.QualifiedGoIdent(field.Message.GoIdent)
			isPointer = false
			subMessages = append(subMessages, field.Message)

		case protoreflect.GroupKind:
			isRepeated := field.Desc.Cardinality()
			slog.Debug("Skipping group kind field", "field", field.GoName, "kind", field.Desc.Kind(), "cardinality", isRepeated)
			continue
		}

		if isPointer {
			goType = "*" + goType
		}

		jsonTag := fmt.Sprintf(`json:"%s"`, field.Desc.TextName())

		// Get field options
		fieldOptions := field.Desc.Options()
		fieldOptions.ProtoReflect().Range(func(fd protoreflect.FieldDescriptor, value protoreflect.Value) bool {
			if fd.IsExtension() {
				return true // Skip extensions
			}
			fieldOptionsProto := fmt.Sprintf("%s: %v", fd.Name(), value.Interface())
			slog.Debug("Field option", "field", field.GoName, "option", fieldOptionsProto)
			return true
		})

		slog.Debug("field info",
			"kind", field.Desc.Kind(),
			"type", goType,
			"original", field.GoName,
			"isPointer", isPointer,
			"field_options", fieldOptions,
		)

		g.P("\t", field.GoName, "\t", goType, "\t`", jsonTag, "`")
	}
	g.P("}")
	g.P()

	// Handle sub-messages recursively
	for _, subMsg := range subMessages {
		generateStructForMessage(g, subMsg)
	}
}
