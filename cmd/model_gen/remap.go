package model_gen

import "strings"

// toGoFieldName converts a proto field name to Go field name convention
// Examples: "user_name" -> "UserName", "id" -> "Id", "user_id" -> "UserId"
func toGoFieldName(name string) string {
	if name == "" {
		return ""
	}

	// Split by underscore and convert each part
	parts := strings.Split(name, "_")
	for i, part := range parts {
		if len(part) > 0 {
			// Convert first letter to uppercase, rest to lowercase
			parts[i] = strings.ToUpper(part[:1]) + strings.ToLower(part[1:])
		}
	}

	return strings.Join(parts, "")
}
