package main

import (
	"encoding/json"
	"fmt"
	"os"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
)

func main() {
	resp, err := restclient.List[models.Factory](fields.Unlimited)
	if err != nil {
		fmt.Printf("Error fetching: %v\n", err)
		os.Exit(1)
	}

	// 遍历结果并格式化输出
	for i, g := range resp.Results {
		fmt.Printf("%d => ", i)
		// 使用 MarshalIndent 进行美观的 JSON 格式化输出
		jsonData, err := json.MarshalIndent(g, "", "  ")
		if err != nil {
			fmt.Printf("Error marshaling JSON: %v\n", err)
			continue
		}
		// 输出格式化的 JSON 到标准输出流
		fmt.Println(string(jsonData))
	}
}
