package main

import (
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/factory"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/task/aws/eks"
)

func main() {
	// eks.SyncAll()

	s := eks.NewBatchSyncerWithFactoryKeys(
		factory.AWSFarlight.String(),
		// factory.AWSPlat.String(),
		// factory.AWSPlat2.String(),
		// factory.AWSSamo.String(),
		// factory.AWSMona.String(),
		// factory.AWSAd.String(),
	)
	s.Sync()
}
