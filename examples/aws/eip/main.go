package main

import (
	"gitlab.lilithgame.com/yunwei/pkg/wrap"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/factory"
	awseiptask "gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/task/aws/eip"
)

func main() {
	// FactoryAWSSamo     FactoryKeyType = "aws-samo"
	// FactoryAWSPlat     FactoryKeyType = "aws-plat"
	// FactoryAWSPlat2    FactoryKeyType = "aws-plat2"
	// FactoryAWSMona     FactoryKeyType = "aws-mona"
	// FactoryAWSAd       FactoryKeyType = "aws-ad"

	s := wrap.Must(awseiptask.NewSyncer(factory.AWSPlat.String()))
	s.SyncAll()
}
