package main

import (
	"fmt"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
)

func main() {
	// awsmgotask.Sync(string(factory.AWSFarlight))

	// s := wrap.Unwrap(awsmgotask.NewSyncer(string(factory.AWSPlat2)))
	// wrap.DieWithError(s.Clean(**********))

	resp, err := restclient.ListAll[models.MySQL](
		// fields.FactoryField(10),
		fields.FactoryAccountField(36),
		fields.LessThan("update_at", **********),
	)
	if err != nil {
		panic(err)
	}

	fmt.Println("Results:", resp.TotalPages, resp.Count)

	for _, r := range resp.Results {
		fmt.Println(r.<PERSON>(), r.<PERSON>)
	}
}
