package main

import (
	"gitlab.lilithgame.com/yunwei/pkg/wrap"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/factory"
	awsslb "gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/task/aws/slb"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/task/aws/slb/elb"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/task/aws/slb/elb2"
)

func main() {
	if err := awsslb.SyncAll(); err != nil {
		panic(err)
	}
}

func elbv1Sync() {
	s := wrap.Must(elb.NewSyncer(factory.AWSPlat2.String()))
	if err := s.Sync(); err != nil {
		panic(err)
	}
}

func elbv2Sync() {
	s := wrap.Must(elb2.NewSyncer(factory.AWSPlat2.String()))
	if err := s.Sync(); err != nil {
		panic(err)
	}
}
