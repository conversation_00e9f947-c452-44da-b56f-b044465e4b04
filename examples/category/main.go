package main

import (
	"fmt"
	"regexp"

	"github.com/fatih/color"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/category"
)

func main() {
	key := category.CategoryKey("gke-dgameremake-global-p-default-pool-864c7e2d-wf5f", "")
	fmt.Println(key)
}

func main3() {
	r := regexp.MustCompile(`^(?:[tc][a-z]{2})_(?<product>[a-zA-Z]+)(_[\w]+)*$`)
	s := "tca_rok_proxy_4bj_002"

	match := r.FindStringSubmatch(s)
	if match != nil {
		for idx, val := range match {
			fmt.Printf(" %d: %s\n", idx, val)
		}

		if productIndex := r.SubexpIndex("product"); productIndex != -1 {
			fmt.Printf("product: %s\n", match[productIndex])
		}
	}
}

func main2() {
	// r := regexp.MustCompile(`^tsh-xgame-(?:official)-\d{4}$`)
	// r := regexp.MustCompile(`^tsh-(?<product>xgame)-(?:official)-\d{4}$`)
	// r := regexp.MustCompile(`^tsh-(?<product>xgame)-(global)-\d{4}$`)
	r := regexp.MustCompile(
		`^(?P<Product>[a-zA-Z]+?)` +
			`(\.(?P<Service>[a-zA-Z0-9]+))?` +
			`(\.(?P<Module>[a-zA-Z0-9]+))*` +
			// `(-(?P<Pub>[a-zA-Z]+))?` +
			`(-(?P<Pub>(global|asia|cn|tw|us|eu)?))?` +
			// `(-(?P<Env>[a-zA-Z]+))?` +
			`(-(?P<Env>(rc|ptr|pre|prod|test|dev|staging|gray|dev|qa|cbt|obt|yace|audit)?))?` +
			// `(-(?P<Type>[a-zA-Z]+))?` +
			`(-(?P<Type>(ecs|machine|k8s|lb|phy|db|pg|mgo|mongo|vpc|eip|sg|redis)))?` +
			`(-(?P<Vendor>[tvagquc][a-z]{2,3}))?` +
			`(-(?P<Suffix>.*))?$`)

	printMatch(r, "tsh-xgame-official-0001")
	printMatch(r, "tsh-xgame-global-0001")
}

func printMatch(r *regexp.Regexp, s string) {
	matches := r.FindStringSubmatch(s)

	fmt.Println(s)
	for i, m := range matches {
		fmt.Printf("  %d => %s\n", i, m)
	}
}

func mainx() {
	names := []string{
		"dgame.gamesvr.2003-cn-prod-ecs-ubj",
		"dgameremake.jumper-tw-prod-ecs-ctw",
		"dgame.gamesvr-cn-prod-ecs-ubj-6719",
		"igame-staging-ptr-***********",
		"mona-cn-audit-ecs-tsh",
	}

	for _, name := range names {
		if r := category.MatchResourceName(name); r != nil {
			color.Green("%s", name)
			fmt.Printf("  Product: %-20s\n", r.Product)
			fmt.Printf("  Service: %-20s\n", r.Service)
			fmt.Printf("  Module: %-20s\n", r.Module)
			fmt.Printf("  Pub: %-20s\n", r.Pub)
			fmt.Printf("  Env: %-20s\n", r.Env)
			fmt.Printf("  Vendor: %-20s\n", r.Vendor)
			fmt.Printf("  Type: %-20s\n", r.Type)
			fmt.Printf("  Suffix: %-20s\n", r.Suffix)
		} else {
			color.Red("%s miss matched", name)
		}
	}
}
