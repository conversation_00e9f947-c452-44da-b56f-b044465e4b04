package main

import (
	"time"

	"gitlab.lilithgame.com/yunwei/pkg/logger"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
)

func listEmptyMachines() error {
	conds := []fields.Field{
		fields.FactoryField(1),
		fields.IsNull(fields.FactoryAccountFieldKey),
		fields.Not(fields.ExternalStatusFieldKey, fields.ExternalStatusDeleted.Value),
	}

	now := time.Now().Unix()
	dayAgo := time.Unix(now, 0).Add(-time.Hour * 4).Unix()
	conds = append(conds, fields.NamedField("updated_before", dayAgo))

	var err error
	resp, err := restclient.ListN[models.Machine](100, conds...)
	if err != nil {
		return err
	}

	logger.Info("find empty account machine result to delete",
		"count", resp.Count,
		"updated_before", dayAgo,
	)

	for _, m := range resp.Results {
		perr := restclient.Patch(&m, fields.NewFields(fields.ExternalStatusDeleted))
		if perr != nil {
			logger.Error("failed to patch machine", "error", perr)
		} else {
			logger.Debug("patch machine success", "machine", m.ExternalName)
		}
	}
	return nil
}

func main() {
	// t := time.Now().Unix()

	// ago := time.Unix(t, 0).Add(-time.Hour * 4).Unix()
	// fmt.Println(ago)

	listEmptyMachines()
}
