package main

import (
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/factory"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/task/ali/ack"
)

func main() {
	if err := ack.SyncMany(
		// factory.AliyunRok,
		// factory.AliyunFarlightCN,
		// factory.AliyunFarlightGlobal,
		// factory.AliyunLilith,
		// factory.AliyunIGameOfficial,
		// factory.AliyunSamo,
		// factory.AliyunXGame,
		// factory.AliyunIGame,
		// factory.AliyunDgame,
		// factory.AliyunWgame,
		// factory.AliyunWgame2,
		// factory.AliyunMona,
		// factory.AliyunPlatGlobal,
		// factory.AliyunFarlightPlat,
		factory.AliyunSatanpit,
	); err != nil {
		panic(err)
	}
}
