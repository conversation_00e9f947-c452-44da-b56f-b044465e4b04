package main

import (
	"gitlab.lilithgame.com/yunwei/pkg/wrap"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/factory"
	alihbasetask "gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/task/ali/hbase"
)

func main() {
	s := alihbasetask.NewBatchSyncerWithFactoryKeys(
		// factory.AliyunLilith,
		// factory.AliyunDgame,
		// factory.AliyunXGame,

		// 403
		factory.AliyunIGame,

		// factory.AliyunMona,
		// factory.AliyunWgame,
		// factory.AliyunDevops,
		// factory.AliyunFarlightCN,
		// factory.AliyunFarlightGlobal,
	)
	wrap.DieWithError(s.Sync())
}
