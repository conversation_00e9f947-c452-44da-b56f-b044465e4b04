package main

import (
	"fmt"

	"github.com/fatih/color"
	"gitlab.lilithgame.com/yunwei/pkg/wrap"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/factory"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/task/ali/sg"
)

func main() {
	accounts := []factory.FactoryKeyType{
		// factory.AliyunSamo,
		// factory.AliyunSamoCN,
		// factory.AliyunMona,
		// factory.AliyunDgame,
		// factory.AliyunWgame,
		// factory.AliyunWgame2,
		// factory.AliyunDevops,
		// factory.AliyunXGame,
		// factory.AliyunIGame,
		// factory.AliyunFarlightCN,
		factory.AliyunFarlightGlobal,
		// factory.AliyunSecurity,
	}

	for _, account := range accounts {
		color.Green("start sync %s", account.String())
		// wrap.DieWithError(sg.Sync(account.String()))

		s := wrap.Must(sg.NewSyncer(account.String()))
		for _, info := range s.Regions() {
			fmt.Printf("%s => %+v\n", info.Name, info)
		}
	}
}
