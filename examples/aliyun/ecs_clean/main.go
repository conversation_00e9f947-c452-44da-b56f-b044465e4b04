package main

import (
	"fmt"
	"time"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
)

func main() {
	conds := []fields.Field{
		fields.FactoryField(1),
		fields.IsNull(fields.FactoryAccountFieldKey),
		fields.Not(fields.ExternalStatusFieldKey, fields.ExternalStatusDeleted.Value),
	}
	dayAgo := time.Now().Add(-time.Hour * 24).Unix()
	conds = append(conds, fields.NamedField("updated_before", dayAgo))

	for {
		resp, err := restclient.ListN[models.Machine](10, conds...)
		if err != nil {
			panic(err)
		}

		fmt.Printf("found %d machines\n", resp.Count)
		if resp.Count == 0 {
			break
		}

		for _, m := range resp.Results {
			if derr := restclient.Patch(&m, fields.NewFields(fields.ExternalStatusDeleted)); derr != nil {
				panic(derr)
			} else {
				fmt.Printf("deleted %s\n", m.ExternalHostname)
			}
		}
	}
}
