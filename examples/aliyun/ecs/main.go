package main

import (
	"github.com/fatih/color"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/factory"
	aliecs "gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/task/ali/ecs"
)

func main2() {
	accounts := []factory.FactoryKeyType{
		factory.AliyunDgame,
		// factory.AliyunLilith,
		// factory.AliyunRok,
		// factory.AliyunXGame,
		// factory.AliyunSamo,
		// factory.AliyunSamoCN,
		// factory.AliyunMona,
		// factory.AliyunIGame,
		// factory.AliyunDevops,
		// factory.AliyunAvatar,
		// factory.AliyunWaibao,
		// factory.AliyunPGame,
		// factory.AliyunPtslg,
		// factory.AliyunW3,
		// factory.AliyunIGameOfficial,
		// factory.AliyunFarlightPlat,
		// factory.AliyunSatanpit,
	}

	for _, account := range accounts {
		color.Green("start syncing %s", account.String())
		syncAccount(account)
	}
}

func syncAccount(account factory.FactoryKeyType) {
	s, err := aliecs.NewSyncer(account)
	if err != nil {
		panic(err)
	}

	// t := time.Now().Unix()
	if err = s.SyncAll(); err != nil {
		color.Red("sync failed, error %v", err)
		// } else {
		// 	s.Clean(t)
	}
}

func main() {
	main2()
}
