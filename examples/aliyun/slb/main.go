package main

import (
	"gitlab.lilithgame.com/yunwei/pkg/wrap"

	// "gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/task/ali/slb/clb"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/factory"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/task/ali/slb/clb"
)

func main() {
	// s := wrap.Must(alb.NewSyncer(factory.AliyunLilith.String()))
	// wrap.DieWithError(s.Sync())

	// s := wrap.Must(clb.NewSyncer(factory.AliyunLilith.String()))
	// wrap.DieWithError(s.Sync())

	accounts := []string{
		// factory.FactoryAliyunLilith.String(),
		// factory.AliyunLilith.String(),

		// factory.AliyunDevops.String(),
		// factory.AliyunSamo.String(),
		// factory.AliyunSamoCN.String(),
		// factory.AliyunMona.String(),
		factory.AliyunDgame.String(),
		// factory.AliyunWgame.String(),
		// factory.AliyunWgame2.String(),
		// factory.AliyunDevops.String(),
		// factory.AliyunXGame.String(),
		// factory.AliyunIGame.String(),
		// factory.AliyunFarlightCN.String(),
		// factory.AliyunFarlightGlobal.String(),
		// factory.AliyunSecurity.String(),
		// factory.AliyunAvatar.String(),
	}

	for _, account := range accounts {
		s := wrap.Must(clb.NewSyncer(account))
		// s := wrap.Must(nlb.NewSyncer(account))
		s.Sync()
		// s.SyncWithRegion("us-east-1")

		// s2 := wrap.Must(alb.NewSyncer(account))
		// s2.Sync()
	}
}
