package main

import (
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/factory"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/task/ali/eip"
)

func main() {
	if err := eip.SyncMany(factory.AliyunLilith.String()); err != nil {
		panic(err)
	}

	// var factories = []string{
	// 	factory.AliyunLilith.String(),
	// 	factory.AliyunSamo.String(),
	// 	factory.AliyunMona.String(),
	// 	factory.AliyunDgame.String(),
	// 	factory.AliyunWgame.String(),
	// 	factory.AliyunWgame2.String(),
	// 	factory.AliyunDevops.String(),
	// 	factory.AliyunXGameOfficial.String(),
	// 	factory.AliyunIGame.String(),
	// 	factory.AliyunFarlightCN.String(),
	// 	factory.AliyunFarlightGlobal.String(),
	// }

	// for _, f := range factories {
	// 	if _, err := alieipsync.CreateCredentialWithAccount(f); err != nil {
	// 		fmt.Printf("CreateCredentialWithAccount %s failed: %v\n", f, err)
	// 	}
	// }
}
