package main

import (
	"fmt"
	"os"
	"time"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/factory"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/job"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/task/ali/disk"
)

func main() {
	main2()
}

func main2() {
	job.AliyunBlockDiskSync(factory.AliyunLilith)
}

func mainx() {
	factories := []string{
		factory.AliyunLilith.String(),
	}

	ss := make([]*disk.Syncer, 0)
	for _, key := range factories {
		if s, err := disk.NewSyncer(key); err == nil {
			ss = append(ss, s)
		}
	}

	for _, s := range ss {
		t := time.Now().Unix()
		// if err := s.Sync(); err != nil {
		if err := s.SyncWithRegions("cn-qingdao", "us-east-1"); err != nil {
			panic(err)
		}
		fmt.Printf("factory account id :%d\n", s.AccountID())
		if err := disk.Clean(t,
			fields.FactoryAccountField(s.AccountID()),
			fields.In("region", "cn-qingdao", "us-east-1"),
		); err != nil {
			fmt.Fprintf(os.Stderr, "failed to clean up: %v\n", err)
		}
	}
}
