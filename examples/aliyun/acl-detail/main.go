package main

import (
	"fmt"

	slbv4 "github.com/alibabacloud-go/slb-20140515/v4/client"
	"gitlab.lilithgame.com/yunwei/pkg/wrap"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	alitask "gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/task/ali"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/task/ali/slb/clb"
)

func main() {
	const region = "cn-shanghai"

	cred, err := alitask.CreateFactoryCredential("aliyun-lilith")
	if err != nil {
		panic(err)
	}
	config := cred.ClientConfig

	cli := wrap.Must(clb.CreateCLBClient(config, region))

	input := &slbv4.DescribeAccessControlListAttributeRequest{
		AclId:    fields.Pointer("acl-uf6wbklk3dtp64cm2dvct"),
		RegionId: fields.Pointer(region),
		PageSize: fields.Int32(10),
	}

	resp, err := cli.DescribeAccessControlListAttribute(input)
	if err != nil {
		panic(err)
	}

	for _, ln := range resp.Body.RelatedListeners.RelatedListener {
		fmt.Printf("releated ln is %+v\n", ln)
	}
}
