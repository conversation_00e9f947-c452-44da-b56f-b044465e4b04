package main

import (
	"fmt"

	slbv4 "github.com/alibabacloud-go/slb-20140515/v4/client"
	"gitlab.lilithgame.com/yunwei/pkg/wrap"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	alitask "gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/task/ali"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/task/ali/slb/clb"
)

func main() {
	const region = "cn-shanghai"

	cred, err := alitask.CreateFactoryCredential("aliyun-lilith")
	if err != nil {
		panic(err)
	}
	config := cred.ClientConfig

	cli := wrap.Must(clb.CreateCLBClient(config, region))

	input := &slbv4.DescribeLoadBalancerListenersRequest{
		RegionId:       fields.Pointer(region),
		LoadBalancerId: []*string{fields.Pointer("lb-uf636a5lsqjwp7nd79qxz")},
	}

	resp, derr := cli.DescribeLoadBalancerListeners(input)
	if derr != nil {
		panic(derr)
	}

	for _, ln := range resp.Body.Listeners {
		fmt.Printf("%+v\n", ln)
	}
}
