package main

import (
	"gitlab.lilithgame.com/yunwei/pkg/wrap"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/factory"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/task/ali/mgo"
)

func main() {
	s := mgo.NewBatchSyncerWithFactoryKeys(
		// factory.AliyunLilith,
		// factory.AliyunSatanpit,
		// factory.AliyunSamo,
		factory.AliyunMona,
		// factory.AliyunIGame,
		// factory.AliyunWgame2,
		// factory.AliyunWgame,
		// factory.AliyunDgame,
		// factory.AliyunXGameOfficial,
		// factory.AliyunFarlightCN,
		// factory.AliyunFarlightGlobal,
		// factory.AliyunSecurity,
	)
	wrap.DieWithError(s.Sync())
	// wrap.DieWithError(s.SyncInRegion("cn-heyuan"))
}
