package main

import (
	"log"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/factory"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/task/volc/vecs"
)

func main() {
	// wrap.DieWithError(vecs.SyncAll(
	err := vecs.SyncAll(
		// factory.VolcPlat,
		// factory.VolcPlat2,
		// factory.VolcWgame,
		// factory.VolcFarlightCN,
		// factory.VolcMona,
		factory.VolcQA,
	)
	if err != nil {
		log.Fatalf("sync error: %v", err)
	}
}
