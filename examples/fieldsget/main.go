package main

import (
	"fmt"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
)

func main() {
	fs := []fields.Field{
		fields.StringField("factory", "aliyun"),
	}
	fmt.Printf("fs origin: %+v\n", fs)

	addFile(fs...)

	fmt.Printf("fs after: %q\n", fs)
}

func addFile(conds ...fields.Field) {
	conds = append(conds, fields.NamedField("test", "foo"))
	fmt.Println(len(conds))
}
