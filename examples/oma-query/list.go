package main

var goodlist = []string{
	"10.137.37.145",
	"10.137.23.29",
	"10.137.23.50",
	"10.137.37.79",
	"10.137.23.131",
	"10.137.23.3",
	"10.137.23.110",
	"10.137.23.18",
	"10.137.37.2",
	"10.137.23.4",
	"10.137.23.55",
	"10.137.37.232",
	"10.137.23.132",
	"10.137.37.233",
	"10.137.23.36",
	"10.137.23.42",
	"10.137.23.97",
	"10.137.23.40",
	"10.137.23.26",
	"10.137.23.46",
	"10.137.23.95",
	"10.137.23.211",
	"10.137.23.90",
	"10.137.37.190",
	"10.137.23.208",
	"10.137.23.51",
	"10.137.23.230",
	"10.137.23.193",
	"10.137.23.71",
	"10.137.36.202",
	"10.137.23.32",
	"10.137.23.79",
	"10.137.23.23",
	"10.137.23.89",
	"10.137.37.141",
	"10.137.23.102",
	"10.137.23.76",
	"10.137.23.66",
	"10.137.23.7",
	"10.137.23.72",
	"10.137.23.24",
	"10.137.23.45",
	"10.137.37.26",
	"10.137.37.174",
	"10.137.23.117",
	"10.137.23.205",
	"10.137.23.126",
	"10.137.23.103",
	"10.137.23.204",
	"10.137.23.70",
	"10.137.23.37",
	"10.137.23.17",
	"10.137.23.30",
	"10.137.23.38",
	"10.137.23.73",
	"10.137.23.99",
	"10.137.23.41",
	"10.137.37.1",
	"10.137.23.44",
	"10.137.23.116",
	"10.137.37.117",
	"10.137.37.181",
	"10.137.23.22",
	"10.137.23.251",
	"10.137.23.98",
	"10.137.23.53",
	"10.137.23.92",
	"10.137.23.19",
	"10.137.37.213",
	"10.137.37.119",
	"10.137.23.28",
	"10.137.38.17",
	"10.137.23.68",
	"10.137.36.208",
	"10.137.23.56",
	"10.137.37.136",
	"10.137.38.44",
	"10.137.23.91",
	"10.137.23.43",
	"10.137.23.25",
	"10.137.23.2",
	"10.137.23.94",
	"10.137.23.75",
	"10.137.23.62",
	"10.137.23.93",
	"10.137.23.1",
	"10.137.23.101",
	"10.137.23.61",
	"10.137.36.183",
	"10.137.37.179",
	"10.137.38.28",
	"10.137.23.100",
	"10.137.23.20",
	"10.137.38.31",
	"10.137.23.67",
	"10.137.37.106",
	"10.137.38.18",
	"10.137.23.119",
	"10.137.37.199",
	"10.137.23.35",
	"10.137.37.84",
	"10.137.37.87",
	"10.137.23.57",
	"10.137.23.77",
	"10.137.37.194",
	"10.137.23.16",
	"10.137.37.36",
	"10.137.37.148",
	"10.137.23.9",
	"10.137.37.108",
}

var goodMap = make(map[string]int, 0)

func init() {
	for _, item := range goodlist {
		goodMap[item] = 1
	}
}
