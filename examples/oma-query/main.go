package main

import (
	"fmt"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
)

func main() {
	// resp := wrap.Unwrap(restclient.Query(string(models.ResourceSLB),
	// 	fields.NamedField("external_uuid", "lb-0xig50locadan448mr6c1"),
	// 	fields.LimitField(1),
	// ))

	// fmt.Printf("response is %s\n", string(resp))

	conds := []fields.Field{
		fields.FactoryField(9),
		fields.FactoryAccountField(38),
		fields.Not(fields.ExternalStatusFieldKey, fields.ExternalStatusDeleted.Value),
		fields.NumberField("updated_before", **********),
	}
	resp, _ := restclient.List[models.Machine](conds...)

	fmt.Printf("conds: %+v\n", conds)

	fmt.Printf("total %d machines\n", resp.Count)

	for _, i := range resp.Results {
		fmt.Println(i.ExternalName, i.ExternalStatus, i.AgentVersion)
	}
}
