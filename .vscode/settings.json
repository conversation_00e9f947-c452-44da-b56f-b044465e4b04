{
  "github.copilot.chat.commitMessageGeneration.instructions": [
    {
        "file": ".github/git-commit.md"
    }
  ],
  "github.copilot.chat.codeGeneration.instructions": [
      {
          "file": ".github/codegen.md"
      }
  ],
  "github.copilot.chat.codesearch.enabled": true,
  "go.lintTool": "golangci-lint",
  "go.lintFlags": [
    "--fast"
  ],
  "yaml.schemas": {
    "https://golangci-lint.run/jsonschema/golangci.v1.jsonschema.json": ".golangci.yml"
  },
  "[go]": {
    "editor.codeActionsOnSave": {
      "source.organizeImports": "always"
    }
  },
  "protoc": {
    "options": [
        "--proto_path=${workspaceFolder}/proto",
        "--proto_path=${workspaceFolder}/proto/options",
    ]
  }
}
