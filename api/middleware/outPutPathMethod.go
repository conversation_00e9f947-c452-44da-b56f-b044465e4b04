package middleware

import (
	"bytes"
	"io"
	"time"

	"github.com/gin-gonic/gin"

	log "gitlab.lilithgame.com/yunwei/pkg/logger"
)

func OutPutPathMethod(c *gin.Context) {
	body, _ := io.ReadAll(c.Request.Body)

	c.Request.Body = io.NopCloser(bytes.NewBuffer(body))

	start := time.Now()
	c.Next()

	if cost := time.Since(start); cost > time.Second {
		log.Warn("OutPutPathMethod cost too much time",
			"FullPath", c.<PERSON>ath(),
			"Request.Method", c.Request.Method,
			"Request.Body", string(body),
			"cost", cost.Seconds(),
		)
	}
}
