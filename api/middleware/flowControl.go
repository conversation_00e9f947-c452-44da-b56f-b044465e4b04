package middleware

import (
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"golang.org/x/sync/semaphore"
)

const DefaultCooldown = time.Second * 5

type FlowController struct {
	sem      *semaphore.Weighted
	cooldown time.Duration
}

func NewFlowController(n int64) *FlowController {
	return NewFlowControllerWithCooldown(n, DefaultCooldown)
}

// NewFlowControllerWithCooldown 创建一个流量控制器, n 为最大并发数, c 为冷却时间
func NewFlowControllerWithCooldown(n int64, c time.Duration) *FlowController {
	return &FlowController{
		sem:      semaphore.NewWeighted(n),
		cooldown: c,
	}
}

func (fc *FlowController) Middleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		if !fc.sem.TryAcquire(1) { // 尝试获取信号量
			c.AbortWithStatusJSON(http.StatusTooManyRequests, gin.H{"message": "too hot, slow down"})
			return
		}

		// 释放信号量, 缓几秒钟, 防止大规模访问
		defer time.AfterFunc(fc.cooldown, func() {
			fc.sem.Release(1)
		})

		c.Next()
	}
}
