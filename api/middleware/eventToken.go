package middleware

import (
	"net/http"

	"github.com/gin-gonic/gin"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/config"
)

func ValidEventToken(c *gin.Context) {
	token := c.GetHeader(config.Config().EventAuth.TokenName)
	if token == "" {
		c.AbortWithStatus(http.StatusForbidden)
		return
	}

	valid := false
	for _, validToken := range config.Config().EventAuth.TokenValues {
		if token == validToken {
			valid = true
			break
		}
	}

	if !valid {
		c.AbortWithStatus(http.StatusUnauthorized)
		return
	}

	c.Next()
}
