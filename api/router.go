package api

import (
	"fmt"
	"time"

	"github.com/gin-gonic/gin"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/api/handlers"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/api/middleware"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/config"
)

func Serve(addrs ...string) {
	gin.SetMode(gin.ReleaseMode)
	gin.DisableConsoleColor()

	r := gin.Default()
	r.Use(middleware.OutPutPathMethod)

	r.GET("/", handlers.IndexHandlerFunc(gin.H{
		"start_time": time.Now().Format(time.RFC3339),
	}))

	apiGroup := r.Group("/api")
	{
		eventsGroup := apiGroup.Group("/oma-agent-events")
		eventsGroup.Use(middleware.ValidEventToken)
		{
			coolerGroup := eventsGroup.Group("")
			cooler := middleware.NewFlowControllerWithCooldown(500, config.Config().EventRequestCooldown).Middleware()
			coolerGroup.Use(cooler)
			{
				coolerGroup.POST("", handlers.HandleAgentPostEvents)
				coolerGroup.PUT("", handlers.HandleAgentPutEvents)
			}

			eventsGroup.DELETE("", handlers.HandleAgentDeleteEvents)
		}
	}

	fmt.Printf("starting listening at %v\n", addrs)
	r.Run(addrs...)
}
