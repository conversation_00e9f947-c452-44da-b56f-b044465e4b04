package v2

import (
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"
	"gitlab.lilithgame.com/yunwei/cloudmeta"

	beauty "gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/beauty/aws"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
)

// HandleAgentPostEventsFromAWS handle event from a aws ec2
func HandleAgentPostEventsFromAWS(c *gin.Context, meta cloudmeta.AWSMeta, f ...fields.Field) {
	var status string
	if c.Request.Method == http.MethodDelete {
		status = "STOPPED"
	} else {
		status = "RUNNING"
	}

	additions := append(f, fields.ExternalStatusField(status))

	err := syncAWSEC2(meta, additions...)
	if err != nil {
		c.AbortWithError(http.StatusInternalServerError, fmt.Errorf("handle aws event failed: %w", err))
		return
	}

	c.Status(http.StatusOK)
}

func syncAWSEC2(v cloudmeta.AWSMeta, additions ...fields.Field) error {
	var m models.Machine

	has, err := restclient.Find(&m,
		fields.ExternalUUIDField(v.InstanceID),
	)
	if err != nil {
		return err
	}

	attrs := fields.NewFields(additions...)
	attrs.Update(beauty.BeautyHostFromMeta(&v))

	if has {
		if m.ExternalTags != "" {
			attrs.Remove(fields.ExternalTagsFieldKey) // remove virtual tag in case of overriding
		}
		_, err = restclient.PatchByID[models.Machine](m.GetID(), attrs)
		return err
	}

	_, err = restclient.Post[models.Machine](attrs)
	return err
}
