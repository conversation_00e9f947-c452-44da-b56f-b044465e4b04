package v2

import (
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"
	"gitlab.lilithgame.com/yunwei/cloudmeta"
	"gitlab.lilithgame.com/yunwei/pkg/logger"

	beauty "gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/beauty/tencent"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
)

// HandleAgentPostEventsFromTencent handle event from tencent cvm
func HandleAgentPostEventsFromTencent(c *gin.Context, meta cloudmeta.TencentMeta, f ...fields.Field) {
	var status string
	if c.Request.Method == http.MethodDelete {
		status = "STOPPED"
	} else {
		status = "RUNNING"
	}

	additions := append(f, fields.ExternalStatusField(status))

	err := syncTencentCVM(meta, additions...)
	if err != nil {
		c.AbortWithError(http.StatusInternalServerError, fmt.Errorf("handle tencent cvm event failed: %w", err))
		return
	}

	c.Status(http.StatusOK)
}

func syncTencentCVM(v cloudmeta.TencentMeta, additions ...fields.Field) error {
	var m models.Machine

	has, err := restclient.Find(&m,
		fields.ExternalUUIDField(v.InstanceID),
	)
	if err != nil {
		return err
	}

	attrs := fields.NewFields(additions...)
	attrs.Update(beauty.BeautyHostFromMeta(&v))

	if has {
		if m.ExternalTags != "" {
			attrs.Remove(fields.ExternalTagsFieldKey) // remove virtual tag in case of overriding
		}
		// _, err = restclient.Patch[models.Machine](m.ID, attrs)
		err = restclient.Patch(&m, attrs)
	} else {
		_, err = restclient.Post[models.Machine](attrs)
	}

	logger.Info("update tencent cvm finished", "error", err, "instance", attrs.GetString(fields.ExternalNameFieldKey))

	return err
}
