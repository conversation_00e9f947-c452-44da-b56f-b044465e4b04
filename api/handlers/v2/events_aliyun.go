package v2

import (
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"
	"gitlab.lilithgame.com/yunwei/cloudmeta"
	"gitlab.lilithgame.com/yunwei/pkg/logger"

	beauty "gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/beauty/aliyun"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
)

// HandleAgentPostEventsFromAliyun handle event from a aliyun ecs
func HandleAgentPostEventsFromAliyun(c *gin.Context, meta cloudmeta.AliyunMeta, f ...fields.Field) {
	var status string
	if c.Request.Method == http.MethodDelete {
		status = "STOPPED"
	} else {
		status = "RUNNING"
	}

	additions := append(f, fields.ExternalStatusField(status))

	err := syncAliyunECS(meta, additions...)
	if err != nil {
		c.AbortWithError(http.StatusInternalServerError, fmt.Errorf("handle aliyun event failed: %w", err))
		return
	}

	c.Status(http.StatusOK)
}

func syncAliyunECS(v cloudmeta.AliyunMeta, additions ...fields.Field) error {
	var m models.Machine

	has, err := restclient.Find(&m,
		fields.ExternalUUIDField(v.InstanceID),
	)
	if err != nil {
		return err
	}

	attrs := fields.NewFields(additions...)
	attrs.Update(beauty.BeautyHostFromMeta(&v))

	if has {
		if m.ExternalTags != "" {
			attrs.Remove(fields.ExternalTagsFieldKey) // remove virtual tag in case of overriding
		}

		// _, err = restclient.Patch[models.Machine](m.ID, attrs)
		restclient.Patch(&m, attrs)
	} else {
		_, err = restclient.Post[models.Machine](attrs)
	}

	logger.Debug("sync aliyun ecs", "attrs", attrs, "err", err)

	return err
}
