package v2

import (
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"
	"gitlab.lilithgame.com/yunwei/cloudmeta"

	beauty "gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/beauty/gcp"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
)

// HandleAgentPostEventsFromGCP handle event from a gcp instance
func HandleAgentPostEventsFromGCP(c *gin.Context, meta cloudmeta.GCPMeta, f ...fields.Field) {
	status := fields.ExternalStatusRunning
	if c.Request.Method == http.MethodDelete {
		status = fields.ExternalStatusStopped
	}

	additions := append(f, status)

	err := syncGCE(&meta, additions...)
	if err != nil {
		c.AbortWithError(http.StatusInternalServerError, fmt.Errorf("handle gcp event failed: %w", err))
		return
	}

	c.Status(http.StatusOK)
}

func syncGCE(v *cloudmeta.GCPMeta, additions ...fields.Field) error {
	var m models.Machine

	has, err := restclient.Find(&m,
		fields.ExternalUUIDField(v.InstanceID),
	)
	if err != nil {
		return err
	}

	attrs := fields.NewFields(additions...)
	attrs.Update(beauty.BeautyHostFromMeta(v))

	if has {
		if m.ExternalTags != "" {
			attrs.Remove(fields.ExternalTagsFieldKey) // remove virtual tag in case of overriding
		}
		// _, err = restclient.Patch[models.Machine](m.ID, attrs)
		err = restclient.Patch(&m, attrs)
	} else {
		_, err = restclient.Post[models.Machine](attrs)
	}

	return err
}
