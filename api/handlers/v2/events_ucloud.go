package v2

import (
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"
	"gitlab.lilithgame.com/yunwei/cloudmeta"
	"gitlab.lilithgame.com/yunwei/pkg/logger"

	beauty "gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/beauty/uhost"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
)

// HandleAgentPostEventsFromUcloud handle event from a ucloud host
func HandleAgentPostEventsFromUcloud(c *gin.Context, meta cloudmeta.UcloudMeta, f ...fields.Field) {
	var status string
	if c.Request.Method == http.MethodDelete {
		status = "STOPPED"
	} else {
		status = "RUNNING"
	}

	additions := append(f, fields.ExternalStatusField(status))

	logger.Debug("handle ucloud event", "meta", meta, "status", status, "additions", additions)

	err := syncUhost(meta, additions...)
	if err != nil {
		logger.Error("handle ucloud event failed", "error", err)
		c.AbortWithError(http.StatusInternalServerError, fmt.Errorf("handle ucloud event failed: %w", err))
		return
	}

	c.Status(http.StatusOK)
}

func syncUhost(v cloudmeta.UcloudMeta, additions ...fields.Field) error {
	var m models.Machine

	has, err := restclient.Find(&m,
		fields.ExternalUUIDField(v.InstanceID),
	)
	if err != nil {
		return err
	}

	attrs := fields.NewFields(additions...)
	attrs.Update(beauty.BeautyUhostFromMeta(&v))

	if has {
		if m.ExternalTags != "" {
			attrs.Remove(fields.ExternalTagsFieldKey) // remove virtual tag in case of overriding
		}
		// _, err = restclient.Patch[models.Machine](m.ID, attrs)
		err = restclient.Patch(&m, attrs)
	} else {
		_, err = restclient.Post[models.Machine](attrs)
	}

	return err
}
