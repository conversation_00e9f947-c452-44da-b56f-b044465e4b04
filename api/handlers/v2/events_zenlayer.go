package v2

import (
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"
	"gitlab.lilithgame.com/yunwei/cloudmeta"

	zenlayerBeauty "gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/beauty/zenlayer"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/category"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
)

func HandleAgentPostEventsFromZenlayer(c *gin.Context, meta cloudmeta.ZenlayerMeta, fs ...fields.Field) {
	var status string
	if c.Request.Method == http.MethodDelete {
		status = "STOPPED"
	} else {
		status = "RUNNING"
	}

	additions := append(fs, fields.ExternalStatusField(status))
	err := syncZenlayerMachine(meta, additions...)
	if err != nil {
		c.AbortWithError(http.StatusInternalServerError, fmt.Errorf("handle zenlayer event failed: %w", err))
		return
	}

	c.Status(http.StatusOK)
}

func syncZenlayerMachine(meta cloudmeta.ZenlayerMeta, additions ...fields.Field) error {
	var m models.Machine

	has, err := restclient.Find(&m,
		fields.ExternalUUIDField(meta.InstanceID),
	)
	if err != nil {
		return err
	}

	attrs := fields.NewFields(additions...)
	attrs.Update(zenlayerBeauty.BeautyHostFromMeta(&meta))

	// Set product by hostname
	if hostname := attrs.GetString(fields.ExternalHostNameFieldKey); hostname != "" {
		if productId := category.Category(hostname, ""); productId != nil {
			attrs.With(fields.ProductField(*productId))
		}
	}

	if has {
		if m.ExternalTags != "" {
			attrs.Remove(fields.ExternalTagsFieldKey) // remove virtual tag in case of overriding
		}
		// _, err = restclient.Patch[models.Machine](m.ID, attrs)
		err = restclient.Patch(&m, attrs)
		return err
	}

	_, err = restclient.Post[models.Machine](attrs)
	return err
}
