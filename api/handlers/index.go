package handlers

import (
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"gitlab.lilithgame.com/yunwei/pkg/logger"
)

func IndexHandler(c *gin.Context) {
	logger.Info("index handler")
	c.<PERSON>(http.StatusOK, gin.H{
		"msg":  "OK",
		"time": time.Now().Format("2006-01-02 15:04:05"),
	})
}

func IndexHandlerFunc(metadata map[string]any) gin.HandlerFunc {
	playload := gin.H{
		"msg": "OK",
	}

	for k, v := range metadata {
		playload[k] = v
	}

	return func(c *gin.Context) {
		c.<PERSON>(http.StatusOK, playload)
	}
}
