package handlers

import (
	"encoding/json"
	"errors"
	"fmt"
	"net/http"

	cloudevents "github.com/cloudevents/sdk-go/v2"
	"gitlab.lilithgame.com/yunwei/cloudmeta"
	"gitlab.lilithgame.com/yunwei/pkg/metrics"
)

// CompleteVendorMeta complete v to a valid cloudmeta.
//
// Because of serializing will cause type lost,
// we have to guess and rebuild it, ugly but must.
func CompleteVendorMeta(v any) (data any, err error) {
	bs, _ := json.Marshal(v)

	var basemeta cloudmeta.CloudMeta
	if err := json.Unmarshal(bs, &basemeta); err != nil {
		return nil, err
	}

	// trust VendorName, or else things will get complicated
	switch vendor := basemeta.VendorName; cloudmeta.VendorName(vendor) {
	case cloudmeta.AliyunVendor:
		{
			var meta cloudmeta.AliyunMeta
			if err := json.Unmarshal(bs, &meta); err != nil {
				return nil, err
			}
			return meta, nil
		}

	case cloudmeta.GCPVendor:
		{
			var meta cloudmeta.GCPMeta
			if err := json.Unmarshal(bs, &meta); err != nil {
				return nil, err
			}
			return meta, nil
		}

	case cloudmeta.AWSVendor:
		{
			var meta cloudmeta.AWSMeta
			if err := json.Unmarshal(bs, &meta); err != nil {
				return nil, err
			}
			return meta, nil
		}

	case cloudmeta.UcloudVendor:
		{
			var meta cloudmeta.UcloudMeta
			if err := json.Unmarshal(bs, &meta); err != nil {
				return nil, err
			}
			return meta, nil
		}

	case cloudmeta.VolcVendor:
		{
			var meta cloudmeta.VolcMeta
			if err := json.Unmarshal(bs, &meta); err != nil {
				return nil, err
			}
			return meta, nil
		}

	case cloudmeta.CapitalOnlineVendor:
		{
			var meta cloudmeta.CapitalOnlineMeta
			if err := json.Unmarshal(bs, &meta); err != nil {
				return nil, err
			}
			return meta, nil
		}

	case cloudmeta.ZenlayerVendor:
		{
			var meta cloudmeta.ZenlayerMeta
			if err := json.Unmarshal(bs, &meta); err != nil {
				return nil, err
			}
			return meta, nil
		}

	case "":
		return nil, errors.New("not a cloud meta")

	default:
		return nil, fmt.Errorf("asset-sync does not support cloud provider %s", vendor)
	}
}

// TODO: maybe rewrite in generic way
// func GuessCloudType[TS *cloudmeta.AliyunMeta | *cloudmeta.AWSMeta](v any) (data TS, err error) {
// 	bs, _ := json.Marshal(v)

// 	var basemeta cloudmeta.CloudMeta
// 	if err := json.Unmarshal(bs, &basemeta); err != nil {
// 		return nil, err
// 	}

// 	switch vendor := basemeta.VendorName; vendor {
// 	case string(cloudmeta.AliyunVendor):
// 		{
// 			var meta cloudmeta.AliyunMeta
// 			if err := json.Unmarshal(bs, &meta); err != nil {
// 				return nil, err
// 			}
// 			return &meta, nil
// 		}

// 	}
// }

func EventData(r *http.Request) (*metrics.AgentHeartBeat, error) {
	event, err := cloudevents.NewEventFromHTTPRequest(r)
	if err != nil {
		return nil, errors.Join(err, errors.New("invalid cloudevent format"))
	}

	var requestData metrics.AgentHeartBeat
	if err = event.DataAs(&requestData); err != nil {
		return nil, fmt.Errorf("invalid agent heartbeat format, %v", err)
	}

	if requestData.CloudMetadata == nil {
		return nil, errors.New("non-cloud instance not supported")
	}

	meta, err := CompleteVendorMeta(requestData.CloudMetadata)
	if err != nil {
		return nil, err
	}

	// cloudmeta with type
	requestData.CloudMetadata = meta
	return &requestData, nil
}
