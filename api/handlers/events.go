package handlers

import (
	"encoding/json"
	"fmt"
	"net"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"gitlab.lilithgame.com/yunwei/cloudmeta"
	"gitlab.lilithgame.com/yunwei/pkg/metrics"

	apiv2 "gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/api/handlers/v2"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
)

func HandleAgentPostEvents(c *gin.Context) {
	hb, err := EventData(c.Request)
	if err != nil {
		c.String(http.StatusBadRequest, err.Error())
		return
	}

	agentVersion := &hb.Version
	if c.Request.Method == http.MethodDelete {
		agentVersion = nil
	}

	additions := []fields.Field{
		fields.AgentVersionField(agentVersion),
		fields.CPUField(hb.CPUInfo.Count),
		fields.MemoryField(hb.MemoryInfo.SizeGB),
		fields.ExternalHostNameField(hb.SystemInfo.Hostname),
	}

	dist := strings.ToLower(hb.SystemInfo.Dist)
	if dist == "alpine" {
		// virtual tag for the first time
		if c.Request.Method == http.MethodPost {
			virtualTag := []map[string]string{
				{
					// 为了让监控系统保持跟 ack.* 兼容的处理, 所以也以 ack 开头
					"k": "ack.oma-agent",
					"v": "ds",
				},
			}

			if bs, err := json.Marshal(virtualTag); err == nil {
				additions = append(additions, fields.ExternalTagsField(string(bs)))
			}
		}
	} else {
		// 虚拟机安装的方式才有必要记载 agent_id, 以便于执行时选择
		additions = append(additions, fields.StringField("agent_id", hb.AgentID))
	}

	if dist != "alpine" && dist != "" {
		additions = append(additions,
			fields.OSNameField(fmt.Sprintf("%s %s", dist, hb.SystemInfo.DistVersion)),
		)
	}

	switch v := hb.CloudMetadata.(type) {
	case cloudmeta.AliyunMeta:
		apiv2.HandleAgentPostEventsFromAliyun(c, v, additions...)
		return

	case cloudmeta.AWSMeta:
		apiv2.HandleAgentPostEventsFromAWS(c, v, additions...)
		return

	case cloudmeta.GCPMeta:
		apiv2.HandleAgentPostEventsFromGCP(c, v, additions...)
		return

	case cloudmeta.UcloudMeta:
		apiv2.HandleAgentPostEventsFromUcloud(c, v, additions...)
		return

	case cloudmeta.VolcMeta:
		apiv2.HandleAgentPostEventsFromVolc(c, v, additions...)
		return

	case cloudmeta.CapitalOnlineMeta:
		additions = append(additions, fields.ExternalNameField(hb.SystemInfo.Hostname))

		if privateIP, publicIP := resolveIPAddressFromHeartbeat(hb); privateIP != "" || publicIP != "" {
			additions = append(additions,
				fields.PrivateIPField(privateIP),
				fields.PublicIPField(publicIP),
			)
		}

		apiv2.HandleAgentPostEventsFromCaptical(c, v, additions...)
		return

	case cloudmeta.ZenlayerMeta:
		additions = append(additions, fields.ExternalNameField(hb.SystemInfo.Hostname))

		if privateIP, publicIP := resolveIPAddressFromHeartbeat(hb); privateIP != "" || publicIP != "" {
			additions = append(additions,
				fields.PrivateIPField(privateIP),
				fields.PublicIPField(publicIP),
			)
		}

		apiv2.HandleAgentPostEventsFromZenlayer(c, v, additions...)
		return
	}

	c.Status(http.StatusNotImplemented)
}

func resolveIPAddressFromHeartbeat(hb *metrics.AgentHeartBeat) (string, string) {
	var privateIP string
	var publicIP string

	for _, iface := range hb.Iface {
		for _, ipaddr := range iface.IPAddrs {
			if privateIP != "" && publicIP != "" {
				goto finish
			}

			if addr, err := net.ResolveIPAddr("ip", ipaddr.IP); err == nil {
				if addr.IP.IsPrivate() {
					if privateIP == "" {
						privateIP = ipaddr.IP
					}
				} else {
					if publicIP == "" {
						publicIP = ipaddr.IP
					}
				}
			}
		}
	}
finish:
	return privateIP, publicIP
}

func HandleAgentPutEvents(c *gin.Context) {
	// Do the same thing with POST
	HandleAgentPostEvents(c)
}

func HandleAgentDeleteEvents(c *gin.Context) {
	// Do the same thing with POST
	HandleAgentPostEvents(c)
}
