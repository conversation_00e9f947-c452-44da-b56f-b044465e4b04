#!/bin/bash

# 脚本用于从 sync_vpc.txt 文件中提取错误条目
# 使用方法: ./extract_errors.sh [输入文件] [输出文件]

INPUT_FILE="${1:-sync_vpc.txt}"
OUTPUT_FILE="${2:-sync_vpc_errors.txt}"
STATS_FILE="${OUTPUT_FILE%.txt}_stats.txt"

# 检查输入文件是否存在
if [ ! -f "$INPUT_FILE" ]; then
    echo "❌ 错误: 找不到文件 $INPUT_FILE"
    exit 1
fi

echo "🔍 开始提取错误条目..."
echo "📁 输入文件: $INPUT_FILE"
echo "📄 输出文件: $OUTPUT_FILE"
echo "📊 统计文件: $STATS_FILE"
echo "$(printf '%.0s-' {1..50})"

# 创建输出文件头部
cat > "$OUTPUT_FILE" << EOF
# 错误条目提取报告
# 提取时间: $(date '+%Y-%m-%d %H:%M:%S')
# 源文件: $INPUT_FILE
# 总行数: $(wc -l < "$INPUT_FILE")

$(printf '%.0s=' {1..80})
详细错误条目:
$(printf '%.0s=' {1..80})

EOF

# 定义错误模式
ERROR_PATTERNS=(
    "ERROR"
    "error"
    "Error"
    "ERRO"
    "failed"
    "Failed"
    "FAILED"
    "exception"
    "Exception"
    "EXCEPTION"
    "panic"
    "Panic"
    "PANIC"
    "fatal"
    "Fatal"
    "FATAL"
    "timeout"
    "Timeout"
    "TIMEOUT"
    '"level":"ERROR"'
    '"level":"error"'
    '"error":'
    "sync.*failed"
    "Failed to.*sync"
    "connection.*refused"
    "Connection.*refused"
    "network.*error"
    "Network.*error"
    "api.*error"
    "API.*error"
    "http.*error"
    "HTTP.*error"
    "status.*[45][0-9][0-9]"
)

# 计数器
TOTAL_ERRORS=0
declare -A ERROR_COUNTS

# 提取错误条目
echo "🔍 正在扫描错误条目..."

while IFS= read -r line; do
    line_num=$((line_num + 1))
    
    # 检查每个错误模式
    for pattern in "${ERROR_PATTERNS[@]}"; do
        if echo "$line" | grep -qiE "$pattern"; then
            TOTAL_ERRORS=$((TOTAL_ERRORS + 1))
            ERROR_COUNTS["$pattern"]=$((ERROR_COUNTS["$pattern"] + 1))
            
            # 写入错误条目
            echo "[$TOTAL_ERRORS] 行号: $line_num" >> "$OUTPUT_FILE"
            echo "    模式: $pattern" >> "$OUTPUT_FILE"
            echo "    内容: $line" >> "$OUTPUT_FILE"
            echo "$(printf '%.0s-' {1..80})" >> "$OUTPUT_FILE"
            break  # 避免重复匹配
        fi
    done
done < "$INPUT_FILE"

# 创建统计报告
cat > "$STATS_FILE" << EOF
# 错误统计报告
# 生成时间: $(date '+%Y-%m-%d %H:%M:%S')
# 源文件: $INPUT_FILE

$(printf '%.0s=' {1..80})
总体统计:
$(printf '%.0s=' {1..80})
总行数: $(wc -l < "$INPUT_FILE")
错误条目数: $TOTAL_ERRORS
错误率: $(awk "BEGIN {printf \"%.2f\", $TOTAL_ERRORS/$(wc -l < "$INPUT_FILE")*100}")%

$(printf '%.0s=' {1..80})
错误模式统计:
$(printf '%.0s=' {1..80})
EOF

# 输出错误模式统计（按出现次数排序）
for pattern in "${!ERROR_COUNTS[@]}"; do
    echo "$pattern: ${ERROR_COUNTS[$pattern]} 次"
done | sort -k2 -nr >> "$STATS_FILE"

# 添加摘要到输出文件开头
sed -i.bak "4a\\
# 错误条目数: $TOTAL_ERRORS\\
# 错误率: $(awk "BEGIN {printf \"%.2f\", $TOTAL_ERRORS/$(wc -l < "$INPUT_FILE")*100}")%\\
" "$OUTPUT_FILE"

# 清理备份文件
rm -f "${OUTPUT_FILE}.bak"

echo "✅ 成功提取 $TOTAL_ERRORS 个错误条目到 $OUTPUT_FILE"
echo "📊 统计报告已保存到 $STATS_FILE"
echo "📈 错误率: $(awk "BEGIN {printf \"%.2f\", $TOTAL_ERRORS/$(wc -l < "$INPUT_FILE")*100}")%"

# 显示前5个最常见的错误模式
echo ""
echo "🔝 最常见的错误模式 (前5个):"
for pattern in "${!ERROR_COUNTS[@]}"; do
    echo "$pattern: ${ERROR_COUNTS[$pattern]} 次"
done | sort -k2 -nr | head -5

echo ""
echo "🎉 处理完成!"
echo "💡 提示: 查看 $OUTPUT_FILE 了解详细错误信息"
echo "💡 提示: 查看 $STATS_FILE 了解统计信息"
