# 错误条目提取工具

这些脚本用于从 `sync_vpc.txt` 日志文件中提取和分析错误条目。

## 文件说明

- `extract_errors.py` - Python 版本的错误提取脚本（功能更丰富）
- `extract_errors.sh` - Shell 版本的错误提取脚本（更轻量）
- `README_error_extraction.md` - 使用说明文档

## 使用方法

### Python 脚本 (推荐)

```bash
# 使用默认文件名
python3 extract_errors.py

# 或者直接运行
./extract_errors.py
```

**输出文件:**
- `sync_vpc_errors.txt` - 详细的错误条目文本报告
- `sync_vpc_errors.json` - JSON 格式的结构化报告

### Shell 脚本

```bash
# 使用默认文件名
./extract_errors.sh

# 指定输入和输出文件
./extract_errors.sh input.txt output.txt
```

**输出文件:**
- `sync_vpc_errors.txt` - 详细的错误条目
- `sync_vpc_errors_stats.txt` - 统计信息

## 功能特性

### 🔍 错误模式识别

脚本能识别以下类型的错误：

- **日志级别错误**: ERROR, error, ERRO
- **操作失败**: failed, Failed, FAILED
- **异常情况**: exception, Exception, panic
- **致命错误**: fatal, Fatal, FATAL
- **超时错误**: timeout, Timeout
- **JSON 格式错误**: `"level":"ERROR"`, `"error":`
- **同步相关错误**: sync failed, Failed to sync
- **网络错误**: connection refused, network error
- **API 错误**: api error, http error
- **HTTP 状态码错误**: 4xx, 5xx

### 📊 统计信息

- 总行数统计
- 错误条目数量
- 错误率计算
- 各种错误模式的出现频次
- 时间戳提取（如果有）

### 📋 报告格式

**文本报告示例:**
```
# 错误条目提取报告
# 提取时间: 2024-01-15 10:30:00
# 源文件: sync_vpc.txt
# 总行数: 1000
# 错误条目数: 25
# 错误率: 2.50%

================================================================================
错误统计:
================================================================================
ERROR.*: 15 次
failed.*: 8 次
timeout.*: 2 次

================================================================================
详细错误条目:
================================================================================

[1] 行号: 45
    时间: 2024-01-15T10:25:30
    模式: ERROR.*
    内容: ERROR Failed to sync VPC vpc-12345: connection timeout
--------------------------------------------------------------------------------
```

**JSON 报告示例:**
```json
{
  "metadata": {
    "source_file": "sync_vpc.txt",
    "extraction_time": "2024-01-15T10:30:00",
    "total_lines": 1000,
    "error_count": 25,
    "error_rate": 2.5
  },
  "errors": [
    {
      "line_number": 45,
      "content": "ERROR Failed to sync VPC vpc-12345: connection timeout",
      "pattern_matched": "ERROR.*",
      "timestamp": "2024-01-15T10:25:30",
      "severity": "ERROR"
    }
  ]
}
```

## 使用场景

1. **问题排查**: 快速定位日志中的错误信息
2. **质量分析**: 统计同步过程的错误率
3. **模式分析**: 了解最常见的错误类型
4. **监控报告**: 生成错误摘要报告

## 自定义配置

### 添加新的错误模式

**Python 脚本中修改:**
```python
error_patterns = [
    r'ERROR.*',
    r'your_custom_pattern.*',  # 添加你的模式
    # ... 其他模式
]
```

**Shell 脚本中修改:**
```bash
ERROR_PATTERNS=(
    "ERROR"
    "your_custom_pattern"  # 添加你的模式
    # ... 其他模式
)
```

### 修改输出格式

可以根据需要修改脚本中的输出格式部分。

## 注意事项

1. 确保 `sync_vpc.txt` 文件存在于当前目录
2. Python 脚本需要 Python 3.6+ 版本
3. Shell 脚本需要 bash 环境
4. 大文件处理可能需要一些时间
5. 输出文件会覆盖同名的现有文件

## 故障排除

**文件不存在错误:**
```bash
❌ 错误: 找不到文件 sync_vpc.txt
```
解决方案: 确保文件存在或指定正确的文件路径

**权限错误:**
```bash
chmod +x extract_errors.py extract_errors.sh
```

**编码错误:**
脚本使用 UTF-8 编码，如果遇到编码问题，请检查输入文件的编码格式。
