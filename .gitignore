# Created by https://www.toptal.com/developers/gitignore/api/go
# Edit at https://www.toptal.com/developers/gitignore?templates=go

### Go ###
# If you prefer the allow list template instead of the deny list, see community template:
# https://github.com/github/gitignore/blob/main/community/Golang/Go.AllowList.gitignore
#
# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib
*.csv
*.txt
!requirements.txt

.DS_Store
.env
.vscode/*
!.vscode/settings.json

bin/
main
asset-syncer
targets/

update-instance

tools/oma-sys/target/

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Log files
*.log
tempFile/

# Dependency directories (remove the comment below to include it)
# vendor/
!vendor/modules.txt

# Go workspace file
go.work

# excludes config file
conf.*
internal/**/*.ini

__debug_*

# End of https://www.toptal.com/developers/gitignore/api/go


dist/
