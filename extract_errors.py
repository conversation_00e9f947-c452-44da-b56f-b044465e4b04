#!/usr/bin/env python3
"""
脚本用于从 sync_vpc.txt 文件中提取错误条目
支持多种错误模式的识别和分类
"""

import re
import json
from datetime import datetime
from collections import defaultdict

def extract_errors_from_log(input_file, output_file):
    """
    从日志文件中提取错误条目
    """
    error_patterns = [
        # 常见的错误模式
        r'ERROR.*',
        r'ERRO.*',
        r'error.*',
        r'Error.*',
        r'failed.*',
        r'Failed.*',
        r'FAILED.*',
        r'exception.*',
        r'Exception.*',
        r'EXCEPTION.*',
        r'panic.*',
        r'Panic.*',
        r'PANIC.*',
        r'fatal.*',
        r'Fatal.*',
        r'FATAL.*',
        # JSON 格式的错误
        r'.*"level":"ERROR".*',
        r'.*"level":"error".*',
        r'.*"error":.*',
        # 特定的同步错误
        r'.*sync.*failed.*',
        r'.*Failed to.*sync.*',
        r'.*timeout.*',
        r'.*Timeout.*',
        r'.*connection.*refused.*',
        r'.*Connection.*refused.*',
        # 网络相关错误
        r'.*network.*error.*',
        r'.*Network.*error.*',
        r'.*dns.*error.*',
        r'.*DNS.*error.*',
        # API 相关错误
        r'.*api.*error.*',
        r'.*API.*error.*',
        r'.*http.*error.*',
        r'.*HTTP.*error.*',
        r'.*status.*[45]\d\d.*',
    ]
    
    errors = []
    error_stats = defaultdict(int)
    line_number = 0
    
    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            for line in f:
                line_number += 1
                line = line.strip()
                
                if not line:
                    continue
                
                # 检查是否匹配任何错误模式
                for pattern in error_patterns:
                    if re.search(pattern, line, re.IGNORECASE):
                        error_entry = {
                            'line_number': line_number,
                            'content': line,
                            'pattern_matched': pattern,
                            'timestamp': extract_timestamp(line)
                        }
                        errors.append(error_entry)
                        error_stats[pattern] += 1
                        break  # 避免重复匹配
    
    except FileNotFoundError:
        print(f"错误: 找不到文件 {input_file}")
        return
    except Exception as e:
        print(f"读取文件时发生错误: {e}")
        return
    
    # 写入错误条目到输出文件
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(f"# 错误条目提取报告\n")
            f.write(f"# 提取时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"# 源文件: {input_file}\n")
            f.write(f"# 总行数: {line_number}\n")
            f.write(f"# 错误条目数: {len(errors)}\n")
            f.write(f"# 错误率: {len(errors)/line_number*100:.2f}%\n")
            f.write("\n" + "="*80 + "\n")
            f.write("错误统计:\n")
            f.write("="*80 + "\n")
            
            for pattern, count in sorted(error_stats.items(), key=lambda x: x[1], reverse=True):
                f.write(f"{pattern}: {count} 次\n")
            
            f.write("\n" + "="*80 + "\n")
            f.write("详细错误条目:\n")
            f.write("="*80 + "\n\n")
            
            for i, error in enumerate(errors, 1):
                f.write(f"[{i}] 行号: {error['line_number']}\n")
                if error['timestamp']:
                    f.write(f"    时间: {error['timestamp']}\n")
                f.write(f"    模式: {error['pattern_matched']}\n")
                f.write(f"    内容: {error['content']}\n")
                f.write("-" * 80 + "\n")
        
        print(f"✅ 成功提取 {len(errors)} 个错误条目到 {output_file}")
        print(f"📊 错误率: {len(errors)/line_number*100:.2f}%")
        
    except Exception as e:
        print(f"写入文件时发生错误: {e}")

def extract_timestamp(line):
    """
    从日志行中提取时间戳
    """
    timestamp_patterns = [
        r'\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}',  # ISO format
        r'\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}',  # Standard format
        r'\d{2}/\d{2}/\d{4} \d{2}:\d{2}:\d{2}',  # US format
        r'"time":"([^"]+)"',                      # JSON format
    ]
    
    for pattern in timestamp_patterns:
        match = re.search(pattern, line)
        if match:
            return match.group(1) if match.groups() else match.group(0)
    
    return None

def create_json_report(input_file, json_output_file):
    """
    创建 JSON 格式的错误报告
    """
    error_patterns = [
        r'ERROR.*', r'error.*', r'failed.*', r'Failed.*',
        r'.*"level":"ERROR".*', r'.*"error":.*'
    ]
    
    errors = []
    line_number = 0
    
    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            for line in f:
                line_number += 1
                line = line.strip()
                
                if not line:
                    continue
                
                for pattern in error_patterns:
                    if re.search(pattern, line, re.IGNORECASE):
                        error_entry = {
                            'line_number': line_number,
                            'content': line,
                            'pattern_matched': pattern,
                            'timestamp': extract_timestamp(line),
                            'severity': classify_severity(line)
                        }
                        errors.append(error_entry)
                        break
        
        report = {
            'metadata': {
                'source_file': input_file,
                'extraction_time': datetime.now().isoformat(),
                'total_lines': line_number,
                'error_count': len(errors),
                'error_rate': len(errors)/line_number*100 if line_number > 0 else 0
            },
            'errors': errors
        }
        
        with open(json_output_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print(f"✅ JSON 报告已保存到 {json_output_file}")
        
    except Exception as e:
        print(f"创建 JSON 报告时发生错误: {e}")

def classify_severity(line):
    """
    根据内容分类错误严重程度
    """
    line_lower = line.lower()
    
    if any(word in line_lower for word in ['fatal', 'panic', 'critical']):
        return 'CRITICAL'
    elif any(word in line_lower for word in ['error', 'failed', 'exception']):
        return 'ERROR'
    elif any(word in line_lower for word in ['warn', 'warning']):
        return 'WARNING'
    else:
        return 'INFO'

if __name__ == "__main__":
    input_file = "sync_vpc.txt"
    output_file = "sync_vpc_errors.txt"
    json_output_file = "sync_vpc_errors.json"
    
    print("🔍 开始提取错误条目...")
    print(f"📁 输入文件: {input_file}")
    print(f"📄 输出文件: {output_file}")
    print(f"📋 JSON 报告: {json_output_file}")
    print("-" * 50)
    
    # 提取错误到文本文件
    extract_errors_from_log(input_file, output_file)
    
    # 创建 JSON 报告
    create_json_report(input_file, json_output_file)
    
    print("\n🎉 处理完成!")
