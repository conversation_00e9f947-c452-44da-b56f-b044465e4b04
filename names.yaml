transforms:
  - name: xgame-global
    decompose: ^(?P<Vendor>[tvaguc][a-z]{2})-xgamem(-global)?(-\w+)$
    result: xgame-global

  - name: xgame
    decompose: ^(?P<Vendor>[tvaguc][a-z]{2})-xgamem-official(-\w+)$
    result: xgame-official

  - version: "2.0"
    match:
      - op: eq
        key: Product
        value: igame
      - op: startsWith
        key: Suffix
        value: "ptr-"
    matchOp: AND
    result: igame-ptr

  - version: "2.0"
    match:
      - op: eq
        key: Product
        value: igame
      - op: eq
        key: Env
        value: test
    result: igame-test

  - name: dota
    version: "2.0"
    match:
      - op: eq
        key: Product
        value: dgame
      - op: eq
        key: Pub
        value: cn
    result: dota

  - name: dgameremake-asia
    version: "2.0"
    match:
      - op: eq
        key: Product
        value: dgameremake
      - op: eq
        key: Pub
        value: asia
    result: dgameremake-asia
