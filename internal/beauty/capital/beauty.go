package capital

import (
	"gitlab.lilithgame.com/yunwei/cloudmeta"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/cache"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
)

func BeautyHostFromMeta(v *cloudmeta.CapitalOnlineMeta) fields.Fields {
	attrs := fields.NewFields(
		fields.ExternalUUIDField(v.InstanceID),
		// fields.ExternalHostNameField(v.Hostname),
		// // fields.ExternalNameField(v.Hostname),
		// fields.ExternalFlavorField(v.InstanceType),
		// fields.PrivateIPField(v.PrivateIP),
		// fields.PublicIPField(v.PublicIP),
		// fields.CreateTimeField(v.CreatedAt),
		// fields.ExpiredTimeField(v.ExpiredAt),
	)

	if createTime := v.CreateTime; createTime != "" {
		attrs.SetField(fields.CreateTimeField(createTime))
	}

	if f := cache.GetFactory(v.VendorName); f != nil {
		attrs.SetField(fields.FactoryField(f.GetID()))

		if zone := cache.GetFactoryZone(f.GetID(), v.Region); zone != nil {
			attrs.SetField(fields.ZoneField(zone.GetID()))
		}
	}

	return attrs
}
