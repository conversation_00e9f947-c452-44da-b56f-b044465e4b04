package gcp

import (
	"path/filepath"

	"cloud.google.com/go/compute/apiv1/computepb"
	"gitlab.lilithgame.com/yunwei/cloudmeta"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/category"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/cache"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
)

func BeautyHostFromMeta(m *cloudmeta.GCPMeta) fields.Fields {
	attrs := fields.NewFields(
		fields.ExternalUUIDField(m.InstanceID),
		fields.ExternalNameField(m.InstanceName),
		// fields.ExternalHostNameField(m.Hostname),
		fields.ExternalFlavorField(m.InstanceType),
		fields.PrivateIPField(m.PrivateIP),
		fields.PublicIPField(m.ExternalIP),
		fields.ImageIDField(m.ImageID),
		fields.GCPProjectIDField(m.ProjectID),
	)

	if productID := category.Category(
		attrs.GetString(fields.ExternalNameFieldKey),
		attrs.GetString(fields.GCPProjectIDFieldKey),
	); productID != nil {
		attrs.SetField(fields.ProductField(*productID))
	}

	// TODO: hard code fix
	if f := cache.GetFactory("gcp"); f != nil {
		attrs.SetField(fields.FactoryField(f.GetID()))

		if zone := cache.GetFactoryZone(f.GetID(), m.Zone); zone != nil {
			attrs.SetField(fields.ZoneField(zone.GetID()))
		}
	}

	return attrs
}

func Beauty(i *computepb.Instance, project string) fields.Fields {
	attrs := fields.NewFields(
		fields.ExternalUUIDField(fields.IntString(i.GetId())),
		fields.ExternalStatusField(i.GetStatus()),
		fields.ExternalNameField(i.GetName()),
		fields.ExternalFlavorField(filepath.Base(i.GetMachineType())),
		fields.CreateTimeField(i.GetCreationTimestamp()),
		fields.DescField(i.GetDescription()),
	)

	if hostname := i.GetHostname(); hostname != "" {
		attrs.SetString(fields.ExternalHostNameFieldKey, &hostname)
	}

	// IPAddress
	iface0 := i.NetworkInterfaces[0]
	attrs.SetField(fields.PrivateIPField(iface0.GetNetworkIP()))
	if len(iface0.AccessConfigs) > 0 {
		attrs.SetField(fields.PublicIPField(iface0.AccessConfigs[0].GetNatIP()))
	} else {
		attrs.SetField(fields.PublicIPField(""))
	}

	// Tags
	tags := make([]map[string]string, 0)
	for _, v := range i.Tags.Items {
		tags = append(tags, map[string]string{
			"k": v,
			"v": v,
		})
	}
	attrs.SetJSON(fields.ExternalTagsFieldKey, tags)

	// Factory & Zone
	if f := cache.GetFactory("gcp"); f != nil {
		attrs.Set(fields.FactoryFieldKey, f.GetID())

		zoneName := filepath.Base(i.GetZone())
		if zone := cache.GetFactoryZone(f.GetID(), zoneName); zone != nil {
			attrs.Set(fields.ZoneFieldKey, zone.GetID())
		}
	}

	// Product
	if productID := category.Category(i.GetName(), project); productID != nil {
		fields.SetValue(attrs, fields.ProductFieldKey, productID)
		// } else {
		// find similar machine in the same project which has product specified
		// var similarMachine models.Machine
		// if has, err := restclient.Find(&similarMachine,
		// 	fields.GCPProjectIDField(project),
		// 	fields.NotNull("product__id"),
		// ); err == nil && has {
		// 	fields.SetValue(attrs, fields.ProductFieldKey, similarMachine.Product)
		// }
		// fields.SetNull(attrs, fields.ProductFieldKey)
	}
	return attrs
}
