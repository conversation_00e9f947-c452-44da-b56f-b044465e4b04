package uhost

import (
	"strconv"

	"gitlab.lilithgame.com/yunwei/cloudmeta"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/category"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/cache"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
)

func BeautyUhostFromMeta(v *cloudmeta.UcloudMeta) fields.Fields {
	attrs := fields.NewFields(
		fields.ExternalNameField(v.InstanceName),
		fields.ExternalUUIDField(v.InstanceID),
		fields.OSNameField(v.OS),
		fields.ExternalFlavorField(v.InstanceType),
		fields.ImageIDField(v.ImageID),
		fields.PrivateIPField(v.PrivateIP),
		fields.PublicIPField(v.PublicIP),
	)

	if cpuCount, err := strconv.Atoi(v.CPU); err == nil {
		attrs.SetField(fields.CPUField(cpuCount))
	}

	if mem, err := strconv.Atoi(v.Memory); err == nil {
		attrs.SetField(fields.MemoryField(mem / 1024)) // GiB
	}

	// factory and zone
	if f := cache.GetFactory(v.VendorName); f != nil {
		attrs.SetField(fields.FactoryField(f.GetID()))

		if zone := cache.GetFactoryZone(f.GetID(), v.Zone); zone != nil {
			attrs.SetField(fields.ZoneField(zone.GetID()))
		}
	}

	// productId
	if productID := category.Category(v.InstanceName, ""); productID != nil {
		attrs.Set(fields.ProductFieldKey, fields.Value(productID))
	}

	return attrs
}
