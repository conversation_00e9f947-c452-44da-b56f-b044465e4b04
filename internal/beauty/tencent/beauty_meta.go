package tencent

import (
	"gitlab.lilithgame.com/yunwei/cloudmeta"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/category"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/cache"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
)

// BeautyHost return a `fields.Fields`
func BeautyHostFromMeta(v *cloudmeta.TencentMeta) fields.Fields {
	attrs := fields.NewFields(
		fields.ExternalUUIDField(v.InstanceID),
		fields.ExternalNameField(v.InstanceName),
		fields.OSNameField(v.ImageID),
		fields.ExternalFlavorField(v.InstanceType),
		fields.PrivateIPField(v.PrivateIP),
		fields.PublicIPField(v.PublicIP),
		fields.CreateTimeField(v.CreatedTime),
	)

	if f := cache.GetFactory(v.VendorName); f != nil {
		attrs.SetField(fields.FactoryField(f.GetID()))

		if zone := cache.GetFactoryZone(f.GetID(), v.Zone); zone != nil {
			attrs.SetField(fields.ZoneField(zone.GetID()))
		}
	}

	if productID := category.Category(v.InstanceName, ""); productID != nil {
		attrs.SetField(fields.ProductField(*productID))
	}

	return attrs
}
