package tencent

import (
	cvm "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/cvm/v20170312"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
)

func BeautyHost(i *cvm.Instance) fields.Fields {
	attrs := fields.NewFields(
		fields.ExternalUUIDField(*i.InstanceId),
		fields.ExternalNameField(*i.InstanceName),
		fields.ExternalStatusField(*i.InstanceState),
		fields.ExternalFlavorField(*i.InstanceType),
		fields.CPUField(*i.CPU),
		fields.MemoryField(*i.Memory),
		fields.OSNameField(*i.OsName),
		fields.CreateTimeField(*i.CreatedTime),
		fields.StringPField("charge_type", i.InstanceChargeType),
		fields.StringPField("expired_time", i.ExpiredTime),
	)

	// Private IP
	if privateIPs := i.PrivateIpAddresses; len(privateIPs) > 0 {
		attrs.SetField(fields.PrivateIPField(fields.Value(privateIPs[0])))
	}

	// Public IP
	if pubips := i.PublicIpAddresses; len(pubips) > 0 {
		attrs.SetField(fields.PublicIPField(fields.Value(pubips[0])))
	}

	return attrs
}
