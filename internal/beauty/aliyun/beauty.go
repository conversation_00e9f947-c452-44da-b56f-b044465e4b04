package aliyun

import (
	"strings"

	ecsv3client "github.com/alibabacloud-go/ecs-20140526/v3/client"
	"gitlab.lilithgame.com/yunwei/cloudmeta"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/category"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/cache"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
)

// BeautyHost return a `fields.Fields` object from a ecs instance
//
// Fields:
//   - external_uuid
//   - external_name
//   - external_hostname
//   - external_status
//   - external_flavor
//   - cpu: int32
//   - mem: int32, GiB
//   - os_name
//   - zone
//   - create_time
//   - expired_time
//   - desc
//   - private_ip
//   - public_ip
//   - external_tags
//   - security_groups
func BeautyHost(i *ecsv3client.DescribeInstancesResponseBodyInstancesInstance) fields.Fields {
	attrs := fields.NewFields(
		fields.ExternalUUIDField(*i.InstanceId),
		fields.ExternalNameField(*i.InstanceName),
		fields.ExternalHostNameField(*i.HostName),
		fields.ExternalStatusField(*i.Status),
		fields.ExternalFlavorField(*i.InstanceType),
		fields.CPUField(*i.Cpu),
		fields.OSNameField(*i.OSName),
		fields.CreateTimeField(*i.CreationTime),
		fields.DescField(*i.Description),
		fields.StringPField("charge_type", i.InstanceChargeType),
		fields.BoolNumberPField("recyclable", i.Recyclable),
		fields.StringPField("expired_time", i.ExpiredTime),
	)

	if mem := fields.PDividedNumber(i.Memory, 1024); mem > 0 {
		// if mem := fields.Value(instance.Memory); mem > 0 {
		// m := float64(mem) / 1024.0
		attrs.SetField(fields.MemoryField(mem))
	}

	// recycle
	var recycleReason string
	if locks := i.OperationLocks; locks != nil {
		for _, lockObj := range locks.LockReason {
			if strings.Contains(*lockObj.LockReason, "recycl") {
				recycleReason = *lockObj.LockReason
				break
			}
		}
	}
	attrs.SetField(fields.StringField("recycle", recycleReason))

	if z := BeautyZone(*i.ZoneId); z != nil {
		attrs.SetField(fields.ZoneField(z.GetID()))
	}

	if ifaces := i.NetworkInterfaces; ifaces != nil {
		if nics := ifaces.NetworkInterface; len(nics) > 0 {
			attrs.SetString(fields.PrivateIPFieldKey, nics[0].PrimaryIpAddress)
		}
	}

	if pubAddrs := i.PublicIpAddress; pubAddrs != nil {
		if pubIps := pubAddrs.IpAddress; len(pubIps) > 0 {
			publicIP := fields.Value(pubIps[0])
			attrs.SetField(fields.PublicIPField(publicIP))
		} else {
			attrs.Set(fields.PublicIPFieldKey, nil)
		}
	} else {
		attrs.Set(fields.PublicIPFieldKey, nil)
	}

	// security groups
	if sgs := i.SecurityGroupIds; sgs != nil {
		sgIds := make([]string, 0)
		for _, sg := range sgs.SecurityGroupId {
			sgIds = append(sgIds, *sg)
		}
		if len(sgIds) > 0 {
			attrs.Set("security_groups", strings.Join(sgIds, ","))
		}
	} else {
		attrs.Set("security_groups", nil)
	}

	// tags
	emrClusterId := ""
	if tags := i.Tags; tags != nil && len(tags.Tag) > 0 {
		tagList := make([]map[string]string, 0)
		for _, tag := range tags.Tag {
			tagKey := fields.Value(tag.TagKey)
			tagValue := fields.Value(tag.TagValue)

			tagList = append(tagList, map[string]string{
				"k": tagKey,
				"v": tagValue,
			})

			// aliyun k8s cluster
			if tagKey == "ack.aliyun.com" {
				attrs.SetField(fields.ExternalACKClusterIDField(tagValue))
			}

			if tagKey == "acs:emr:clusterId" {
				emrClusterId = tagValue
			}
		}

		attrs.SetJSON(fields.ExternalTagsFieldKey, tagList)
	}

	// Product (instance name / emr cluster name)
	if productID := category.Category(*i.InstanceName, ""); productID != nil {
		attrs.SetField(fields.ProductField(*productID))
	} else if emrClusterId != "" {
		if emrClusterName := getEMRClusterName(emrClusterId); emrClusterName != "" {
			if productID = category.Category(emrClusterName, ""); productID != nil {
				attrs.SetField(fields.ProductField(*productID))
			}
		}
	} else {
		attrs.SetOrNil(fields.ProductFieldKey, nil)
	}

	if expiredTime := *i.ExpiredTime; expiredTime != "" {
		attrs.SetField(fields.ExpiredTimeField(expiredTime))
	}

	return attrs
}

func getEMRClusterName(clusterId string) string {
	var m models.EMRCluster

	has, err := restclient.Find(&m, fields.StringField("cluster_id", clusterId))
	if err != nil || !has {
		return ""
	}

	return m.Name
}

func BeautyZone(zone string) *models.Zone {
	f := cache.GetFactoryByName("阿里云")
	if f == nil {
		return nil
	}

	if z := cache.GetFactoryZone(f.GetID(), zone); z != nil {
		return z
	}

	return nil
}

func BeautyHostFromMeta(v *cloudmeta.AliyunMeta) fields.Fields {
	attrs := fields.NewFields(
		fields.ExternalUUIDField(v.InstanceID),
		fields.ExternalHostNameField(v.Hostname),
		fields.ExternalFlavorField(v.InstanceType),
		fields.PrivateIPField(v.PrivateIP),
		fields.OSField(v.ImageID),
	)

	// compatible with old version
	if instanceName := v.InstanceName; instanceName != "" {
		attrs.With(fields.ExternalNameField(instanceName))
	}

	if publicIP := v.PublicIP; publicIP != "" {
		attrs.With(fields.PublicIPField(publicIP))
		// } else {
		// 	attrs.Set(fields.PublicIPFieldKey, nil)
	}

	// FIXME: should use 'aliyun' instead of 'aliyun-lilith'
	if f := cache.GetFactory("aliyun-lilith"); f != nil {
		attrs.SetField(fields.FactoryField(f.GetID()))

		if zone := cache.GetFactoryZone(f.GetID(), v.Zone); zone != nil {
			attrs.SetField(fields.ZoneField(zone.GetID()))
		}

		// TODO: load flavor from database or cache
	}

	if productID := category.Category(v.InstanceName, ""); productID != nil {
		attrs.SetField(fields.ProductField(*productID))
	}

	return attrs
}
