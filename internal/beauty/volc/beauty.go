package volc

import (
	"encoding/json"
	"strings"

	"github.com/volcengine/volcengine-go-sdk/service/ecs"
	"gitlab.lilithgame.com/yunwei/cloudmeta"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/category"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/cache"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
)

// BeautyHost return a `fields.Fields` object from a ecs instance
//
// Fields:
//   - external_uuid
//   - external_name
//   - external_hostname
//   - external_status
//   - external_flavor
//   - cpu: int32
//   - mem: int32, GiB
//   - os_name
//   - create_time
//   - expired_time
//   - desc
//   - zone
//   - private_ip
//   - public_ip
//   - external_tags
func BeautyHost(i *ecs.InstanceForDescribeInstancesOutput) fields.Fields {
	attrs := fields.NewFields(
		fields.StringPField(fields.ExternalUUIDFieldKey, i.InstanceId),
		fields.StringPField(fields.ExternalNameFieldKey, i.InstanceName),
		fields.ExternalStatusField(fields.PString(i.Status)),
		fields.StringPField(fields.ExternalFlavorFieldKey, i.InstanceTypeId),
		fields.NumberPField(fields.CPUFieldKey, i.Cpus),
		fields.NumberField(fields.MemoryFieldKey, fields.PDividedNumber(i.MemorySize, 1024)),
		fields.StringPField(fields.OSNameFieldKey, i.OsName),
		fields.StringPField(fields.CreateTimeFieldKey, i.CreatedAt),
		fields.StringPField(fields.ExpiredTimeFieldKey, i.ExpiredAt),
		fields.StringPField(fields.DescFieldKey, i.Description),
		fields.StringPField(fields.ChargeTypeFieldKey, i.InstanceChargeType),
		fields.StringPField("expired_time", i.ExpiredAt),
	)

	fields.SetAnyValue(attrs, fields.ExternalHostNameFieldKey, i.Hostname, i.HostName)

	if ifaces := i.NetworkInterfaces; len(ifaces) > 0 {
		for _, iface := range ifaces {
			if fields.PValueApply(iface.Type, strings.ToLower) == "primary" {
				attrs.SetString(fields.PrivateIPFieldKey, iface.PrimaryIpAddress)
				break
			}
		}
		// fmt.Printf("ifaces: %+v\n", ifaces)
	}

	attrs.Set(fields.PublicIPFieldKey, nil)

	// tags
	if tags := i.Tags; len(tags) > 0 {
		tagList := make([]map[string]string, 0)

		for _, tag := range tags {
			tagList = append(tagList, map[string]string{
				"k": fields.PString(tag.Key),
				"v": fields.PString(tag.Value),
			})
		}

		if bs, berr := json.Marshal(tagList); berr == nil {
			attrs.SetField(fields.ExternalTagsField(string(bs)))
		}
	}

	// ProductID
	if productID := category.Category(*i.InstanceName, ""); productID != nil {
		attrs.SetField(fields.ProductField(*productID))
	}

	return attrs
}

func BeautyHostFromMeta(v *cloudmeta.VolcMeta) fields.Fields {
	attrs := fields.NewFields(
		fields.ExternalUUIDField(v.InstanceID),
		fields.ExternalHostNameField(v.Hostname),
		fields.ExternalFlavorField(v.InstanceType),
		fields.PrivateIPField(v.PrivateIP),
		fields.PublicIPField(v.PublicIP),
		fields.CreateTimeField(v.CreatedAt),
	)

	if expiredAt := v.ExpiredAt; expiredAt != "" {
		attrs.SetField(fields.ExpiredTimeField(expiredAt))
	}

	if f := cache.GetFactory(v.VendorName); f != nil {
		attrs.SetField(fields.FactoryField(f.GetID()))

		if zone := cache.GetFactoryZone(f.GetID(), v.Zone); zone != nil {
			attrs.SetField(fields.ZoneField(zone.GetID()))
		}
	}

	// ProductID
	if productID := category.Category(v.Hostname, ""); productID != nil {
		attrs.SetField(fields.ProductField(*productID))
	}

	return attrs
}
