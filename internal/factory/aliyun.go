package factory

const (
	Aliyun FactoryKeyType = "aliyun"

	AliyunLilith         FactoryKeyType = <PERSON>yun + "-lilith"
	AliyunPlatGlobal     FactoryKeyType = <PERSON><PERSON> + "-platglobal" // 平台海外
	AliyunRok            FactoryKeyType = <PERSON><PERSON> + "-rok"
	AliyunSamo           FactoryKeyType = <PERSON><PERSON> + "-samo" // 北美
	AliyunSamoCN         FactoryKeyType = <PERSON>yun + "-samocn"
	AliyunMona           FactoryKeyType = Aliyun + "-mona"
	AliyunDgame          FactoryKeyType = <PERSON>yun + "-dgame"
	AliyunWgame          FactoryKeyType = <PERSON><PERSON> + "-wgame"
	AliyunWgame2         FactoryKeyType = <PERSON>yun + "-wgame2"
	AliyunW3             FactoryKeyType = <PERSON>yun + "-w3"
	AliyunDevops         FactoryKeyType = <PERSON><PERSON> + "-devops"
	AliyunXGame          FactoryKeyType = Aliyun + "-xgameofficial"
	AliyunIGame          FactoryKeyType = Aliyun + "-igame"
	AliyunIGameOfficial  FactoryKeyType = Aliyun + "-igameofficial"
	AliyunFarlightCN     FactoryKeyType = <PERSON><PERSON> + "-farlightcn"
	AliyunFarlightGlobal FactoryKeyType = <PERSON><PERSON> + "-farlightglobal"
	AliyunFarlightPlat   FactoryKeyType = <PERSON><PERSON> + "-plat2farlight"
	AliyunSecurity       FactoryKeyType = Aliyun + "-security"
	AliyunAvatar         FactoryKeyType = Aliyun + "-avatar"
	AliyunWaibao         FactoryKeyType = Aliyun + "-waibao"
	AliyunIT             FactoryKeyType = Aliyun + "-it"
	AliyunMTR            FactoryKeyType = Aliyun + "-mtr"
	AliyunPGame          FactoryKeyType = Aliyun + "-pgame"
	AliyunPtslg          FactoryKeyType = Aliyun + "-ptslg"
	AliyunSatanpit       FactoryKeyType = Aliyun + "-satanpit"
)

var AliyunFactories = []FactoryKeyType{
	// 主账号
	AliyunLilith,

	// 平台
	AliyunPlatGlobal,

	// Rok
	AliyunRok,

	// iGame
	AliyunIGame,
	AliyunIGameOfficial,

	// Ptslg
	AliyunPtslg,

	// Samo
	AliyunSamo,
	AliyunSamoCN,

	// Dgame
	AliyunDgame,
	// Mona
	AliyunMona,

	// WGame
	AliyunWgame,
	AliyunWgame2,
	AliyunW3,
	AliyunDevops,

	// XGame
	AliyunXGame,

	// PGame
	AliyunPGame,

	// Farlight
	AliyunFarlightCN,
	AliyunFarlightGlobal,
	AliyunFarlightPlat,

	// Party
	AliyunAvatar,

	AliyunSecurity,

	AliyunWaibao,
	// AliyunIT,
	AliyunMTR,

	AliyunSatanpit,
}
