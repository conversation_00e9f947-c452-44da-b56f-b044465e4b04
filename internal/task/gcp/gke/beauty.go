package gke

import (
	"strings"

	"cloud.google.com/go/container/apiv1/containerpb"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
)

func Beauty(c *containerpb.Cluster) fields.Fields {
	attr := fields.NewFields(
		fields.StringField("cluster_id", c.Get<PERSON>d()),
		fields.StringField("cluster_name", c.<PERSON>()),
		fields.StringField("cluster_version", c.GetCurrentMasterVersion()),
		fields.CreateTimeField(c.GetCreateTime()),

		// fields.StringField("vendor", "aliyun"),
		// fields.StringField("account_name", s.cred.Account.KeyName),
		// fields.StringPField("cluster_id", cluster.ClusterId),
		// fields.StringPField("cluster_name", cluster.Name),
		// fields.StringPField("cluster_version", cluster.CurrentVersion),
	)

	if loc := c.GetLocation(); loc != "" {
		idx := strings.LastIndex(loc, "-")
		attr.SetField(fields.RegionIDField(loc[idx+1:]))
	}

	return attr
}
