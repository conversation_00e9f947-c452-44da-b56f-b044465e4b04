package gke

import (
	"log/slog"
	"time"

	"gitlab.lilithgame.com/yunwei/pkg/logger"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/factory"
	gtask "gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/task/gcp"
)

type Syncer struct {
	cred *gtask.FactoryCrendential
	log  *slog.Logger
}

func NewSyncer(accountKey string) (*Syncer, error) {
	cred, err := gtask.CreateCredentialWithAccount(accountKey)
	if err != nil {
		return nil, err
	}

	log := logger.Slog().With("factory", cred.Factory.Name, "account", cred.Account.KeyName)

	return &Syncer{cred: cred, log: log}, nil
}

func Sync(account string) error {
	s, err := NewSyncer(account)
	if err != nil {
		return err
	}

	startAt := time.Now().Unix()
	if serr := s.Sync(); serr != nil {
		return serr
	}

	return s.Clean(startAt)
}

func SyncMany(keys ...factory.FactoryKeyType) error {
	var err error

	for _, key := range keys {
		if serr := Sync(key.String()); serr != nil {
			err = serr
		}
	}

	return err
}

func SyncAll() error {
	return SyncMany(
		factory.GCPLilith,
		factory.GCPFarlight,
	)
}
