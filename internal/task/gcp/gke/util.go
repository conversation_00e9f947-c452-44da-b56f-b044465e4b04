package gke

import "regexp"

func resolveMetaFromURL(url string) map[string]string {
	d := make(map[string]string)

	// https://www.googleapis.com/compute/v1/projects/farlight-dap/zones/asia-southeast1-a/instanceGroupManagers/gke-gsg-dap-global-gke-0001-spark-a65ff65d-grp

	rg := regexp.MustCompile(
		`https://www.googleapis.com/compute/v1` +
			`/projects/(?<project>[a-z0-9\-]+)` +
			`/zones/(?<zone>[a-z0-9\-]+)` +
			`/instanceGroupManagers/(?<group>[a-z0-9\-]+)$`,
	)

	matches := rg.FindStringSubmatch(url)
	if matches == nil {
		return nil
	}

	names := []string{
		"project", "zone", "group",
	}

	for _, name := range names {
		if idx := rg.SubexpIndex(name); idx > -1 {
			d[name] = matches[idx]
		}
	}

	return d
}
