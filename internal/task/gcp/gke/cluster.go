package gke

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	computev1 "cloud.google.com/go/compute/apiv1"
	"cloud.google.com/go/compute/apiv1/computepb"
	containerv1 "cloud.google.com/go/container/apiv1"
	"cloud.google.com/go/container/apiv1/containerpb"
	"github.com/fatih/color"
	"google.golang.org/api/iterator"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/category"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
)

func (s *Syncer) Sync() error {
	var err error

	projects := s.AllProjects()
	for _, p := range projects {
		if serr := s.SyncWithProject(p); serr != nil {
			err = serr
			s.log.Error("sync project error", "project", p, "error", err)
		}
	}

	return err
}

func (s *Syncer) SyncWithProject(project string) error {
	cli, err := containerv1.NewClusterManagerClient(
		context.TODO(),
		s.cred.Credential,
	)
	if err != nil {
		return err
	}
	defer cli.Close()

	req := &containerpb.ListClustersRequest{
		Parent: fmt.Sprintf("projects/%s/locations/-", project),
	}
	resp, err := cli.ListClusters(context.TODO(), req)
	if err != nil {
		return err
	}

	color.Blue("[GKE] Syncing %s", project)

	factoryAttr := fields.FieldList(
		fields.FactoryField(s.cred.Factory.GetID()),
		fields.FactoryAccountField(s.cred.Account.GetID()),
		fields.StringField("cloud_project", project),
	)
	for _, cluster := range resp.Clusters {
		clusterAttrs := Beauty(cluster).With(factoryAttr...)

		// product
		if product := category.Category(cluster.GetName(), project); product != nil {
			clusterAttrs.Set("product", *product)
		}

		// color.Green("\t%+v\n", clusterAttrs)

		conds := fields.FieldList(*clusterAttrs.GetField("cluster_id"))

		if k, perr := restclient.PostOrPatch[models.K8SCluster](conds, clusterAttrs); perr != nil {
			color.Red("sync cluster %s failed, %v", cluster.GetName(), perr)
		} else {
			color.Green("sync cluster %s ok", cluster.GetName())

			startAt := time.Now().Unix()
			// sync nodes
			for _, nodePool := range cluster.GetNodePools() {
				for _, groupUrl := range nodePool.GetInstanceGroupUrls() {
					m := resolveMetaFromURL(groupUrl)
					if lerr := s.syncInstancesInNodePool(k.ClusterID, m["project"], m["zone"], m["group"]); lerr != nil {
						color.Red("list instance error, %v", lerr)
					}
				}
			}

			restclient.DeleteSubResource[models.K8SCluster]("nodes", k.GetID(), fields.NumberField("updated_before", startAt))
		}

	}

	return nil
}

func (s *Syncer) instanceInspector(projectID string, zone string) func(name string) (*computepb.Instance, error) {
	gceSvc, _ := computev1.NewInstancesRESTClient(context.TODO(), s.cred.Credential)

	return func(name string) (*computepb.Instance, error) {
		return gceSvc.Get(context.TODO(), &computepb.GetInstanceRequest{
			Project:  projectID,
			Zone:     zone,
			Instance: name,
		})
	}
}

func (s *Syncer) syncInstancesInNodePool(clusterID string, projectID, zone, group string) error {
	svc, err := computev1.NewInstanceGroupsRESTClient(context.TODO(), s.cred.Credential)
	if err != nil {
		return err
	}

	inspect := s.instanceInspector(projectID, zone)

	req := &computepb.ListInstancesInstanceGroupsRequest{
		Project:       projectID,
		Zone:          zone,
		InstanceGroup: group,
		// ReturnPartialSuccess: fields.Pointer(true),
	}
	it := svc.ListInstances(context.TODO(), req)

	for {
		pair, ierr := it.Next()
		if ierr != nil {
			if !errors.Is(iterator.Done, ierr) {
				err = ierr
				s.log.Error("iterate instance group error", "error", err)
			}
			break
		}

		instanceUrl := pair.GetInstance()
		instanceName := instanceUrl[strings.LastIndex(pair.GetInstance(), "/")+1:]

		s.log.Debug("find an instance", "instance_name", instanceName, "instance_url", instanceUrl)

		if inst, rerr := inspect(instanceName); rerr == nil {
			attrs := fields.NewFields(
				fields.StringField("cluster", clusterID),
				fields.NumberField("instance_id", inst.GetId()),
				fields.StringField("instance_name", inst.GetName()),
			)

			color.Green("\tinstance attrs: %+v", attrs)

			conds := fields.FieldList(*attrs.GetField("instance_id"))
			if _, perr := restclient.PostOrPatch[models.K8SNode](conds, attrs); perr != nil {
				color.Red("sync instance %s error %v", inst.GetName(), perr)
			} else {
				color.Green("sync instance %s ok", inst.GetName())
			}
		} else {
			color.Red("instance %s found error %v", instanceName, rerr)
		}
	}

	return err
}
