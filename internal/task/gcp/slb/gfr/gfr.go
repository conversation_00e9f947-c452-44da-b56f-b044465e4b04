package gfr

import (
	"context"
	"fmt"

	"cloud.google.com/go/compute/apiv1/computepb"
	"google.golang.org/api/iterator"
)

const LBType = "GlobalForwarding"

func (s *Syncer) Sync() error {
	var err error

	// FIXME: Need fix
	for _, project := range []string{
		"cn-shanghai",
	} {
		err = s.SyncWithProject(project)
	}

	return err
}

// func (s *Syncer) ListProjects() []string {
// 	return nil
// }

func (s *Syncer) SyncWithProject(project string) error {
	ctx := context.Background()

	cli, err := CreateGlobalForwardingClient(ctx, s.cred.Credential)
	if err != nil {
		return err
	}
	defer cli.Close()

	var nextToken string
	var started bool

	req := &computepb.ListGlobalForwardingRulesRequest{
		Project: project,
	}

	for ; nextToken != "" || !started; started = true {
		if nextToken != "" {
			req.PageToken = &nextToken
		}

		it := cli.List(ctx, req)
		for {
			rule, err := it.Next()
			if err == iterator.Done {
				break
			}

			if err != nil {
				return err
			}

			fmt.Printf("rule: %+v\n", rule)
		}

		nextToken = it.PageInfo().Token
	}

	return nil
}
