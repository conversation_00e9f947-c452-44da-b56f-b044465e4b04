package gfr

import (
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
)

func LoadZones(conditions ...fields.Field) map[string]models.Zone {
	zonePage, err := restclient.List[models.Zone](conditions...)
	if err != nil {
		return nil
	}

	v := make(map[string]models.Zone)
	for _, zone := range zonePage.Results {
		v[zone.ZoneID] = zone
	}

	return v
}
