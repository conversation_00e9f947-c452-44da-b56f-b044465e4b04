package gfr

import (
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
	gtask "gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/task/gcp"
)

type Syncer struct {
	cred  *gtask.FactoryCrendential
	zones map[string]models.Zone
}

func NewSyncer(accountKey string) (*Syncer, error) {
	cred, err := gtask.CreateCredentialWithAccount(accountKey)
	if err != nil {
		return nil, err
	}

	return &Syncer{
		cred: cred,
		zones: LoadZones(
			fields.NamedField("factory__name", "谷歌云"),
			fields.Unlimited,
		),
	}, nil
}
