package gcp

import (
	"context"
	"fmt"
	"sync"

	log "gitlab.lilithgame.com/yunwei/pkg/logger"
	"google.golang.org/api/compute/v1"
	"google.golang.org/api/option"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/util"
)

type imageChanData struct {
	imageTotal int32
	imageList  []string
}

func ImageTask(jsonStr string, projectList []string, GCP_REG_MAP_FROM_CMDB map[string]float64, regionList []*compute.Region, factoryId int) {
	ALL_PROJECT_IMAGE_LIST := []string{}
	var allProjectImageTotal int32
	ch := make(chan imageChanData, 100)
	var wg, consume, product sync.WaitGroup
	wg.Add(1)
	consume.Add(len(projectList))
	for _, project := range projectList {
		product.Add(1)
		go func(project string, regionList []*compute.Region) {
			ALL_IMAGE_LIST := []string{}
			var total int32
			ctx := context.Background()
			computeService, err := compute.NewService(ctx, option.WithCredentialsJSON([]byte(jsonStr)))
			if err != nil {
				log.Error(err.Error())
				fmt.Println("compute.NewService Error", err.Error())
				product.Done()
				consume.Done()
				return
			}
			if computeService == nil {
				product.Done()
				consume.Done()
				return
			}
			res, err := computeService.Images.List(project).Context(ctx).Do()
			if err != nil {
				log.Error(err.Error())
				fmt.Println("computeService.Images.List Error", err)
				product.Done()
				consume.Done()
				return
			} else {
				if len(res.Items) != 0 {
					for _, item := range res.Items {
						// if !strings.HasPrefix(*item.Platform, "CentOS") && !strings.HasPrefix(*item.Platform, "Ubuntu") {
						// 	continue
						// }
						// attr := util.ImageAttrBuilder(*image.ImageId, *image.Name, *image.Description, *image.Name, *image.ImageOwnerAlias, "", AWS_REG_MAP_FROM_CMDB[*region.RegionName], 3)
						// fmt.Println("attr:", attr)
						ALL_IMAGE_LIST = append(ALL_IMAGE_LIST, item.Name)
						total++
					}
				}
			}
			imageData := imageChanData{
				imageTotal: total,
				imageList:  ALL_IMAGE_LIST,
			}
			ch <- imageData
			product.Done()
		}(project, regionList)
	}
	go func() {
		defer wg.Done()
		for c := range ch {
			allProjectImageTotal += c.imageTotal
			ALL_PROJECT_IMAGE_LIST = append(ALL_PROJECT_IMAGE_LIST, c.imageList...)
			consume.Done()
		}
	}()
	go func() {
		product.Wait()
		consume.Wait()
		close(ch)
	}()
	wg.Wait()

	// 清理cmdb中无用数据
	if cmdbResultList, err := restclient.ListAll[models.Image](
		fields.NamedField("factory", factoryId),
		// TODO: 镜像应该跟随项目，而不是跟随工厂?
	); err == nil {
		for _, img := range cmdbResultList.Results {
			imageId := img.ExternalID
			if !util.IsContain(ALL_PROJECT_IMAGE_LIST, imageId) {
				// oma.DeleteByID("image", img.ID)
				restclient.Delete(img)
			}
		}
	}
}
