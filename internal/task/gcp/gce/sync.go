package gce

import (
	"log/slog"
	"time"

	"github.com/fatih/color"
	"gitlab.lilithgame.com/yunwei/pkg/logger"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
	gtask "gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/task/gcp"
)

type Syncer struct {
	cred  *gtask.FactoryCrendential
	zones map[string]*models.Zone
	log   *slog.Logger
}

func NewSyncer(accountKey string) (*Syncer, error) {
	cred, err := gtask.CreateCredentialWithAccount(accountKey)
	if err != nil {
		return nil, err
	}

	log := logger.Slog().With("factory", cred.Factory.Name, "account", cred.Account.KeyName)

	return &Syncer{
		cred: cred,
		zones: LoadZones(
			fields.FactoryField(cred.Factory.GetID()),
			// fields.FactoryAccountField(cred.Account.GetID()),
			fields.Unlimited,
		),
		log: log,
	}, nil
}

func Sync(account string, projects ...string) error {
	s, err := NewSyncer(account)
	if err != nil {
		return err
	}

	// projects := s.GlobalForwardingEnabeldProjects()
	if len(projects) == 0 {
		projects = s.ListProjects()
	}
	start := time.Now().Unix()

	var errs []error
	for _, p := range projects {
		s.log.Info("sync gcp project", "project", p)

		if err := s.SyncProjectInstances(p); err != nil {
			errs = append(errs, err)
			s.log.Error("sync gcp project error", "project", p, "error", err)
		} else {
			s.log.Debug("sync gcp project ok", "project", p)
		}
	}

	if len(errs) > 0 {
		color.Red("skip cleanup project %s due to error %v", account, errs[0])
		return errs[0]
	}

	// clean empty account that no updates in 12 hours
	s.CleanEmptyAccountMachine(start, -time.Hour*12)
	return s.Clean(start)
}
