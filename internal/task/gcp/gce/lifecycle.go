package gce

// https://cloud.google.com/compute/docs/instances/instance-life-cycle?hl=zh-cn
const (
	// 为虚拟机分配的资源。虚拟机尚未运行
	StateProvisioning = "PROVISIONING"
	// 获取资源，虚拟机正在准备首次启动
	StateStaging = "STAGING"
	// 虚拟机正在启动或运行
	StateRunning = "RUNNING"
	// 虚拟机正在停止。这是虚拟机进入 TERMINATED 状态后的临时状态
	StateStopping = "STOPPING"
	// 正在修复虚拟机
	StateRepairing = "REPAIRING"
	// 虚拟机已停止
	StateTerminated = "TERMINATED"
	// 虚拟机正在暂停
	StateSuspending = "SUSPENDING"
	// 虚拟机处于暂停状态
	StateSuspended = "SUSPENDED"
)
