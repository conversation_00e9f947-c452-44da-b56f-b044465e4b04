package gce

import (
	"context"
	"errors"
	"fmt"
	"path/filepath"
	"slices"

	computev1 "cloud.google.com/go/compute/apiv1"
	"cloud.google.com/go/compute/apiv1/computepb"
	osconfigv1 "cloud.google.com/go/osconfig/apiv1"
	osconfigpb "cloud.google.com/go/osconfig/apiv1/osconfigpb"
	rmgv3 "cloud.google.com/go/resourcemanager/apiv3"
	"github.com/fatih/color"
	"github.com/googleapis/gax-go/v2/apierror"

	beauty "gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/beauty/gcp"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
)

func (s *Syncer) ListProjects(filters ...ProjectFilter) []string {
	cli, err := rmgv3.NewProjectsClient(context.TODO(), s.cred.Credential)
	if err != nil {
		return nil
	}
	defer cli.Close()

	projects := make([]string, 0)

	it := NewProjectIterator(cli, filters...)
	for p := range it.Iter() {
		projects = append(projects, p.GetProjectId())
	}

	slices.Sort(projects)

	return projects
}

func (s *Syncer) GlobalForwardingEnabeldProjects() []string {
	return s.ListProjects(s.GlobalForwardingRulesFilter)
}

func (s *Syncer) SyncProjectInstances(project string) error {
	plog := s.log.With("project", project)

	cli, err := computev1.NewInstancesRESTClient(context.TODO(), s.cred.Credential)
	if err != nil {
		plog.Error("create compute v1 restclient failed", "error", err)
		return err
	}
	defer cli.Close()

	machineTypeQuery := s.completeMachineType(project)

	it := NewDefaultProjectInstanceIterator(cli, project)

	var updateError error
	for {
		instances, ierr := it.NextPage()

		if ierr != nil {
			if gerr, ok := ierr.(*apierror.APIError); ok && gerr.HTTPCode() == 403 {
				plog.Warn("project not allowed to list", "err", gerr.Error())
				return nil
			}
			break
		}

		plog.Debug("gce next page", "instance_count", len(instances))

		for _, instance := range instances {
			if uerr := s.updateInstance(instance, project, machineTypeQuery); uerr != nil {
				updateError = uerr
				plog.Error("update instance failed", "error", uerr, "instance", instance.GetName())
			}
		}
	}

	if updateError != nil {
		return updateError
	}

	if errors.Is(err, ErrNoMore) {
		return nil
	}

	return err
}

func (s *Syncer) updateInstance(instance *computepb.Instance, project string, typeQuery func(machineType, zoneName string) (fields.Fields, error)) error {
	color.Green("  %s (zone: %s, id: %d, zone: %s)",
		instance.GetName(),
		filepath.Base(instance.GetZone()),
		instance.GetId(),
		instance.GetZone(),
		// instance.GetHostname(),
		// instance,
	)

	// comment attrs
	attrs := beauty.Beauty(instance, project).
		With(
			fields.FactoryAccountField(s.cred.Account.GetID()),
			fields.GCPProjectIDField(project),
		)

	// cpu, memory is required
	flavor := filepath.Base(instance.GetMachineType())
	zone := filepath.Base(instance.GetZone())
	if fs, err := typeQuery(flavor, zone); err == nil {
		attrs.Update(fs)
	} else {
		color.Red("machine type %s not found, %v", attrs.GetString(fields.ExternalFlavorFieldKey), err)
	}

	// os info
	if osinfo, err := s.completeOSInfo(project, zone, fmt.Sprintf("%d", instance.GetId())); err == nil {
		attrs.With(
			fields.OSNameField(osinfo.LongName),
		)
	} else {
		s.log.Warn("get os info failed", "error", err, "project", project, "zone", zone, "instance", instance.GetId())
	}

	// Update/Insert to database
	conds := []fields.Field{
		fields.ExternalUUIDField(fields.IntString(instance.GetId())),
	}

	if _, perr := restclient.PostOrPatch[models.Machine](conds, attrs); perr != nil {
		return perr
	}

	return nil
}

func (s *Syncer) completeOSInfo(project string, zone string, instance string) (*osconfigpb.Inventory_OsInfo, error) {
	oscli, oerr := osconfigv1.NewOsConfigZonalRESTClient(context.TODO(), s.cred.Credential)
	if oerr != nil {
		return nil, oerr
	}
	defer oscli.Close()

	req := &osconfigpb.GetInventoryRequest{
		Name: fmt.Sprintf(
			"projects/%s/locations/%s/instances/%s/inventory",
			project, zone, instance,
		),
	}
	invent, err := oscli.GetInventory(context.TODO(), req)
	if err != nil {
		return nil, err
	}

	return invent.GetOsInfo(), nil
}

func (s *Syncer) completeMachineType(project string) func(machineType, zoneName string) (fields.Fields, error) {
	buckets := make(map[string]fields.Fields)

	return func(machineType, zoneName string) (fields.Fields, error) {
		if v, exists := buckets[machineType+zoneName]; exists {
			return v, nil
		}

		cli, err := computev1.NewMachineTypesRESTClient(context.TODO(), s.cred.Credential)
		if err != nil {
			return nil, err
		}
		defer cli.Close()

		mtype, err := cli.Get(context.TODO(), &computepb.GetMachineTypeRequest{
			MachineType: machineType,
			Project:     project,
			Zone:        zoneName,
		})
		if err != nil {
			return nil, err
		}

		attrs := fields.NewFields(
			fields.CPUField(mtype.GetGuestCpus()),
			fields.MemoryField(mtype.GetMemoryMb()/1024),
		)

		// cache it
		buckets[machineType+zoneName] = attrs

		return attrs, nil
	}
}
