package gce

import (
	"time"

	"github.com/fatih/color"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
)

func (s *Syncer) Clean(t int64) error {
	if cerr := s.CleanOutdatedMachines(t); cerr != nil {
		color.Red("clean outdated machine failed, %v", cerr)
		return cerr
	}

	// if cerr := s.CleanEmptyAccountMachine(t, -time.Hour*4); cerr != nil {
	// 	color.Red("clean empty account outdated machine failed, %v", cerr)
	// 	return cerr
	// }

	return nil
}

func (s *Syncer) CleanOutdatedMachines(startTime int64) error {
	machines, err := restclient.ListAll[models.Machine](
		fields.FactoryField(s.cred.Factory.GetID()),
		fields.FactoryAccountField(s.cred.Account.GetID()),
		fields.NumberField("updated_before", startTime),
		fields.Not(fields.ExternalStatusFieldKey, fields.ExternalStatusDeleted.Value),
	)
	if err != nil {
		return err
	}

	s.log.Info("find gce machines outdated", "count", machines.Count)

	for _, m := range machines.Results {
		if m.AgentVersion != "" && m.ExternalStatus != fields.ExternalStatusTerminated.Value {
			s.log.Warn("should not delete this machine",
				"reason", "agent version not empty, it may be running",
				fields.ExternalUUIDFieldKey, m.ExternalUUID,
				fields.ExternalNameFieldKey, m.ExternalName,
				fields.ExternalStatusFieldKey, m.ExternalStatus,
				"update_at", m.UpdateAt,
				"startTime", startTime,
				"instance_factory", m.Factory,
				"instance_account", m.Account,
				"deleted_by", s.cred.Factory.GetID(),
				"deleted_by_account", s.cred.Account.GetID(),
			)
			// TODO: but in case of accident, we should not delete it
			continue
		}

		if perr := restclient.Patch(&m, fields.NewFields(fields.ExternalStatusDeleted)); perr != nil {
			s.log.Error("patch machine failed", "id", m.GetID(), "name", m.ExternalName, "error", perr)
		} else {
			s.log.Debug("patch machine deleted ok", "id", m.GetID(), "name", m.ExternalName)
		}
	}

	return nil
}

func softDelete(ms []models.Machine) error {
	for _, m := range ms {
		if perr := restclient.Patch(&m, fields.NewFields(fields.ExternalStatusDeleted)); perr != nil {
			return perr
		}
	}
	return nil
}

func (s *Syncer) CleanEmptyAccountMachine(t int64, delta time.Duration) error {
	conds := []fields.Field{
		fields.FactoryField(s.cred.Factory.GetID()),
		fields.IsNull(fields.FactoryAccountFieldKey),
		fields.Not(fields.ExternalStatusFieldKey, fields.ExternalStatusDeleted.Value),
	}

	ago := time.Unix(t, 0).Add(delta).Unix()
	conds = append(conds, fields.NumberField("updated_before", ago))

	var err error
	for {
		resp, lerr := restclient.ListN[models.Machine](100, conds...)
		if lerr != nil {
			err = lerr
			break
		}

		if resp.Count <= 0 {
			s.log.Info("no more empty account machine to delete")
			break
		}

		s.log.Info("find outdated empty account machine to delete", "count", resp.Count, "updated_before", t)

		if derr := softDelete(resp.Results); derr != nil {
			err = derr
			break
		}
	}

	return err
}
