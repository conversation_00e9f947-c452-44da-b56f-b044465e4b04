package gce

import (
	"context"
	"errors"

	computev1 "cloud.google.com/go/compute/apiv1"
	"cloud.google.com/go/compute/apiv1/computepb"
	"google.golang.org/api/iterator"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/util"
)

var (
	ErrNoMore           = errors.New("no more")
	ErrIterationStopped = errors.New("iterator is stopped")
)

type ProjectInstanceIterator struct {
	cli *computev1.InstancesClient

	Project  string
	PageSize uint32

	started   bool
	nextToken string
}

func NewDefaultProjectInstanceIterator(cli *computev1.InstancesClient, project string) *ProjectInstanceIterator {
	return NewProjectInstanceIterator(cli, project, 100)
}

func NewProjectInstanceIterator(cli *computev1.InstancesClient, project string, pageSize uint32) *ProjectInstanceIterator {
	it := ProjectInstanceIterator{
		cli:      cli,
		Project:  project,
		PageSize: util.NumberRanger(uint32(10), 500)(pageSize),
	}
	return &it
}

func (it *ProjectInstanceIterator) Iter() <-chan *computepb.Instance {
	ch := make(chan *computepb.Instance, 1)

	go func(c chan<- *computepb.Instance) {
		for page, err := it.NextPage(); err == nil; page, err = it.NextPage() {
			for _, instance := range page {
				c <- instance
			}
		}

		close(c)
	}(ch)

	return ch
}

func (it *ProjectInstanceIterator) NextPage() ([]*computepb.Instance, error) {
	token := it.nextToken
	if token == "" && it.started {
		return nil, ErrNoMore
	}

	req := computepb.AggregatedListInstancesRequest{
		MaxResults:           &it.PageSize,
		Project:              it.Project,
		ReturnPartialSuccess: fields.Pointer(true),
	}

	if token != "" {
		req.PageToken = &token
	}

	it.started = true

	resp := it.cli.AggregatedList(context.TODO(), &req)
	it.nextToken = resp.PageInfo().Token

	instances := make([]*computepb.Instance, 0)

	for pair, err := resp.Next(); ; pair, err = resp.Next() {
		if pairValue := pair.Value; pairValue != nil {
			instances = append(instances, pair.Value.Instances...)
		}

		if err != nil {
			if errors.Is(err, iterator.Done) {
				return instances, nil
			}
			return instances, err
		}
	}
}
