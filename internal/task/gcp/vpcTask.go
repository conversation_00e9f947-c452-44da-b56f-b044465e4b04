package gcp

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"sync"

	log "gitlab.lilithgame.com/yunwei/pkg/logger"
	"google.golang.org/api/compute/v1"
	"google.golang.org/api/googleapi"
	"google.golang.org/api/option"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/util"
)

type vpcChanData struct {
	vpcTotal    int32
	vpcList     []string
	subnetTotal int32
	subnetList  []string
}

func VPCTask(
	cred []byte,
	projectList []string,
	GCP_REG_MAP_FROM_CMDB map[string]int,
	regionList []*compute.Region,
	factoryId int,
	accountID int,
) {
	ALL_PROJECT_VPC_LIST := []string{}
	ALL_REGION_SUBNET_LIST := []string{}

	var allProjectVpcTotal, allRegionSubnetTotal int32
	ch := make(chan vpcChanData, 100)
	var wg, consume, product sync.WaitGroup

	wg.Add(1)
	consume.Add(len(projectList))
	for _, project := range projectList {
		product.Add(1)
		go func(project string, regionList []*compute.Region) {
			ALL_VPC_LIST := []string{}
			ALL_SUBNET_LIST := []string{}

			var vpcTotal, allNumber int32

			ctx, cancel := context.WithCancel(context.Background())
			defer cancel() // 确保上下文总是被取消

			computeService, err := compute.NewService(ctx, option.WithCredentialsJSON(cred))
			if err != nil {
				log.Error(err.Error())
				fmt.Println("compute.NewService Error", err.Error())
				product.Done()
				consume.Done()
				return
			}
			if computeService == nil {
				product.Done()
				consume.Done()
				return
			}
			resp1, err := computeService.Networks.List(project).Context(ctx).Do()
			if err != nil {
				googleError, ok := err.(*googleapi.Error)
				if ok && googleError.Code == 403 {
					// 安全地处理错误详情，避免 panic
					var reasonMsg string
					if len(googleError.Details) > 1 {
						if detail, ok := googleError.Details[1].(map[string]any); ok {
							if reason, exists := detail["reason"]; exists && reason != nil {
								if reasonStr, ok := reason.(string); ok {
									reasonMsg = reasonStr
								} else {
									reasonMsg = fmt.Sprintf("%v", reason)
								}
							} else {
								reasonMsg = "unknown reason"
							}
						} else {
							reasonMsg = "invalid detail format"
						}
					} else {
						reasonMsg = "no detailed error information"
					}
					log.Warn("GCP VPC access denied", "project", project, "reason", reasonMsg)
				} else {
					log.Error("GCP VPC list error", "project", project, "error", err.Error())
				}
				product.Done()
				consume.Done()
				return
			}
			vpcs := resp1.Items
			for _, vpc := range vpcs {
				vpcName := vpc.Name
				if vpcName == "default" {
					continue
				}
				vpcId := strconv.FormatUint(vpc.Id, 10)
				ALL_VPC_LIST = append(ALL_VPC_LIST, vpcId)

				var vpcRecord models.VPC
				hasVPCRecord, err := restclient.Find(&vpcRecord, fields.NamedField("vpc_id", vpcId))
				if err != nil {
					log.Error("VPC: "+vpcId+" len(cmdbResultList) != 0/1, Check it!", err.Error())
					continue
				}

				vpcAttr := util.VpcAttrBuilder("", vpcId, vpcName, vpc.Description, "", factoryId, accountID)
				if !hasVPCRecord {
					if resp, perr := restclient.Post[models.VPC](vpcAttr); perr == nil {
						vpcRecord = *resp
					}
				} else {
					restclient.PatchByID[models.VPC](vpcRecord.GetID(), vpcAttr)
				}

				vpcTotal++
			}

			for _, region := range regionList {
				resp, err := computeService.Subnetworks.List(project, region.Name).Context(ctx).Do()
				if err != nil {
					fmt.Println("computeService.Subnetworks.List Error", err.Error())
					continue
				}
				if len(resp.Items) > 0 {
					for _, subnetwork := range resp.Items {
						vpcName := strings.Split(subnetwork.Network, "/")[len(strings.Split(subnetwork.Network, "/"))-1]
						if vpcName == "default" {
							continue
						}

						var vpcInOMA models.VPC
						if hasVpc, err := restclient.Find(&vpcInOMA, fields.NamedField("name", vpcName)); err != nil || !hasVpc {
							log.Error("find vpc failed", "vpc", vpcName, "error", err)
							continue
						}

						// vpcCmdbId := vpcMap["id"].(float64)
						vpcCmdbId := vpcInOMA.GetID()
						subnetName := subnetwork.Name
						cidr := subnetwork.IpCidrRange
						if strings.HasPrefix(cidr, "172") {
							continue
						}
						subnetId := strconv.FormatUint(subnetwork.Id, 10)

						var subnetRecord models.Subnet
						hasRecord, err := restclient.Find(&subnetRecord, fields.NamedField("subnet_id", subnetId))
						if err != nil {
							log.Error("search subnet record error", "subnet_id", subnetId, "error", err)
							continue
						}

						subnetAttr := util.SubnetAttrBuilder(subnetId, subnetName, "", subnetwork.Description, cidr, false, 0, vpcCmdbId, "")
						if !hasRecord {
							restclient.Post[models.Subnet](subnetAttr)
						} else {
							restclient.PatchByID[models.Subnet](subnetRecord.GetID(), subnetAttr)
						}

						allNumber++
						ALL_SUBNET_LIST = append(ALL_SUBNET_LIST, subnetId)
					}
				}
			}

			vpcData := vpcChanData{
				vpcTotal:    vpcTotal,
				vpcList:     ALL_VPC_LIST,
				subnetTotal: allNumber,
				subnetList:  ALL_SUBNET_LIST,
			}
			ch <- vpcData
			product.Done()
		}(project, regionList)
	}
	go func() {
		defer wg.Done()
		for c := range ch {
			allProjectVpcTotal += c.vpcTotal
			ALL_PROJECT_VPC_LIST = append(ALL_PROJECT_VPC_LIST, c.vpcList...)
			allRegionSubnetTotal += c.subnetTotal
			ALL_REGION_SUBNET_LIST = append(ALL_REGION_SUBNET_LIST, c.subnetList...)
			consume.Done()
		}
	}()
	go func() {
		product.Wait()
		consume.Wait()
		close(ch)
	}()
	wg.Wait()

	// 清理cmdb中无用数据
	// subnet
	if subnets, err := restclient.ListAll[models.Subnet](
		fields.NamedField("vpc__factory", factoryId),
	); err == nil {
		for _, r := range subnets.Results {
			if !util.IsContain(ALL_REGION_SUBNET_LIST, r.SubnetID) {
				restclient.Delete(r)
			}
		}
	}

	// vpc
	if vpcs, err := restclient.ListAll[models.VPC](
		fields.NamedField("factory", factoryId),
	); err == nil {
		for _, r := range vpcs.Results {
			if !util.IsContain(ALL_PROJECT_VPC_LIST, r.VpcID) {
				// oma.DeleteByID("vpc", r.GetID())
				restclient.Delete(r)
			}
		}
	}
}
