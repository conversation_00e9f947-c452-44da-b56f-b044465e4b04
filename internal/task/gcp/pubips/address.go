package pubips

import (
	"context"

	computev1 "cloud.google.com/go/compute/apiv1"
	rmgrv3 "cloud.google.com/go/resourcemanager/apiv3"
	"github.com/fatih/color"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
)

func (s *Syncer) AllProjects(filters ...ProjectFilter) []string {
	cli, err := rmgrv3.NewProjectsClient(context.TODO(), s.cred.Credential)
	if err != nil {
		return nil
	}

	projects := make([]string, 0)

	it := NewProjectIterator(cli, filters...)
	for p := range it.Iter() {
		projects = append(projects, p.GetProjectId())
	}

	return projects
}

func (s *Syncer) GlobalForwardingEnabeldProjects() []string {
	return s.AllProjects(s.GlobalForwardingRulesFilter)
}

func (s *Syncer) SyncProjectAddresses(project string) error {
	cli, err := computev1.NewAddressesRESTClient(context.TODO(), s.cred.Credential)
	if err != nil {
		s.log.Error("failed to create eip client", "error", err)
		return err
	}
	defer cli.Close()

	it := NewDefaultProjectAddressIterator(cli, project)
	for addr := range it.Iter() {
		attrs := Beauty(addr, project).With(
			fields.FactoryField(s.cred.Factory.GetID()),
			fields.FactoryAccountField(s.cred.Account.GetID()),
		)
		color.Cyan("  address: %+v\n", attrs)

		// sync to db
		conds := []fields.Field{
			fields.ExternalUUIDField(attrs.GetString(fields.ExternalUUIDFieldKey)),
		}

		if _, perr := restclient.PostOrPatch[models.ElasticIP](conds, attrs); perr != nil {
			err = perr
			s.log.Error("failed to sync eip", "error", err)
		}
	}

	return err
}

func (s *Syncer) SyncProjectGlobalAddresses(project string) error {
	cli, err := computev1.NewGlobalAddressesRESTClient(context.TODO(), s.cred.Credential)
	if err != nil {
		s.log.Error("failed to create global address client", "error", err)
		return err
	}
	defer cli.Close()

	it := NewDefaultProjectGlobalAddressIterator(cli, project)
	for addr := range it.Iter() {
		attrs := Beauty(addr, project).With(
			fields.FactoryField(s.cred.Factory.GetID()),
			fields.FactoryAccountField(s.cred.Account.GetID()),
		)
		color.Green("  global address: %+v\n", attrs)

		conds := []fields.Field{
			fields.ExternalUUIDField(attrs.GetString(fields.ExternalUUIDFieldKey)),
		}

		if _, perr := restclient.PostOrPatch[models.ElasticIP](conds, attrs); perr != nil {
			err = perr
			s.log.Error("failed to sync global eip", "error", err)
		}
	}

	return err
}
