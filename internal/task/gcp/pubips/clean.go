package pubips

import (
	"fmt"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
)

func (s *Syncer) Clean(t int64) error {
	conds := []fields.Field{
		fields.FactoryField(s.cred.Factory.GetID()),
		fields.FactoryAccountField(s.cred.Account.GetID()),
		fields.LessThan("update_at", t),
	}

	resp, err := restclient.ListAll[models.ElasticIP](conds...)
	if err != nil {
		return err
	}

	for _, m := range resp.Results {
		fmt.Printf("delete pubip %s => %s\n", m.Name, m.PublicIP)
		if perr := restclient.Delete(m); perr != nil {
			err = perr
		}
	}

	return err
}
