package pubips

import (
	"context"

	computev1 "cloud.google.com/go/compute/apiv1"
	cpbv1 "cloud.google.com/go/compute/apiv1/computepb"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/util"
)

type ProjectGlobalAddressIterator struct {
	cli *computev1.GlobalAddressesClient

	Project  string
	PageSize uint32

	started   bool
	nextToken string
}

func NewDefaultProjectGlobalAddressIterator(cli *computev1.GlobalAddressesClient, project string) *ProjectGlobalAddressIterator {
	return NewProjectGlobalAddressIterator(cli, project, 100)
}

func NewProjectGlobalAddressIterator(cli *computev1.GlobalAddressesClient, project string, pageSize uint32) *ProjectGlobalAddressIterator {
	it := ProjectGlobalAddressIterator{
		cli:      cli,
		Project:  project,
		PageSize: util.NumberRanger[uint32](10, 500)(pageSize),
	}
	return &it
}

func (it *ProjectGlobalAddressIterator) Iter() <-chan *cpbv1.Address {
	ch := make(chan *cpbv1.Address, 1)

	go func(c chan<- *cpbv1.Address) {
		for page, err := it.NextPage(); err == nil; page, err = it.NextPage() {
			for _, addr := range page {
				c <- addr
			}
		}

		close(c)
	}(ch)

	return ch
}

func (it *ProjectGlobalAddressIterator) NextPage() ([]*cpbv1.Address, error) {
	token := it.nextToken
	if token == "" && it.started {
		return nil, ErrNoMore
	}

	req := cpbv1.ListGlobalAddressesRequest{
		MaxResults: &it.PageSize,
		Project:    it.Project,
		Filter:     fields.Pointer("addressType = EXTERNAL"),
	}

	req.PageToken = &token
	it.started = true

	resp := it.cli.List(context.TODO(), &req)
	it.nextToken = resp.PageInfo().Token

	addrs := make([]*cpbv1.Address, 0)
	for addr, err := resp.Next(); err == nil; addr, err = resp.Next() {
		addrs = append(addrs, addr)
	}

	if len(addrs) == 0 {
		return nil, ErrNoMore
	}

	return addrs, nil
}
