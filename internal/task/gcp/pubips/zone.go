package pubips

import (
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
)

func LoadZones(conditions ...fields.Field) map[string]*models.Zone {
	page, err := restclient.List[models.Zone](conditions...)
	if err != nil {
		return nil
	}

	zoneMap := make(map[string]*models.Zone)
	for _, zone := range page.Results {
		zoneMap[zone.ZoneID] = &zone
	}

	return zoneMap
}
