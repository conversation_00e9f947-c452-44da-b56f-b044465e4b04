package pubips

import (
	"context"
	"errors"

	computev1 "cloud.google.com/go/compute/apiv1"
	cpbv1 "cloud.google.com/go/compute/apiv1/computepb"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/util"
)

var (
	ErrNoMore           = errors.New("no more")
	ErrIterationStopped = errors.New("iterator is stopped")
)

type ProjectAddressIterator struct {
	cli *computev1.AddressesClient

	Project  string
	PageSize uint32

	started   bool
	nextToken string
}

func NewDefaultProjectAddressIterator(cli *computev1.AddressesClient, project string) *ProjectAddressIterator {
	return NewProjectAddressIterator(cli, project, 100)
}

func NewProjectAddressIterator(cli *computev1.AddressesClient, project string, pageSize uint32) *ProjectAddressIterator {
	it := ProjectAddressIterator{
		cli:      cli,
		Project:  project,
		PageSize: util.NumberRanger[uint32](10, 500)(pageSize),
	}
	return &it
}

func (it *ProjectAddressIterator) Iter() <-chan *cpbv1.Address {
	ch := make(chan *cpbv1.Address, 1)

	go func(c chan<- *cpbv1.Address) {
		for page, err := it.NextPage(); err == nil; page, err = it.NextPage() {
			for _, addr := range page {
				c <- addr
			}
		}

		close(c)
	}(ch)

	return ch
}

func (it *ProjectAddressIterator) NextPage() ([]*cpbv1.Address, error) {
	token := it.nextToken
	if token == "" && it.started {
		return nil, ErrNoMore
	}

	req := cpbv1.AggregatedListAddressesRequest{
		MaxResults: &it.PageSize,
		Project:    it.Project,
		Filter:     fields.Pointer("addressType = EXTERNAL"),
	}

	req.PageToken = &token
	it.started = true

	resp := it.cli.AggregatedList(context.TODO(), &req)
	it.nextToken = resp.PageInfo().Token

	addrs := make([]*cpbv1.Address, 0)
	for pair, err := resp.Next(); err == nil; pair, err = resp.Next() {
		addrs = append(addrs, pair.Value.Addresses...)
	}

	if len(addrs) == 0 {
		return nil, ErrNoMore
	}

	return addrs, nil
}
