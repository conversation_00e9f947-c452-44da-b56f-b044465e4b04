package pubips

import (
	"context"
	"errors"

	computev1 "cloud.google.com/go/compute/apiv1"
	"cloud.google.com/go/compute/apiv1/computepb"
	rmgrv3 "cloud.google.com/go/resourcemanager/apiv3"
	rpbv3 "cloud.google.com/go/resourcemanager/apiv3/resourcemanagerpb"
	"google.golang.org/api/iterator"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
)

type ProjectFilter func(*rpbv3.Project) bool

// ProjectIterator is an iterator for all projects under an account.
type ProjectIterator struct {
	cli     *rmgrv3.ProjectsClient
	filters []ProjectFilter
}

func NewProjectIterator(cli *rmgrv3.ProjectsClient, filters ...ProjectFilter) *ProjectIterator {
	return &ProjectIterator{
		cli:     cli,
		filters: filters,
	}
}

func (it *ProjectIterator) AddFilter(filter ProjectFilter) {
	it.filters = append(it.filters, filter)
}

func (it *ProjectIterator) Filter(p *rpbv3.Project) bool {
	for _, filter := range it.filters {
		if !filter(p) {
			return false
		}
	}
	return true
}

func (it *ProjectIterator) Iter() <-chan *rpbv3.Project {
	ch := make(chan *rpbv3.Project, 1)

	go func(c chan<- *rpbv3.Project) {
		defer close(c)

		var token string
		var stared bool

		// search(list) projects
		req := rpbv3.SearchProjectsRequest{
			Query:    "parent.type:organization",
			PageSize: 100,
		}

		for ; token != "" || !stared; stared = true {
			if token != "" {
				req.PageToken = token
			}

			pit := it.cli.SearchProjects(context.Background(), &req)

			// Set next token
			token = pit.PageInfo().Token

			for {
				p, err := pit.Next()
				if errors.Is(err, iterator.Done) {
					break
				}

				if err != nil {
					return
				}

				if !it.Filter(p) {
					continue
				}

				c <- p
			}
		}
	}(ch)

	return ch
}

// GlobalForwardingRulesFilter returns a filter that filters out projects that have no global forwarding rules.
//
// ref: https://cloud.google.com/load-balancing/docs/forwarding-rule-concepts?hl=zh-cn#https_lb
func (s *Syncer) GlobalForwardingRulesFilter(p *rpbv3.Project) bool {
	rc, err := computev1.NewGlobalForwardingRulesRESTClient(context.TODO(), s.cred.Credential)
	if err != nil {
		return false
	}
	defer rc.Close()

	it := rc.List(context.TODO(), &computepb.ListGlobalForwardingRulesRequest{
		Project:    p.GetProjectId(),
		MaxResults: fields.Uint32(1),
	})

	if _, err := it.Next(); err != nil {
		return false
	}
	return true
}

func (s *Syncer) GlobalForwardingRulesDisabledFilter(p *rpbv3.Project) bool {
	return !s.GlobalForwardingRulesFilter(p)
}
