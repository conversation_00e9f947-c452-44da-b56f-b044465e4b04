package pubips

import (
	cpbv1 "cloud.google.com/go/compute/apiv1/computepb"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
)

// Beauty returns a Fields object with all the fields of a pubip
// object, but with their names beautified.
func Beauty(a *cpbv1.Address, project string) fields.Fields {
	attrs := fields.NewFields(
		fields.ExternalUUIDField(fields.IntString(a.GetId())),
		fields.StringField("name", a.GetName()),
		fields.StringField("version", a.GetIpVersion()),
		fields.PublicIPField(a.GetAddress()),
		fields.CreateTimeField(a.GetCreationTimestamp()),
		fields.StringField("bind_type", a.GetPurpose()),
	)

	// upper case status for consistency, and map "IN_USE" to "RUNNING"
	status := fields.PLowerString(a.Status)
	if status == "in_use" {
		status = "running"
	}
	attrs.SetField(fields.ExternalStatusField(status))

	return attrs
}
