package pubips

import (
	"log/slog"
	"time"

	"gitlab.lilithgame.com/yunwei/pkg/logger"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
	gtask "gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/task/gcp"
)

type Syncer struct {
	cred  *gtask.FactoryCrendential
	zones map[string]*models.Zone
	log   *slog.Logger
}

func NewSyncer(account string) (*Syncer, error) {
	cred, err := gtask.CreateCredentialWithAccount(account)
	if err != nil {
		return nil, err
	}

	s := &Syncer{
		cred: cred,
		zones: LoadZones(
			fields.FactoryField(cred.Factory.GetID()),
			fields.Unlimited,
		),
		log: logger.Slog().With("factory", cred.Factory.Name, "account", cred.Account.Name),
	}
	return s, nil
}

func Sync(account string) error {
	s, err := NewSyncer(account)
	if err != nil {
		return err
	}

	// projects := s.GlobalForwardingEnabeldProjects()
	projects := s.AllProjects()
	startTime := time.Now().Unix()

	var lastError error
	for _, p := range projects {
		if err := s.SyncProjectAddresses(p); err != nil {
			lastError = err
		}

		if err = s.SyncProjectGlobalAddresses(p); err != nil {
			lastError = err
		}
	}

	if lastError != nil {
		return lastError
	}

	// clean empty account that no updates in 12 hours
	return s.Clean(startTime)
}

func SyncRegional(account string) error {
	s, err := NewSyncer(account)
	if err != nil {
		return err
	}

	// projects := s.GlobalForwardingEnabeldProjects()
	projects := s.AllProjects()
	startTime := time.Now().Unix()

	var lastErr error
	for _, p := range projects {
		logger.Info("sync gcp project", "project", p)

		if err := s.SyncProjectAddresses(p); err != nil {
			lastErr = err
		}
	}

	if lastErr != nil {
		return lastErr
	}

	return s.Clean(startTime)
}

func SyncGlobal(account string) error {
	s, err := NewSyncer(account)
	if err != nil {
		return err
	}

	// projects := s.GlobalForwardingEnabeldProjects()
	projects := s.AllProjects()
	startTime := time.Now().Unix()

	var errs []error
	for _, p := range projects {
		logger.Info("sync gcp project", "project", p)

		if err := s.SyncProjectGlobalAddresses(p); err != nil {
			errs = append(errs, err)
		}
	}

	if len(errs) > 0 {
		return errs[0]
	}

	// clean empty account that no updates in 12 hours
	return s.Clean(startTime)
}
