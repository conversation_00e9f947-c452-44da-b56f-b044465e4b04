package gcp

import (
	"fmt"

	"google.golang.org/api/option"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/cache"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
)

type FactoryCrendential struct {
	Factory    *models.Factory        `json:"factory"`
	Account    *models.FactoryAccount `json:"account"`
	Credential option.ClientOption    `json:"crendential"`
}

func CreateCredential(secretName string) (option.ClientOption, error) {
	creds, err := restclient.GetCredentialBytes(secretName)
	if err != nil {
		return nil, err
	}

	return option.WithCredentialsJSON(creds), nil
}

func CreateCredentialWithAccount(accountKey string) (*FactoryCrendential, error) {
	var factoryKey string = "gcp"

	if f := cache.GetFactory(factoryKey); f != nil {
		account, err := getAccount(f.GetID(), accountKey)
		if err != nil {
			return nil, err
		}

		cred, err := CreateCredential(account.KMSAccount)
		if err != nil {
			return nil, err
		}

		return &FactoryCrendential{
			Factory:    f,
			Account:    account,
			Credential: cred,
		}, nil
	}

	return nil, fmt.Errorf("unknown factory %s", factoryKey)
}

func getAccount(fid int, accountKey string) (*models.FactoryAccount, error) {
	var account models.FactoryAccount

	has, err := restclient.Find(&account,
		fields.NamedField("key_name", accountKey),
		fields.FactoryField(fid),
	)
	if err != nil {
		return nil, err
	}

	if !has {
		return nil, fmt.Errorf("account %s in factory %d not found", accountKey, fid)
	}

	fmt.Printf("account %+v\n", account)

	return &account, nil
}
