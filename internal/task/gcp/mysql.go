package gcp

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"sync"

	log "gitlab.lilithgame.com/yunwei/pkg/logger"
	"google.golang.org/api/compute/v1"
	"google.golang.org/api/googleapi"
	"google.golang.org/api/option"
	"google.golang.org/api/sqladmin/v1"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/util"
)

type mysqlChanData struct {
	mysqlTotal int32
	mysqlList  []string
}

func MysqlTask(cred []byte, projectList []string, GCP_REG_MAP_FROM_CMDB map[string]int, regionList []*compute.Region, account *models.FactoryAccount) {
	// start := time.Now()
	ALL_PROJECT_MYSQL_LIST := []string{}
	var allProjectMysqlTotal int32
	ch := make(chan mysqlChanData, 100)
	var wg, consume, product sync.WaitGroup
	wg.Add(1)
	consume.Add(len(projectList))
	for _, project := range projectList {
		product.Add(1)
		go func(project string) {
			ALL_MYSQL_LIST := []string{}
			var total int32
			ctx := context.Background()
			sqlService, err := sqladmin.NewService(ctx, option.WithCredentialsJSON(cred))
			if err != nil {
				log.Error(err.Error())
				fmt.Println("sqladmin.NewService Error", err.Error())
				product.Done()
				consume.Done()
				return
			}
			res, err := sqlService.Instances.List(project).Do()
			if err != nil {
				googleError, ok := err.(*googleapi.Error)
				if ok && googleError.Code == 403 {
					detail := err.(*googleapi.Error).Details[1].(map[string]any)
					fmt.Println(project, detail["reason"].(string))
				} else {
					fmt.Println(project, err.Error())
				}
				product.Done()
				consume.Done()
				return
			} else {
				tiersList, err := sqlService.Tiers.List(project).Context(ctx).Do()
				if err != nil {
					log.Error("list tier failed", "project", project, "err", err.Error())
					fmt.Println("sqlService.Tiers.List Error", err)
				}

				for _, instance := range res.Items {
					if instanceType := instance.InstanceType; instanceType != "CLOUD_SQL_INSTANCE" && instanceType != "READ_REPLICA_INSTANCE" {
						log.Warn("db instance tier is invalid",
							"project", project,
							"name", instance.Name,
							"tier", instance.Settings.Tier,
							"type", instanceType,
							"created_at", instance.CreateTime,
						)
						continue
					}

					cpu := 0
					memory := int64(0)

					tiers := strings.Split(instance.Settings.Tier, "-")
					// example tier: "db-custom-1-3840"
					if len(tiers) < 3 {
						log.Warn("db instance tiers slice shoule at least length of 3",
							"name", instance.Name,
							"tiers", tiers,
						)
						continue
					}

					cpu, _ = strconv.Atoi(tiers[len(tiers)-2])
					memoryStr, _ := strconv.Atoi(tiers[len(tiers)-1])
					memory = int64(memoryStr)

					for _, tier := range tiersList.Items {
						if tier.Tier == instance.Settings.Tier {
							if strings.HasSuffix(tier.Tier, "f1-micro") || strings.HasSuffix(tier.Tier, "g1-small") {
								cpu = 1
							} else {
								cpu, _ = strconv.Atoi(strings.Split(tier.Tier, "-")[len(strings.Split(tier.Tier, "-"))-1])
							}
							memory = tier.RAM / (1 << 20) // GiB
						}
					}

					attr := BeautyMySQLWith(instance,
						fields.RegionField(GCP_REG_MAP_FROM_CMDB[instance.Region]),
						fields.FactoryField(account.Factory),
						fields.FactoryAccountField(account.GetID()),
						fields.NumberField("cpu", cpu),
						fields.NumberField("mem", memory),
					)

					cond := []fields.Field{
						*attr.GetField(fields.ExternalUUIDFieldKey),
					}

					restclient.PostOrPatch[models.MySQL](cond, attr)

					total++
					ALL_MYSQL_LIST = append(ALL_MYSQL_LIST, instance.Name)
				}
			}
			mysqlData := mysqlChanData{
				mysqlTotal: total,
				mysqlList:  ALL_MYSQL_LIST,
			}
			ch <- mysqlData
			product.Done()
		}(project)
	}
	go func() {
		defer wg.Done()
		for c := range ch {
			allProjectMysqlTotal += c.mysqlTotal
			ALL_PROJECT_MYSQL_LIST = append(ALL_PROJECT_MYSQL_LIST, c.mysqlList...)
			consume.Done()
		}
	}()
	go func() {
		product.Wait()
		consume.Wait()
		close(ch)
	}()
	wg.Wait()
	// spend := time.Since(start)
	// fmt.Println("Factory Id:", factoryId, ", GCP MySQL total:", allProjectMysqlTotal, ", Spend:", spend)

	// 清理cmdb中无用数据
	// cmdbResult, _ := service.QuerySomething("mysql", "")
	// for _, cmdbResult := range cmdbResult {
	// 	resMap := cmdbResult.(map[string]interface{})
	// 	if resMap["factory"] != float64(factoryId) {
	// 		continue
	// 	}
	// 	mysqlId := resMap["external_uuid"].(string)
	// 	if !util.IsContain(ALL_PROJECT_MYSQL_LIST, mysqlId) {
	// 		resMap["local_status"] = 2
	// 		oma.OmaAction("update", "mysql", resMap)
	// 	}
	// }

	totalPages := 1
	for page := 1; page <= totalPages; page++ {
		pageResult, err := restclient.List[models.MySQL](
			fields.FactoryField(account.Factory),
			fields.PageNumField(page),
		)
		if err != nil {
			log.Error(err.Error())
			return
		}

		for _, item := range pageResult.Results {
			if item.Factory != account.Factory {
				continue
			}

			if accountID := item.Account; accountID != nil && *accountID != account.GetID() {
				continue
			}

			mysqlId := item.ExternalUUID
			if !util.IsContain(ALL_PROJECT_MYSQL_LIST, mysqlId) {
				restclient.Delete(item)
			}
		}

		if pageResult.TotalPages == 0 {
			log.Info("No more pages")
			return
		}

		totalPages = pageResult.TotalPages
	}
}

func BeautyMySQLWith(r *sqladmin.DatabaseInstance, additions ...fields.Field) fields.Fields {
	return BeautyMySQL(r).With(additions...)
}

func BeautyMySQL(r *sqladmin.DatabaseInstance) fields.Fields {
	attr := fields.NewFields(
		fields.ExternalUUIDField(r.Name),
		fields.ExternalNameField(r.Name),
		fields.StringField("conn", r.ConnectionName),
		fields.NumberField("port", 3306),
		fields.NumberField("disk", r.Settings.DataDiskSizeGb),
		fields.StringField("flavor_name", r.InstanceType),
		fields.StringField(fields.CreateTimeFieldKey, r.CreateTime),
	)

	// engine, version and edition if is SQLSERVER
	version := r.DatabaseVersion
	if version != "SQL_DATABASE_VERSION_UNSPECIFIED" {
		ver := strings.Split(version, "_")

		engine := ver[0]
		attr.SetField(fields.LowerStringField("engine", engine))

		switch engine {
		case "MYSQL", "POSTGRES":
			attr.SetField(fields.NamedField("version", strings.Join(ver[1:], ".")))
		case "SQLSERVER":
			attr.With(
				fields.NamedField("version", ver[1]),
				fields.NamedField("edition", ver[2]),
			)
		}
	}

	// rename status, runnable -> running
	status := strings.ToLower(r.State)
	if status == "runnable" {
		status = "running"
	}
	attr.SetField(fields.ExternalStatusField(status))

	return attr
}
