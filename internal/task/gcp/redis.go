package gcp

import (
	"context"
	"fmt"
	"strings"
	"sync"
	"time"

	redis "cloud.google.com/go/redis/apiv1"
	"cloud.google.com/go/redis/apiv1/redispb"
	"github.com/googleapis/gax-go/v2/apierror"
	log "gitlab.lilithgame.com/yunwei/pkg/logger"
	"google.golang.org/api/compute/v1"
	"google.golang.org/api/iterator"
	"google.golang.org/api/option"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/util"
)

type redisChanData struct {
	redisTotal int32
	redisList  []string
}

func RedisTask(cred []byte, projectList []string, GCP_REG_MAP_FROM_CMDB map[string]int, regionList []*compute.Region, factoryId int, accountId int) {
	// start := time.Now()
	ALL_PROJECT_REDIS_LIST := []string{}
	var allProjectRedisTotal int32
	ch := make(chan redisChanData, 100)
	var wg, consume, product sync.WaitGroup
	wg.Add(1)
	consume.Add(len(projectList))
	for _, project := range projectList {
		product.Add(1)
		go func(project string, regionList []*compute.Region) {
			ALL_REDIS_LIST := []string{}
			var total int32
			ctx := context.Background()
			redisService, err := redis.NewCloudRedisClient(ctx, option.WithCredentialsJSON(cred))
			if err != nil {
				log.Error(err.Error())
				fmt.Println("redis.NewCloudRedisClient Error", err.Error())
				product.Done()
				consume.Done()
				return
			}
			if redisService == nil {
				product.Done()
				consume.Done()
				return
			}
			for _, region := range regionList {
				req := &redispb.ListInstancesRequest{
					Parent: fmt.Sprintf("projects/%s/locations/%s", project, region.Name),
				}

				it := redisService.ListInstances(ctx, req)
				for {
					instance, err := it.Next()
					if err == iterator.Done {
						break
					}

					if err != nil {
						googleError, ok := err.(*apierror.APIError)
						if ok && googleError.GRPCStatus().Proto().Code == int32(7) && googleError.Reason() != "" {
							log.Error("list gcp redis instance failed", "project", project, "error", googleError.Reason())
							product.Done()
							consume.Done()
							return
						} else {
							log.Error("List GCP redis Instances Error", "project", project, "error", err.Error())
						}
						break
					}
					redisName := strings.Split(instance.Name, "/")[len(strings.Split(instance.Name, "/"))-1]

					attr := BeautyRedis(instance)
					attr.With(
						fields.FactoryField(factoryId),
						fields.FactoryAccountField(accountId),
						fields.RegionField(GCP_REG_MAP_FROM_CMDB[region.Name]),
					)

					{
						cond := []fields.Field{
							fields.ExternalUUIDField(redisName),
							fields.FactoryAccountField(accountId),
						}

						if _, perr := restclient.PostOrPatch[models.Redis](cond, attr); perr != nil {
							log.Error("sync redis instance failed", "error", perr, "name", redisName)
							continue
						}

						total++
					}
					ALL_REDIS_LIST = append(ALL_REDIS_LIST, redisName)
				}
			}

			redisData := redisChanData{
				redisTotal: total,
				redisList:  ALL_REDIS_LIST,
			}
			ch <- redisData
			product.Done()
		}(project, regionList)
	}
	go func() {
		defer wg.Done()
		for c := range ch {
			allProjectRedisTotal += c.redisTotal
			ALL_PROJECT_REDIS_LIST = append(ALL_PROJECT_REDIS_LIST, c.redisList...)
			consume.Done()
		}
	}()
	go func() {
		product.Wait()
		consume.Wait()
		close(ch)
	}()
	wg.Wait()

	// 清理cmdb中无用数据
	if cmdbResultList, err := restclient.ListAll[models.Redis](
		fields.FactoryField(factoryId),
	); err == nil {
		for _, r := range cmdbResultList.Results {
			if r.Factory != factoryId {
				continue
			}
			if !util.IsContain(ALL_PROJECT_REDIS_LIST, r.ExternalUUID) {
				// restclient.PatchAttrs[models.Redis](r fields.NumberStatusField(2))
				restclient.Delete(r)
			}
		}
	}
}

func BeautyRedis(i *redispb.Instance) fields.Fields {
	attr := fields.NewFields(
		fields.NamedField("conn", fmt.Sprintf("%s:%d", i.Host, i.Port)),
		fields.NamedField("port", i.Port),
		fields.NamedField("private_ip", i.Host),
		fields.NamedField("capacity", i.MemorySizeGb),
		fields.NamedField("types", 1),
		fields.CreateTimeField(i.CreateTime.AsTime().Format(time.RFC3339)),
	)

	// name
	instanceName := strings.Split(i.Name, "/")[len(strings.Split(i.Name, "/"))-1]
	attr.With(
		fields.ExternalUUIDField(instanceName),
		fields.ExternalNameField(i.DisplayName),
	)

	// engine and version
	redisVer := strings.Split(i.RedisVersion, "_")
	attr.SetField(fields.TitleStringField("engine", redisVer[0]))

	// version
	ver := strings.ReplaceAll(strings.Join(redisVer[1:], "."), "X", "0")
	attr.SetField(fields.NamedField("version", ver))

	// status
	status := strings.ToLower(i.State.String())
	if status == "ready" {
		status = "running"
	}
	attr.SetField(fields.ExternalStatusField(status))

	return attr
}
