package gcp

import (
	"context"
	"fmt"
	"path/filepath"
	"strings"

	"gitlab.lilithgame.com/yunwei/pkg/logger"
	computev1 "google.golang.org/api/compute/v1"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/cache"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
)

func RegionTask(svc *computev1.Service, projectList []string) ([]*computev1.Region, []*computev1.Zone) {
	var returnList []*computev1.Region
	var returnList2 []*computev1.Zone
	// 获取所有region
	regionLenthFromAPI := 0
	regionTotal := 0

	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	resp, err := svc.Regions.List(projectList[0]).Context(ctx).Do()
	if err != nil {
		logger.Error(err.Error())
		fmt.Println("computeService.Regions.List Error", err)
		return []*computev1.Region{}, []*computev1.Zone{}
	}

	// factory
	f := cache.GetFactory("gcp")
	if f == nil {
		logger.Error("factory gcp not found")
		fmt.Println("computeService.Regions.List Error", err)
		return []*computev1.Region{}, []*computev1.Zone{}
	}

	regionList := resp.Items
	regionLenthFromAPI = len(regionList)
	for _, region := range resp.Items {
		var regionRecord models.Region
		has, err := restclient.Find(&regionRecord,
			fields.RegionIDField(region.Name),
			fields.FactoryField(f.GetID()),
		)
		if err != nil {
			logger.Error("find region error", "error", err, "region", region.Name)
			continue
		}

		regionName := RegionMap[region.Name]
		if regionName == "" {
			regionName = region.Name
		}
		attrs := fields.NewFields(
			fields.StringField("region_id", region.Name),
			fields.StringField("name", regionName),
			fields.FactoryField(f.GetID()),
		)

		if has {
			regionTotal++
			restclient.PatchByID[models.Region](regionRecord.GetID(), attrs)
		} else {
			restclient.Post[models.Region](attrs)
		}

		returnList = append(returnList, region)
	}

	// 所有zone
	zoneLenthFromAPI := 0
	zoneTotal := 0
	res, err := svc.Zones.List(projectList[0]).Context(ctx).Do()
	if err != nil {
		logger.Error(err.Error())
		fmt.Println("computeService.Zones.List Error", err)
		return returnList, []*computev1.Zone{}
	}
	zoneList := res.Items
	zoneLenthFromAPI = len(zoneList)

	findRegion := factoryRegion(f.GetID())

	for _, zone := range zoneList {
		if strings.ToLower(zone.Status) == "down" {
			continue
		}

		var zoneRecord models.Zone
		hasZone, err := restclient.Find(&zoneRecord,
			fields.ZoneIDField(zone.Name),
			fields.FactoryField(f.GetID()),
		)
		if err != nil {
			logger.Error("find zone error", "error", err, "zone", zone.Name)
			continue
		}

		attrs := fields.NewFields(
			fields.ZoneIDField(zone.Name),
			fields.NamedField("name", zone.Name),
			fields.DescField(zone.Description),
			// fields.FactoryField(f.GetID()),
		)

		if r := findRegion(zone.Region); r != nil {
			attrs.With(fields.RegionField(r.GetID()), fields.FactoryField(r.Factory))
		}

		if hasZone {
			zoneTotal++
			_, err = restclient.PatchByID[models.Zone](zoneRecord.GetID(), attrs)
		} else {
			_, err = restclient.Post[models.Zone](attrs)
		}

		if err != nil {
			fmt.Printf("update zone %s, err: %v\n", zone.Name, err)
			logger.Error("sync zone failed", "zone_name", zone, "error", err)
		}

		returnList2 = append(returnList2, zone)
	}

	fmt.Println("GCP Region total from API: ", regionLenthFromAPI)
	fmt.Println("GCP Region total from OMA: ", regionTotal)
	fmt.Println("GCP Zone total from API: ", zoneLenthFromAPI)
	fmt.Println("GCP Zone total from OMA: ", zoneTotal)
	return returnList, returnList2
}

func factoryRegion(factoryID int) func(fullname string) *models.Region {
	tmp := make(map[string]*models.Region)

	return func(fullname string) *models.Region {
		name := filepath.Base(fullname)

		if v, found := tmp[name]; found {
			return v
		}

		if r := findRegion(name, factoryID); r != nil {
			tmp[name] = r
			return r
		}

		return nil
	}
}

func findRegion(name string, factoryID int) *models.Region {
	var region models.Region
	hasRegion, err := restclient.Find(&region,
		fields.RegionIDField(name),
		fields.FactoryField(factoryID),
	)
	if err != nil {
		logger.Error("find region error", "error", err, "region", name)
		return nil
	}

	if !hasRegion {
		logger.Error("region not found", "region", name)
		return nil
	}

	return &region
}
