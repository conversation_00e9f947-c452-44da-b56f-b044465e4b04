package gcp

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"sync"

	log "gitlab.lilithgame.com/yunwei/pkg/logger"
	"google.golang.org/api/compute/v1"
	"google.golang.org/api/googleapi"
	"google.golang.org/api/option"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/util"
)

func VPCTask(
	cred []byte,
	projectList []string,
	GCP_REG_MAP_FROM_CMDB map[string]int,
	regionList []*compute.Region,
	factoryId int,
	accountID int,
) {
	// start := time.Now()
	ALL_PROJECT_VPC_LIST := []string{}
	ALL_REGION_SUBNET_LIST := []string{}

	var allProjectVpcTotal, allRegionSubnetTotal int32
	ch := make(chan vpcChanData, 100)
	var wg, consume, product sync.WaitGroup

	wg.Add(1)
	consume.Add(len(projectList))
	for _, project := range projectList {
		product.Add(1)
		go func(project string, regionList []*compute.Region) {
			ALL_VPC_LIST := []string{}
			ALL_SUBNET_LIST := []string{}
			var vpcTotal, allNumber int32
			ctx, cancel := context.WithCancel(context.Background())
			computeService, err := compute.NewService(ctx, option.WithCredentialsJSON(cred))
			if err != nil {
				log.Error(err.Error())
				fmt.Println("compute.NewService Error", err.Error())
				product.Done()
				consume.Done()
				cancel()
				return
			}
			if computeService == nil {
				product.Done()
				consume.Done()
				cancel()
				return
			}
			resp1, err := computeService.Networks.List(project).Context(ctx).Do()
			if err != nil {
				googleError, ok := err.(*googleapi.Error)
				if ok && googleError.Code == 403 {
					detail := err.(*googleapi.Error).Details[1].(map[string]interface{})
					fmt.Println(project, detail["reason"].(string))
				} else {
					fmt.Println(project, err.Error())
				}
				product.Done()
				consume.Done()
				cancel()
				return
			}
			vpcs := resp1.Items
			for _, vpc := range vpcs {
				vpcName := vpc.Name
				if vpcName == "default" {
					continue
				}
				vpcId := strconv.FormatUint(vpc.Id, 10)
				ALL_VPC_LIST = append(ALL_VPC_LIST, vpcId)

				// 获取防火墙规则
				result, err := computeService.Firewalls.List(project).Context(ctx).Do()
				if err != nil {
					fmt.Println("Firewalls.List Error", err.Error())
					product.Done()
					consume.Done()
					cancel()
					return
				}
				firewalls := result.Items
				for _, firewall := range firewalls {
					if firewall.Network == vpc.SelfLink {
						direction := strings.ToLower(firewall.Direction)
						firewallId := strconv.FormatUint(firewall.Id, 10)

						{
							var record models.Security
							has, err := restclient.Find(&record, fields.NamedField("security_id", firewallId))
							if err != nil {
								log.Errorf("SecurityGroup: " + firewallId + " find error, " + err.Error())
								continue
							}

							firewallAttr := util.SecurityGroupAttrBuilder(
								firewallId,
								firewall.Name,
								firewall.Description,
								vpcCmdbId,
								factoryId,
							)
							if !has {
								restclient.Post[models.Security](firewallAttr)
							} else {
								restclient.PatchByID[models.Security](record.ID, firewallAttr)
							}
						}

						ipCidr := ""
						if len(firewall.SourceRanges) != 0 {
							for _, ip := range firewall.SourceRanges {
								ipCidr += ip + ","
							}
						} else {
							for _, tag := range firewall.SourceTags {
								ipCidr += tag + ","
							}
						}
						ipCidr = strings.TrimRight(ipCidr, ",")
						if ipCidr == "" {
							fmt.Println("123")
						}

						var securityGroupCmdbId int

						var securityGroupCmdbInfo models.Security
						if hasSG, _ := restclient.Find(&securityGroupCmdbInfo, fields.NamedField("security_id", firewallId)); hasSG {
							securityGroupCmdbId = securityGroupCmdbInfo.ID
						}

						// rules - Allowed
						for _, rule := range firewall.Allowed {
							portRange := ""
							if rule.IPProtocol == "all" || rule.IPProtocol == "icmp" {
								portRange = "-1/-1"
							} else {
								for _, port := range rule.Ports {
									portRange += port + ","
								}
							}
							portRange = strings.TrimRight(portRange, ",")
							if portRange == "" {
								portRange = "-1/-1"
							}

							// TODO: fill google sg rules later
							// var ruleRecord models.Rule
							// hasRecord, err := restclient.Find(ruleRecord,
							// 	fields.NamedField("direction", direction),
							// 	fields.NamedField("protocol", strings.ToUpper(rule.IPProtocol)),
							// 	fields.NamedField("port", portRange),
							// 	fields.NamedField("ip", ipCidr),
							// 	fields.NamedField("policy", "Allow"),
							// 	fields.NamedField("priority", strconv.FormatInt(firewall.Priority, 10)),
							// 	fields.NamedField("security__security_id", firewallId),
							// )
							// if err != nil {
							// 	fmt.Println("rule: " + firewallId + " len(cmdbResultList) != 0/1, Check it! " + err.Error())
							// 	continue
							// }

							// ruleAttr := util.SecurityGroupRuleAttrBuilder(
							// 	"",
							// 	"",
							// 	"Allow",
							// 	direction,
							// 	strings.ToUpper(rule.IPProtocol),
							// 	int(firewall.Priority),
							// 	ipCidr,
							// 	portRange,
							// 	securityGroupCmdbId,
							// )
							// if !hasRecord {
							// 	restclient.Post[models.Rule](ruleAttr)
							// } else {
							// 	restclient.PatchByID[models.Rule](ruleRecord.ID, ruleAttr)
							// }
						}

						// rules Deny
						for _, rule := range firewall.Denied {
							portRange := ""
							if rule.IPProtocol == "all" || rule.IPProtocol == "icmp" {
								portRange = "-1/-1"
							} else {
								for _, port := range rule.Ports {
									portRange += port + ","
								}
							}
							portRange = strings.TrimRight(portRange, ",")
							if portRange == "" {
								portRange = "-1/-1"
							}

							var ruleRecord models.Rule
							hasRecord, err := restclient.Find(&ruleRecord,
								fields.NamedField("direction", direction),
								fields.NamedField("protocol", strings.ToUpper(rule.IPProtocol)),
								fields.NamedField("port", portRange),
								fields.NamedField("ip", ipCidr),
								fields.NamedField("policy", "Deny"),
								fields.NamedField("priority", strconv.FormatInt(firewall.Priority, 10)),
								fields.NamedField("security__security_id", firewallId),
							)
							if err != nil {
								fmt.Println("rule: " + firewallId + " len(cmdbResultList) != 0/1, Check it! " + err.Error())
								continue
							}

							ruleAttr := fields.NewFields(
								// fields.NamedField("name", ""),
								// fields.NamedField("rule_id", ""),
								fields.NamedField("policy", "Deny"),
								fields.NamedField("direction", direction),
								fields.NamedField("protocol", strings.ToUpper(rule.IPProtocol)),
								fields.NamedField("priority", firewall.Priority),
								fields.NamedField("ip", ipCidr),
								fields.NamedField("port", portRange),
								fields.NamedField("security", securityGroupCmdbId),
							)
							if !hasRecord {
								restclient.Post[models.Rule](ruleAttr)
							} else {
								restclient.PatchByID[models.Rule](ruleRecord.ID, ruleAttr)
							}
						}
					}
				}
			}

			for _, region := range regionList {
				resp, err := computeService.Subnetworks.List(project, region.Name).Context(ctx).Do()
				if err != nil {
					fmt.Println("computeService.Subnetworks.List Error", err.Error())
					continue
				}
				if len(resp.Items) > 0 {
					for _, subnetwork := range resp.Items {
						vpcName := strings.Split(subnetwork.Network, "/")[len(strings.Split(subnetwork.Network, "/"))-1]
						if vpcName == "default" {
							continue
						}

						var vpcInOMA models.VPC
						if hasVpc, err := restclient.Find(&vpcInOMA, fields.NamedField("name", vpcName)); err != nil || !hasVpc {
							log.Error("find vpc failed", "vpc", vpcName, "error", err)
							continue
						}

						// vpcFromOma, _ := oma.QueryWithKeyArgs("vpc", fields.NamedField("name", vpcName))

						// vpcMap, ok := vpcFromOma[0].(map[string]interface{})
						// if !ok {
						// 	fmt.Println("GCP VPCTask vpc not found: ", vpcName)
						// 	continue
						// }

						// vpcCmdbId := vpcMap["id"].(float64)
						vpcCmdbId := vpcInOMA.ID
						subnetName := subnetwork.Name
						cidr := subnetwork.IpCidrRange
						if strings.HasPrefix(cidr, "172") {
							continue
						}
						subnetId := strconv.FormatUint(subnetwork.Id, 10)

						var subnetRecord models.Subnet
						hasRecord, err := restclient.Find(&subnetRecord, fields.NamedField("subnet_id", subnetId))
						if err != nil {
							log.Errorf("Subnet: " + subnetId + " len(cmdbResultList) != 0/1, Check it! " + err.Error())
							continue
						}

						subnetAttr := util.SubnetAttrBuilder(subnetId, subnetName, "", subnetwork.Description, cidr, false, 0, vpcCmdbId, "")
						if !hasRecord {
							restclient.Post[models.Subnet](subnetAttr)
						} else {
							restclient.PatchByID[models.Subnet](subnetRecord.ID, subnetAttr)
						}

						allNumber++
						ALL_SUBNET_LIST = append(ALL_SUBNET_LIST, subnetId)
					}
				}
			}

			vpcData := vpcChanData{
				vpcTotal:    vpcTotal,
				vpcList:     ALL_VPC_LIST,
				subnetTotal: allNumber,
				subnetList:  ALL_SUBNET_LIST,
			}
			ch <- vpcData
			cancel()
			product.Done()
		}(project, regionList)
	}
	go func() {
		defer wg.Done()
		for c := range ch {
			allProjectVpcTotal += c.vpcTotal
			ALL_PROJECT_VPC_LIST = append(ALL_PROJECT_VPC_LIST, c.vpcList...)
			allRegionSubnetTotal += c.subnetTotal
			ALL_REGION_SUBNET_LIST = append(ALL_REGION_SUBNET_LIST, c.subnetList...)
			consume.Done()
		}
	}()
	go func() {
		product.Wait()
		consume.Wait()
		close(ch)
	}()
	wg.Wait()

	// 清理cmdb中无用数据
	// subnet
	if subnets, err := restclient.ListAll[models.Subnet](
		fields.NamedField("vpc__factory", factoryId),
	); err == nil {
		for _, r := range subnets.Results {
			if !util.IsContain(ALL_REGION_SUBNET_LIST, r.SubnetID) {
				restclient.Delete(r)
			}
		}
	}

	// vpc
	if vpcs, err := restclient.ListAll[models.VPC](
		fields.NamedField("factory", factoryId),
	); err == nil {
		for _, r := range vpcs.Results {
			if !util.IsContain(ALL_PROJECT_VPC_LIST, r.VpcID) {
				// oma.DeleteByID("vpc", r.GetID())
				restclient.Delete(r)
			}
		}
	}
}
