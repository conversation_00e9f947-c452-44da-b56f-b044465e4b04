package redis

import (
	"fmt"
	"time"

	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/awserr"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/elasticache"
	"github.com/fatih/color"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/category"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
	atask "gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/task/aws"
)

type Syncer struct {
	cred    *atask.FactoryCrendential
	regions map[string]models.Region
}

func NewSyncer(factoryKey string) (*Syncer, error) {
	cred, err := atask.CreateCredentialWithAccount(factoryKey)
	if err != nil {
		return nil, err
	}

	return &Syncer{
		cred: cred,
		regions: LoadRegions(
			fields.NamedField("factory__id", cred.Factory.GetID()),
			fields.Unlimited,
		),
	}, nil
}

func (s *Syncer) SyncAll() (err error) {
	for region := range s.regions {
		fmt.Println("syncing region: ", region)
		if serr := s.SyncRegion(region); serr != nil {
			err = serr
		}
	}
	return
}

func (s *Syncer) SyncRegion(region string) error {
	cfg := &aws.Config{
		Credentials: s.cred.Credential,
		Region:      &region,
	}

	sess, err := session.NewSession(cfg)
	if err != nil {
		if aerr, ok := err.(awserr.RequestFailure); ok {
			if aerr.StatusCode() == 403 {
				return nil
			}
		}
		return err
	}

	svc := elasticache.New(sess)
	input := elasticache.DescribeCacheClustersInput{
		MaxRecords:        fields.Int64(100),
		ShowCacheNodeInfo: fields.Pointer(true),
	}

	commonAttr := []fields.Field{
		fields.FactoryField(s.cred.Factory.GetID()),
		fields.FactoryAccountField(s.cred.Account.GetID()),
	}
	for marker, started := "", false; !started || marker != ""; started = true {
		if marker != "" {
			input.SetMarker(marker)
		}

		// replicates, rerr := svc.DescribeReplicationGroups(&elasticache.DescribeReplicationGroupsInput{
		// })
		// for _, regroup := range replicates.ReplicationGroups {
		// 	regroup.ARN
		// }

		resp, err := svc.DescribeCacheClusters(&input)
		if err != nil {
			if aerr, ok := err.(awserr.RequestFailure); ok {
				if aerr.StatusCode() == 403 {
					return nil
				}
			}
			return err
		}

		for _, cc := range resp.CacheClusters {
			fmt.Printf("cluster %+v\n", cc)
			color.Blue("find a cluster %s, engine: %s, nodes count %d", *cc.CacheClusterId, *cc.Engine, *cc.NumCacheNodes)
			if engine := fields.PLowerString(cc.Engine); engine == "memcached" {
				continue
			}

			clusterAttr := fields.FieldList(
				fields.TitleStringPField("engine", cc.Engine),
				fields.StringPField("version", cc.EngineVersion),
				fields.NumberField("types", 0), // 集群版
			)

			// region
			if reg, ok := s.regions[region]; ok {
				clusterAttr = append(clusterAttr, fields.RegionField(reg.GetID()))
			}

			//  尝试获取 product
			if clusterId := fields.Value(cc.CacheClusterId); clusterId != "" {
				if productId := category.Category(clusterId, ""); productId != nil {
					clusterAttr = append(clusterAttr, fields.ProductField(*productId))
				} else {
					if productId = category.GetMatchedProductID(s.cred.Account.Channel); productId != nil {
						clusterAttr = append(clusterAttr, fields.ProductField(*productId))
					}
				}
			}

			for _, cnode := range cc.CacheNodes {
				nodeId := fmt.Sprintf("%s-%s", *cc.CacheClusterId, *cnode.CacheNodeId)

				attr := Beauty(cnode).
					With(commonAttr...).
					With(clusterAttr...).
					With(fields.ExternalNameField(*cc.CacheClusterId)).
					With(
						fields.ExternalUUIDField(nodeId),
						fields.ExternalNameField(nodeId),
					)

				color.Cyan("reids node attrs: %+v", attr)

				cond := fields.FieldList(
					*attr.GetField(fields.ExternalUUIDFieldKey),
				)

				if _, perr := restclient.PostOrPatch[models.Redis](cond, attr); perr != nil {
					color.Red("sync redis error: %+v", perr)
				} else {
					color.Green("sync ok redis: %+v", attr)
				}
			}
		}

		marker = fields.Value(resp.Marker)
	}

	return err
}

func Sync(account string) error {
	s, err := NewSyncer(account)
	if err != nil {
		return err
	}

	startAt := time.Now().Unix()
	if err = s.SyncAll(); err != nil {
		return err
	}

	return s.Clean(startAt)
}
