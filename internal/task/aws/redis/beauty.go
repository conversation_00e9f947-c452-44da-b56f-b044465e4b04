package redis

import (
	"time"

	"github.com/aws/aws-sdk-go/service/elasticache"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
)

func BeautyWith(n *elasticache.CacheNode, extras ...fields.Field) fields.Fields {
	return Beauty(n).With(extras...)
}

func Beauty(n *elasticache.CacheNode) fields.Fields {
	attr := fields.NewFields(
		fields.StringPField(fields.ExternalUUIDFieldKey, n.CacheNodeId),
		fields.StringPField("conn", n.Endpoint.Address),
		fields.NumberPField("port", n.Endpoint.Port),
		fields.CreateTimeField(n.CacheNodeCreateTime.Format(time.RFC3339)),
	)

	// rename status
	status := fields.PLowerString(n.CacheNodeStatus)
	if status == "available" {
		status = "running"
	}
	attr.SetField(fields.ExternalStatusField(status))

	return attr
}
