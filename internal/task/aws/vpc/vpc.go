package vpc

import (
	"time"

	"github.com/aws/aws-sdk-go/service/ec2"
	"github.com/fatih/color"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
)

func (s *Syncer) SyncVPCInRegionAndClean(svc *ec2.EC2, region *models.Region) (err error) {
	start := time.Now().Unix()

	err = s.SyncVPC(svc)
	if err == nil {
		s.CleanOutdatedVPC(start, fields.NamedField("region__id", region.GetID()))
	}
	return
}

func (s *Syncer) SyncVPC(svc *ec2.EC2) (err error) {
	factoryAttrs := fields.FieldList(
		fields.FactoryField(s.cred.Factory.GetID()),
		fields.FactoryAccountField(s.cred.Account.GetID()),
	)

	input := &ec2.DescribeVpcsInput{
		MaxResults: fields.Int64(100),
	}
	for token, started := "", false; token != "" || !started; started = true {
		if token != "" {
			input.SetNextToken(token)
		}

		outputs, derr := svc.DescribeVpcs(input)
		if derr != nil {
			return derr
		}
		token = fields.Value(outputs.NextToken)

		for _, vpcObject := range outputs.Vpcs {
			attrs := BeautyVPC(vpcObject).With(factoryAttrs...)

			conds := fields.FieldList(
				*attrs.GetField("vpc_id"),
			)

			if v, perr := restclient.PostOrPatch[models.VPC](conds, attrs); perr != nil {
				err = perr
			} else {
				color.Green("  sync vpc %s ok", v.VpcID)
			}
		}
	}

	return
}
