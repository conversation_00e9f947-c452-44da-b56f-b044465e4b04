package vpc

import (
	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/ec2"
	"github.com/fatih/color"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
	task "gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/task/aws"
)

type Syncer struct {
	cred    *task.FactoryCrendential
	regions map[string]models.Region
	zones   map[string]models.Zone
}

func NewSyncer(factoryKey string) (*Syncer, error) {
	cred, err := task.CreateCredentialWithAccount(factoryKey)
	if err != nil {
		return nil, err
	}

	return &Syncer{
		cred: cred,
		// TODO: use cache
		zones: LoadZones(
			fields.NamedField("factory__id", cred.Factory.GetID()),
			fields.Unlimited,
		),
		regions: LoadRegions(
			fields.NamedField("factory__id", cred.Factory.GetID()),
			fields.Unlimited,
		), // TODO: load zones by vendor is better
	}, nil
}

// Sync sync all resource in across all regions
func (s *Syncer) Sync() (err error) {
	for _, region := range s.regions {
		color.Green("syncing region %s", region.Name)
		if serr := s.SyncRegion(&region); serr != nil {
			err = serr
		}
	}
	return
}

func (s *Syncer) SyncRegion(region *models.Region) error {
	cfg := &aws.Config{
		Credentials: s.cred.Credential,
		Region:      &region.RegionID,
	}

	sess, err := session.NewSession(cfg)
	if err != nil {
		return err
	}

	svc := ec2.New(sess)

	s.SyncVPCInRegionAndClean(svc, region)

	return err
}
