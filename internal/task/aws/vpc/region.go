package vpc

import (
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
)

func LoadRegions(conds ...fields.Field) map[string]models.Region {
	records, err := restclient.ListAll[models.Region](conds...)
	if err != nil {
		panic(err)
	}

	v := make(map[string]models.Region)

	for _, r := range records.Results {
		v[r.RegionID] = r
	}

	return v
}

func LoadZones(conditions ...fields.Field) map[string]models.Zone {
	page, err := restclient.List[models.Zone](conditions...)
	if err != nil {
		panic(err)
	}

	v := make(map[string]models.Zone)

	for _, zone := range page.Results {
		v[zone.Name] = zone
	}

	return v
}
