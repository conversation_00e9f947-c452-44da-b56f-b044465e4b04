package vpc

import (
	"fmt"
	"os"
	"time"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/factory"
)

type BatchSyncer struct {
	syncers []*Syncer
}

func NewBatchSyncer(s ...*Syncer) *BatchSyncer {
	b := &BatchSyncer{
		syncers: make([]*Syncer, 0),
	}

	b.Append(s...)

	return b
}

func NewBatchSyncerWithFactoryKeys[T ~string](keys ...T) *BatchSyncer {
	batcher := NewBatchSyncer()

	for _, key := range keys {
		if s, err := NewSyncer(string(key)); err == nil {
			batcher.Append(s)
		} else {
			fmt.Fprintf(os.Stderr, "initialize aws ec2 syncer for factory %s error: %v\n", key, err)
		}
	}

	return batcher
}

func NewDefaultBatcherSyncer() *BatchSyncer {
	return NewBatchSyncerWithFactoryKeys(
		factory.AWSFarlight,
		factory.AWSSamo,
		factory.AWSPlat,
		factory.AWSPlat2,
		factory.AWSMona,
		factory.AWSAd,
	)
}

func (bs *BatchSyncer) Len() int {
	return len(bs.syncers)
}

func (bs *BatchSyncer) Append(ss ...*Syncer) {
	if len(ss) > 0 {
		bs.syncers = append(bs.syncers, ss...)
	}
}

func (bs *BatchSyncer) Sync() error {
	var err error

	for _, s := range bs.syncers {
		startAt := time.Now().Unix()

		if serr := s.Sync(); serr != nil {
			err = serr
		} else {
			s.CleanOutdatedVPC(startAt)
		}
	}

	return err
}
