package vpc

import (
	"github.com/aws/aws-sdk-go/service/ec2"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
)

func EC2TagAsFields(i *ec2.Instance) fields.Fields {
	fs := fields.NewFields()

	if tags := i.Tags; len(tags) > 0 {
		for _, tag := range tags {
			fs.SetPString(tag.Key, tag.Value)
		}
	}

	return fs
}

func BeautyVPC(i *ec2.Vpc) fields.Fields {
	attrs := fields.NewFields(
		fields.StringPField("vpc_id", i.VpcId),
		fields.StringPField("cidr_block", i.CidrBlock),
		fields.StringPField("status", i.State),
	)

	// resolve name from tag
	for _, tag := range i.Tags {
		if fields.PLowerString(tag.Key) == "name" {
			attrs.WithString("name", tag.Value)
		}
	}

	return attrs
}
