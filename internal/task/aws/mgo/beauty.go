package mgo

import (
	"time"

	"github.com/aws/aws-sdk-go/service/rds"
	"github.com/fatih/color"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/category"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
)

func BeautyWith(i *rds.DBInstance, additions ...fields.Field) fields.Fields {
	return Beauty(i).With(additions...)
}

func Beauty(r *rds.DBInstance) fields.Fields {
	attr := fields.NewFields(
		fields.StringPField(fields.ExternalUUIDFieldKey, r.DbiResourceId),
		fields.StringPField(fields.ExternalNameFieldKey, r.DBInstanceIdentifier),
		fields.LowerStringPField("engine", r.Engine),
		fields.StringPField("version", r.EngineVersion),
		fields.StringPField("primary_conn", r.Endpoint.Address),
		fields.NumberPField("primary_port", r.Endpoint.Port),
		fields.NumberPField("iops", r.Iops),
		fields.NumberPField("disk", r.AllocatedStorage),
		fields.NamedField("db_type", nil),
		fields.StringField(fields.CreateTimeFieldKey, r.InstanceCreateTime.Format(time.RFC3339)),
	)

	// external_tags
	if tags := r.TagList; len(tags) > 0 {
		tagList := make([]map[string]any, 0)
		for _, tag := range tags {
			tagList = append(tagList, map[string]any{
				"k": *tag.Key,
				"v": *tag.Value,
			})
		}
		attr.SetJSON(fields.ExternalTagsFieldKey, tagList)
	}

	// rename status: available -> running
	status := fields.PLowerString(r.DBInstanceStatus)
	if status == "available" {
		status = "running"
	}
	attr.SetField(fields.ExternalStatusField(status))

	// spec
	if spec := GetMongoSpec(*r.DBInstanceClass); spec != nil {
		attr.With(
			fields.NamedField("cpu", spec.CPU),
			fields.NamedField("mem", spec.Memory),
		)
	} else {
		color.Red("spec not found for %s", *r.DBInstanceClass)
	}

	// product category from name
	if name := attr.GetString(fields.ExternalNameFieldKey); name == "" {
		if productId := category.Category(name, ""); productId != nil {
			attr.With(fields.ProductField(*productId))
		}
	}

	return attr
}
