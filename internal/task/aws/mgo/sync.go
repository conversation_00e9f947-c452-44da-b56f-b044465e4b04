package mgo

import (
	"fmt"
	"time"

	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/rds"
	"github.com/fatih/color"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/cache"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
	awstask "gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/task/aws"
)

type Syncer struct {
	cred    *awstask.FactoryCrendential
	regions map[string]models.Region
	zones   map[string]models.Zone
}

func NewSyncer(factoryKey string) (*Syncer, error) {
	cred, err := awstask.CreateCredentialWithAccount(factoryKey)
	if err != nil {
		return nil, err
	}

	return &Syncer{
		cred: cred,
		// TODO: use cache
		zones: LoadZones(
			fields.NamedField("factory__id", cred.Factory.GetID()),
			fields.Unlimited,
		),
		regions: LoadRegions(
			fields.NamedField("factory__id", cred.Factory.GetID()),
			fields.Unlimited,
		), // TODO: load zones by vendor is better
	}, nil
}

func (s *Syncer) SyncAll() (err error) {
	for region := range s.regions {
		if serr := s.SyncRegion(region); serr != nil {
			err = serr
		}
	}
	return
}

func (s *Syncer) SyncRegion(region string) error {
	cfg := &aws.Config{
		Credentials: s.cred.Credential,
		Region:      &region,
	}

	sess, err := session.NewSession(cfg)
	if err != nil {
		return err
	}

	svc := rds.New(sess)
	input := rds.DescribeDBInstancesInput{
		MaxRecords: aws.Int64(100),
		Filters: []*rds.Filter{
			{Name: aws.String("engine"), Values: aws.StringSlice([]string{"docdb"})},
		},
	}

	commonAttr := []fields.Field{
		fields.FactoryField(s.cred.Factory.GetID()),
		fields.FactoryAccountField(s.cred.Account.GetID()),
	}

	for marker, started := "", false; !started || marker != ""; started = true {
		if marker != "" {
			input.SetMarker(marker)
		}

		resp, err := svc.DescribeDBInstances(&input)
		if err != nil {
			return err
		}

		for _, r := range resp.DBInstances {
			attr := BeautyWith(r, commonAttr...)

			// region
			if zone := cache.GetFactoryZoneName(s.cred.Factory.GetID(), *r.AvailabilityZone); zone != nil {
				attr.SetField(fields.RegionField(zone.Region))
			} else {
				color.Red("zone not found: %s in factory %d", *r.AvailabilityZone, s.cred.Factory.GetID())
			}

			cond := []fields.Field{
				*attr.GetField(fields.ExternalUUIDFieldKey),
			}

			if _, perr := restclient.PostOrPatch[models.Mongo](cond, attr); perr != nil {
				color.Red("sync mongo error: %+v", perr)
			} else {
				fmt.Printf("sync ok Mongo: %+v\n", attr)
			}
		}

		marker = fields.Value(resp.Marker)
	}

	return err
}

func Sync(accountKey string) error {
	s, err := NewSyncer(accountKey)
	if err != nil {
		return err
	}

	startAt := time.Now().Unix()
	if err = s.SyncAll(); err != nil {
		return err
	}

	return s.Clean(startAt)
}
