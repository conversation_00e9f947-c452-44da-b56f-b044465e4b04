package mgo

// ref: https://docs.aws.amazon.com/zh_cn/AmazonRDS/latest/UserGuide/Concepts.DBInstanceClass.html#Concepts.DBInstanceClass.Support

type MongoSpec struct {
	CPU    int
	Memory int
	Name   string
}

// 单节点实例规格 standalone-instance-types
var MongoInstanceTypes = map[string]MongoSpec{
	"db.m7g.16xlarge":           {64, 256, "db.m7g.16xlarge"},
	"db.m7g.12xlarge":           {48, 192, "db.m7g.12xlarge"},
	"db.m7g.8xlarge":            {32, 128, "db.m7g.8xlarge"},
	"db.m7g.4xlarge":            {16, 64, "db.m7g.4xlarge"},
	"db.m7g.2xlarge":            {8, 32, "db.m7g.2xlarge"},
	"db.m7g.xlarge":             {4, 16, "db.m7g.xlarge"},
	"db.m7g.large":              {2, 8, "db.m7g.large"},
	"db.m6g.16xlarge":           {64, 256, "db.m6g.16xlarge"},
	"db.m6g.12xlarge":           {48, 192, "db.m6g.12xlarge"},
	"db.m6g.8xlarge":            {32, 128, "db.m6g.8xlarge"},
	"db.m6g.4xlarge":            {16, 64, "db.m6g.4xlarge"},
	"db.m6g.2xlarge":            {8, 32, "db.m6g.2xlarge"},
	"db.m6g.xlarge":             {4, 16, "db.m6g.xlarge"},
	"db.m6g.large":              {2, 8, "db.m6g.large"},
	"db.m6gd.16xlarge":          {64, 256, "db.m6gd.16xlarge"},
	"db.m6gd.12xlarge":          {48, 192, "db.m6gd.12xlarge"},
	"db.m6gd.8xlarge":           {32, 128, "db.m6gd.8xlarge"},
	"db.m6gd.4xlarge":           {16, 64, "db.m6gd.4xlarge"},
	"db.m6gd.2xlarge":           {8, 32, "db.m6gd.2xlarge"},
	"db.m6gd.xlarge":            {4, 16, "db.m6gd.xlarge"},
	"db.m6gd.large":             {2, 8, "db.m6gd.large"},
	"db.m6id.32xlarge":          {128, 512, "db.m6id.32xlarge"},
	"db.m6id.24xlarge":          {96, 384, "db.m6id.24xlarge"},
	"db.m6id.16xlarge":          {64, 256, "db.m6id.16xlarge"},
	"db.m6id.12xlarge":          {48, 192, "db.m6id.12xlarge"},
	"db.m6id.8xlarge":           {32, 128, "db.m6id.8xlarge"},
	"db.m6id.4xlarge":           {16, 64, "db.m6id.4xlarge"},
	"db.m6id.2xlarge":           {8, 32, "db.m6id.2xlarge"},
	"db.m6id.xlarge":            {4, 16, "db.m6id.xlarge"},
	"db.m6id.large":             {2, 8, "db.m6id.large"},
	"db.m6idn.32xlarge":         {128, 512, "db.m6idn.32xlarge"},
	"db.m6idn.24xlarge":         {96, 384, "db.m6idn.24xlarge"},
	"db.m6idn.16xlarge":         {64, 256, "db.m6idn.16xlarge"},
	"db.m6idn.12xlarge":         {48, 192, "db.m6idn.12xlarge"},
	"db.m6idn.8xlarge":          {32, 128, "db.m6idn.8xlarge"},
	"db.m6idn.4xlarge":          {16, 64, "db.m6idn.4xlarge"},
	"db.m6idn.2xlarge":          {8, 32, "db.m6idn.2xlarge"},
	"db.m6idn.xlarge":           {4, 16, "db.m6idn.xlarge"},
	"db.m6idn.large":            {2, 8, "db.m6idn.large"},
	"db.m6in.32xlarge":          {128, 512, "db.m6in.32xlarge"},
	"db.m6in.24xlarge":          {96, 384, "db.m6in.24xlarge"},
	"db.m6in.16xlarge":          {64, 256, "db.m6in.16xlarge"},
	"db.m6in.12xlarge":          {48, 192, "db.m6in.12xlarge"},
	"db.m6in.8xlarge":           {32, 128, "db.m6in.8xlarge"},
	"db.m6in.4xlarge":           {16, 64, "db.m6in.4xlarge"},
	"db.m6in.2xlarge":           {8, 32, "db.m6in.2xlarge"},
	"db.m6in.xlarge":            {4, 16, "db.m6in.xlarge"},
	"db.m6in.large":             {2, 8, "db.m6in.large"},
	"db.m6i.32xlarge":           {128, 512, "db.m6i.32xlarge"},
	"db.m6i.24xlarge":           {96, 384, "db.m6i.24xlarge"},
	"db.m6i.16xlarge":           {64, 256, "db.m6i.16xlarge"},
	"db.m6i.12xlarge":           {48, 192, "db.m6i.12xlarge"},
	"db.m6i.8xlarge":            {32, 128, "db.m6i.8xlarge"},
	"db.m6i.4xlarge":            {16, 64, "db.m6i.4xlarge"},
	"db.m6i.2xlarge":            {8, 32, "db.m6i.2xlarge"},
	"db.m6i.xlarge":             {4, 16, "db.m6i.xlarge"},
	"db.m6i.large":              {2, 8, "db.m6i.large"},
	"db.m5d.24xlarge":           {96, 384, "db.m5d.24xlarge"},
	"db.m5d.16xlarge":           {64, 256, "db.m5d.16xlarge"},
	"db.m5d.12xlarge":           {48, 192, "db.m5d.12xlarge"},
	"db.m5d.8xlarge":            {32, 128, "db.m5d.8xlarge"},
	"db.m5d.4xlarge":            {16, 64, "db.m5d.4xlarge"},
	"db.m5d.2xlarge":            {8, 32, "db.m5d.2xlarge"},
	"db.m5d.xlarge":             {4, 16, "db.m5d.xlarge"},
	"db.m5d.large":              {2, 8, "db.m5d.large"},
	"db.m5.24xlarge":            {96, 384, "db.m5.24xlarge"},
	"db.m5.16xlarge":            {64, 256, "db.m5.16xlarge"},
	"db.m5.12xlarge":            {48, 192, "db.m5.12xlarge"},
	"db.m5.8xlarge":             {32, 128, "db.m5.8xlarge"},
	"db.m5.4xlarge":             {16, 64, "db.m5.4xlarge"},
	"db.m5.2xlarge":             {8, 32, "db.m5.2xlarge"},
	"db.m5.xlarge":              {4, 16, "db.m5.xlarge"},
	"db.m5.large":               {2, 8, "db.m5.large"},
	"db.m4.16xlarge":            {64, 256, "db.m4.16xlarge"},
	"db.m4.10xlarge":            {40, 160, "db.m4.10xlarge"},
	"db.m4.4xlarge":             {16, 64, "db.m4.4xlarge"},
	"db.m4.2xlarge":             {8, 32, "db.m4.2xlarge"},
	"db.m4.xlarge":              {4, 16, "db.m4.xlarge"},
	"db.m4.large":               {2, 8, "db.m4.large"},
	"db.m3.2xlarge":             {8, 30, "db.m3.2xlarge"},
	"db.m3.xlarge":              {4, 15, "db.m3.xlarge"},
	"db.m3.large":               {2, 8, "db.m3.large"},
	"db.m3.medium":              {1, 4, "db.m3.medium"},
	"db.m1.xlarge":              {4, 15, "db.m1.xlarge"},
	"db.m1.large":               {2, 8, "db.m1.large"},
	"db.m1.medium":              {1, 4, "db.m1.medium"},
	"db.m1.small":               {1, 2, "db.m1.small"},
	"db.x2iezn.12xlarge":        {48, 1536, "db.x2iezn.12xlarge"},
	"db.x2iezn.8xlarge":         {32, 1024, "db.x2iezn.8xlarge"},
	"db.x2iezn.6xlarge":         {24, 768, "db.x2iezn.6xlarge"},
	"db.x2iezn.4xlarge":         {16, 512, "db.x2iezn.4xlarge"},
	"db.x2iezn.2xlarge":         {8, 256, "db.x2iezn.2xlarge"},
	"db.x2iedn.32xlarge":        {128, 4096, "db.x2iedn.32xlarge"},
	"db.x2iedn.24xlarge":        {96, 3072, "db.x2iedn.24xlarge"},
	"db.x2iedn.16xlarge":        {64, 2048, "db.x2iedn.16xlarge"},
	"db.x2iedn.8xlarge":         {32, 1024, "db.x2iedn.8xlarge"},
	"db.x2iedn.4xlarge":         {16, 512, "db.x2iedn.4xlarge"},
	"db.x2iedn.2xlarge":         {8, 256, "db.x2iedn.2xlarge"},
	"db.x2iedn.xlarge":          {4, 128, "db.x2iedn.xlarge"},
	"db.x2idn.32xlarge":         {128, 2048, "db.x2idn.32xlarge"},
	"db.x2idn.24xlarge":         {96, 1536, "db.x2idn.24xlarge"},
	"db.x2idn.16xlarge":         {64, 1024, "db.x2idn.16xlarge"},
	"db.x2g.16xlarge":           {64, 1024, "db.x2g.16xlarge"},
	"db.x2g.12xlarge":           {48, 768, "db.x2g.12xlarge"},
	"db.x2g.8xlarge":            {32, 512, "db.x2g.8xlarge"},
	"db.x2g.4xlarge":            {16, 256, "db.x2g.4xlarge"},
	"db.x2g.2xlarge":            {8, 128, "db.x2g.2xlarge"},
	"db.x2g.xlarge":             {4, 64, "db.x2g.xlarge"},
	"db.x2g.large":              {2, 32, "db.x2g.large"},
	"db.z1d.12xlarge":           {48, 384, "db.z1d.12xlarge"},
	"db.z1d.6xlarge":            {24, 192, "db.z1d.6xlarge"},
	"db.z1d.3xlarge":            {12, 96, "db.z1d.3xlarge"},
	"db.z1d.2xlarge":            {8, 64, "db.z1d.2xlarge"},
	"db.z1d.xlarge":             {4, 32, "db.z1d.xlarge"},
	"db.z1d.large":              {2, 16, "db.z1d.large"},
	"db.x1e.32xlarge":           {128, 3904, "db.x1e.32xlarge"},
	"db.x1e.16xlarge":           {64, 1952, "db.x1e.16xlarge"},
	"db.x1e.8xlarge":            {32, 976, "db.x1e.8xlarge"},
	"db.x1e.4xlarge":            {16, 488, "db.x1e.4xlarge"},
	"db.x1e.2xlarge":            {8, 244, "db.x1e.2xlarge"},
	"db.x1e.xlarge":             {4, 122, "db.x1e.xlarge"},
	"db.x1.32xlarge":            {128, 1952, "db.x1.32xlarge"},
	"db.x1.16xlarge":            {64, 976, "db.x1.16xlarge"},
	"db.r7g.16xlarge":           {64, 512, "db.r7g.16xlarge"},
	"db.r7g.12xlarge":           {48, 384, "db.r7g.12xlarge"},
	"db.r7g.8xlarge":            {32, 256, "db.r7g.8xlarge"},
	"db.r7g.4xlarge":            {16, 128, "db.r7g.4xlarge"},
	"db.r7g.2xlarge":            {8, 64, "db.r7g.2xlarge"},
	"db.r7g.xlarge":             {4, 32, "db.r7g.xlarge"},
	"db.r7g.large":              {2, 16, "db.r7g.large"},
	"db.r6g.16xlarge":           {64, 512, "db.r6g.16xlarge"},
	"db.r6g.12xlarge":           {48, 384, "db.r6g.12xlarge"},
	"db.r6g.8xlarge":            {32, 256, "db.r6g.8xlarge"},
	"db.r6g.4xlarge":            {16, 128, "db.r6g.4xlarge"},
	"db.r6g.2xlarge":            {8, 64, "db.r6g.2xlarge"},
	"db.r6g.xlarge":             {4, 32, "db.r6g.xlarge"},
	"db.r6g.large":              {2, 16, "db.r6g.large"},
	"db.r6gd.16xlarge":          {64, 512, "db.r6gd.16xlarge"},
	"db.r6gd.12xlarge":          {48, 384, "db.r6gd.12xlarge"},
	"db.r6gd.8xlarge":           {32, 256, "db.r6gd.8xlarge"},
	"db.r6gd.4xlarge":           {16, 128, "db.r6gd.4xlarge"},
	"db.r6gd.2xlarge":           {8, 64, "db.r6gd.2xlarge"},
	"db.r6gd.xlarge":            {4, 32, "db.r6gd.xlarge"},
	"db.r6gd.large":             {2, 16, "db.r6gd.large"},
	"db.r6id.32xlarge":          {128, 1024, "db.r6id.32xlarge"},
	"db.r6id.24xlarge":          {96, 768, "db.r6id.24xlarge"},
	"db.r6id.16xlarge":          {64, 512, "db.r6id.16xlarge"},
	"db.r6id.12xlarge":          {48, 384, "db.r6id.12xlarge"},
	"db.r6id.8xlarge":           {32, 256, "db.r6id.8xlarge"},
	"db.r6id.4xlarge":           {16, 128, "db.r6id.4xlarge"},
	"db.r6id.2xlarge":           {8, 64, "db.r6id.2xlarge"},
	"db.r6id.xlarge":            {4, 32, "db.r6id.xlarge"},
	"db.r6id.large":             {2, 16, "db.r6id.large"},
	"db.r6idn.32xlarge":         {128, 1024, "db.r6idn.32xlarge"},
	"db.r6idn.24xlarge":         {96, 768, "db.r6idn.24xlarge"},
	"db.r6idn.16xlarge":         {64, 512, "db.r6idn.16xlarge"},
	"db.r6idn.12xlarge":         {48, 384, "db.r6idn.12xlarge"},
	"db.r6idn.8xlarge":          {32, 256, "db.r6idn.8xlarge"},
	"db.r6idn.4xlarge":          {16, 128, "db.r6idn.4xlarge"},
	"db.r6idn.2xlarge":          {8, 64, "db.r6idn.2xlarge"},
	"db.r6idn.xlarge":           {4, 32, "db.r6idn.xlarge"},
	"db.r6idn.large":            {2, 16, "db.r6idn.large"},
	"db.r6in.32xlarge":          {128, 1024, "db.r6in.32xlarge"},
	"db.r6in.24xlarge":          {96, 768, "db.r6in.24xlarge"},
	"db.r6in.16xlarge":          {64, 512, "db.r6in.16xlarge"},
	"db.r6in.12xlarge":          {48, 384, "db.r6in.12xlarge"},
	"db.r6in.8xlarge":           {32, 256, "db.r6in.8xlarge"},
	"db.r6in.4xlarge":           {16, 128, "db.r6in.4xlarge"},
	"db.r6in.2xlarge":           {8, 64, "db.r6in.2xlarge"},
	"db.r6in.xlarge":            {4, 32, "db.r6in.xlarge"},
	"db.r6in.large":             {2, 16, "db.r6in.large"},
	"db.r6i.32xlarge":           {128, 1024, "db.r6i.32xlarge"},
	"db.r6i.24xlarge":           {96, 768, "db.r6i.24xlarge"},
	"db.r6i.16xlarge":           {64, 512, "db.r6i.16xlarge"},
	"db.r6i.12xlarge":           {48, 384, "db.r6i.12xlarge"},
	"db.r6i.8xlarge":            {32, 256, "db.r6i.8xlarge"},
	"db.r6i.4xlarge":            {16, 128, "db.r6i.4xlarge"},
	"db.r6i.2xlarge":            {8, 64, "db.r6i.2xlarge"},
	"db.r6i.xlarge":             {4, 32, "db.r6i.xlarge"},
	"db.r6i.large":              {2, 16, "db.r6i.large"},
	"db.r5d.24xlarge":           {96, 768, "db.r5d.24xlarge"},
	"db.r5d.16xlarge":           {64, 512, "db.r5d.16xlarge"},
	"db.r5d.12xlarge":           {48, 384, "db.r5d.12xlarge"},
	"db.r5d.8xlarge":            {32, 256, "db.r5d.8xlarge"},
	"db.r5d.4xlarge":            {16, 128, "db.r5d.4xlarge"},
	"db.r5d.2xlarge":            {8, 64, "db.r5d.2xlarge"},
	"db.r5d.xlarge":             {4, 32, "db.r5d.xlarge"},
	"db.r5d.large":              {2, 16, "db.r5d.large"},
	"db.r5b.24xlarge":           {96, 768, "db.r5b.24xlarge"},
	"db.r5b.16xlarge":           {64, 512, "db.r5b.16xlarge"},
	"db.r5b.12xlarge":           {48, 384, "db.r5b.12xlarge"},
	"db.r5b.8xlarge":            {32, 256, "db.r5b.8xlarge"},
	"db.r5b.4xlarge":            {16, 128, "db.r5b.4xlarge"},
	"db.r5b.2xlarge":            {8, 64, "db.r5b.2xlarge"},
	"db.r5b.xlarge":             {4, 32, "db.r5b.xlarge"},
	"db.r5b.large":              {2, 16, "db.r5b.large"},
	"db.r5b.8xlarge.tpc2.mem3x": {32, 768, "db.r5b.8xlarge.tpc2.mem3x"},
	"db.r5b.6xlarge.tpc2.mem4x": {24, 768, "db.r5b.6xlarge.tpc2.mem4x"},
	"db.r5b.4xlarge.tpc2.mem4x": {16, 512, "db.r5b.4xlarge.tpc2.mem4x"},
	"db.r5b.4xlarge.tpc2.mem3x": {16, 384, "db.r5b.4xlarge.tpc2.mem3x"},
	"db.r5b.4xlarge.tpc2.mem2x": {16, 256, "db.r5b.4xlarge.tpc2.mem2x"},
	"db.r5b.2xlarge.tpc2.mem8x": {8, 512, "db.r5b.2xlarge.tpc2.mem8x"},
	"db.r5b.2xlarge.tpc2.mem4x": {8, 256, "db.r5b.2xlarge.tpc2.mem4x"},
	"db.r5b.2xlarge.tpc1.mem2x": {8, 128, "db.r5b.2xlarge.tpc1.mem2x"},
	"db.r5b.xlarge.tpc2.mem4x":  {4, 128, "db.r5b.xlarge.tpc2.mem4x"},
	"db.r5b.xlarge.tpc2.mem2x":  {4, 64, "db.r5b.xlarge.tpc2.mem2x"},
	"db.r5b.large.tpc1.mem2x":   {2, 32, "db.r5b.large.tpc1.mem2x"},
	"db.r5.24xlarge":            {96, 768, "db.r5.24xlarge"},
	"db.r5.16xlarge":            {64, 512, "db.r5.16xlarge"},
	"db.r5.12xlarge":            {48, 384, "db.r5.12xlarge"},
	"db.r5.8xlarge":             {32, 256, "db.r5.8xlarge"},
	"db.r5.4xlarge":             {16, 128, "db.r5.4xlarge"},
	"db.r5.2xlarge":             {8, 64, "db.r5.2xlarge"},
	"db.r5.xlarge":              {4, 32, "db.r5.xlarge"},
	"db.r5.large":               {2, 16, "db.r5.large"},
	"db.r5.12xlarge.tpc2.mem2x": {48, 768, "db.r5.12xlarge.tpc2.mem2x"},
	"db.r5.8xlarge.tpc2.mem3x":  {32, 768, "db.r5.8xlarge.tpc2.mem3x"},
	"db.r5.6xlarge.tpc2.mem4x":  {24, 768, "db.r5.6xlarge.tpc2.mem4x"},
	"db.r5.4xlarge.tpc2.mem4x":  {16, 512, "db.r5.4xlarge.tpc2.mem4x"},
	"db.r5.4xlarge.tpc2.mem3x":  {16, 384, "db.r5.4xlarge.tpc2.mem3x"},
	"db.r5.4xlarge.tpc2.mem2x":  {16, 256, "db.r5.4xlarge.tpc2.mem2x"},
	"db.r5.2xlarge.tpc2.mem8x":  {8, 512, "db.r5.2xlarge.tpc2.mem8x"},
	"db.r5.2xlarge.tpc2.mem4x":  {8, 256, "db.r5.2xlarge.tpc2.mem4x"},
	"db.r5.2xlarge.tpc1.mem2x":  {8, 128, "db.r5.2xlarge.tpc1.mem2x"},
	"db.r5.xlarge.tpc2.mem4x":   {4, 128, "db.r5.xlarge.tpc2.mem4x"},
	"db.r5.xlarge.tpc2.mem2x":   {4, 64, "db.r5.xlarge.tpc2.mem2x"},
	"db.r5.large.tpc1.mem2x":    {2, 32, "db.r5.large.tpc1.mem2x"},
	"db.r4.16xlarge":            {64, 488, "db.r4.16xlarge"},
	"db.r4.8xlarge":             {32, 244, "db.r4.8xlarge"},
	"db.r4.4xlarge":             {16, 122, "db.r4.4xlarge"},
	"db.r4.2xlarge":             {8, 61, "db.r4.2xlarge"},
	"db.r4.xlarge":              {4, 31, "db.r4.xlarge"},
	"db.r4.large":               {2, 15, "db.r4.large"},
	"db.r3.8xlarge":             {32, 244, "db.r3.8xlarge"},
	"db.r3.4xlarge":             {16, 122, "db.r3.4xlarge"},
	"db.r3.2xlarge":             {8, 61, "db.r3.2xlarge"},
	"db.r3.xlarge":              {4, 31, "db.r3.xlarge"},
	"db.r3.large":               {2, 15, "db.r3.large"},
	"db.t4g.2xlarge":            {8, 32, "db.t4g.2xlarge"},
	"db.t4g.xlarge":             {4, 16, "db.t4g.xlarge"},
	"db.t4g.large":              {2, 8, "db.t4g.large"},
	"db.t4g.medium":             {2, 4, "db.t4g.meidum"},
	"db.t4g.small":              {2, 2, "db.t4g.small"},
	"db.t4g.micro":              {2, 1, "db.t4g.micro"},
	"db.t3.2xlarge":             {8, 32, "db.t3.2xlarge"},
	"db.t3.xlarge":              {4, 16, "db.t3.xlarge"},
	"db.t3.large":               {2, 8, "db.t3.large"},
	"db.t3.medium":              {2, 4, "db.t3.medium"},
	"db.t3.small":               {2, 2, "db.t3.small"},
	"db.t3.micro":               {2, 1, "db.t3.micro"},
	"db.t2.2xlarge":             {8, 32, "db.t2.2xlarge"},
	"db.t2.xlarge":              {4, 16, "db.t2.xlarge"},
	"db.t2.large":               {2, 8, "db.t2.large"},
	"db.t2.medium":              {2, 4, "db.t2.medium"},
	"db.t2.small":               {1, 2, "db.t2.small"},
	"db.t2.micro":               {1, 1, "db.t2.micro"},
}

func GetMongoSpec(class string) *MongoSpec {
	if spec, ok := MongoInstanceTypes[class]; ok {
		return &spec
	}
	return nil
}
