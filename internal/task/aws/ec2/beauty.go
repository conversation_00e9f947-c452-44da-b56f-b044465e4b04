package ec2

import (
	"fmt"
	"strings"
	"time"

	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/service/ec2"
	"gitlab.lilithgame.com/yunwei/cloudmeta"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/category"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/cache"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
)

// BeautyHost return a `fields.Fields` object from a ec2 instance
//
// Fields:
//   - external_uuid
//   - external_name
//   - external_hostname
//   - external_flavor
//   - external_status
//   - os_name
//   - cpu: int32
//   - mem: int32, GiB
//   - private_ip
//   - public_ip
//   - create_time
//   - external_tags
func BeautyHost(i *ec2.Instance, svc *ec2.EC2) fields.Fields {
	attrs := fields.NewFields(
		fields.StringPField(fields.ExternalUUIDFieldKey, i.InstanceId),
		fields.StringPField(fields.PrivateIPFieldKey, i.PrivateIpAddress),
		fields.StringPField(fields.PublicIPFieldKey, i.PublicIpAddress),
		fields.ExternalStatusField(fields.Value(i.State.Name)),
	)

	if tags := EC2TagAsFields(i); len(tags) > 0 {
		tagList := make([]map[string]any, 0)
		for k, v := range tags {
			tagList = append(tagList, map[string]any{
				"k": k,
				"v": v,
			})
		}
		attrs.SetJSON(fields.ExternalTagsFieldKey, tagList)

		// set external name with tag name or external_hostname
		instanceName := tags.GetString("Name")
		if instanceName != "" {
			attrs.Set(fields.ExternalNameFieldKey, instanceName)
		} else {
			attrs.Set(fields.ExternalNameFieldKey, i.PrivateDnsName)
		}
	}

	// hostname
	attrs.SetString(fields.ExternalHostNameFieldKey, i.PrivateDnsName)

	// flavor
	attrs.SetString(fields.ExternalFlavorFieldKey, i.InstanceType)

	// os_name
	if osInfo, err := ec2InstanceOS(svc, *i.ImageId); err == nil {
		attrs.SetString(fields.OSNameFieldKey, osInfo.Name)
	}

	// cpu, mem
	attrs.SetField(fields.CPUField(fields.PNumber(i.CpuOptions.CoreCount)))

	if hostType, err := ec2InstanceType(svc, *i.InstanceType); err == nil {
		attrs.With(
			fields.MemoryField(fields.PNumber(hostType.MemoryInfo.SizeInMiB)/1024), // GiB
			fields.NumberPField(fields.CPUFieldKey, hostType.VCpuInfo.DefaultVCpus),
		)
	}

	// product
	if productID := category.Category(attrs.GetString(fields.ExternalNameFieldKey), ""); productID != nil {
		attrs.Set(fields.ProductFieldKey, fields.PNumber(productID))
	}

	// create time
	var createTime string
	if diskMappins := i.BlockDeviceMappings; len(diskMappins) > 0 {
		for _, m := range diskMappins {
			createTime = m.Ebs.AttachTime.Format(time.RFC3339)
			break
		}
	}
	attrs.SetField(fields.CreateTimeField(createTime))

	return attrs
}

func EC2TagAsFields(i *ec2.Instance) fields.Fields {
	fs := fields.NewFields()

	if tags := i.Tags; len(tags) > 0 {
		for _, tag := range tags {
			fs.SetPString(tag.Key, tag.Value)
		}
	}

	return fs
}

func ec2InstanceOS(svc *ec2.EC2, ami string) (*ec2.Image, error) {
	input := &ec2.DescribeImagesInput{
		ImageIds: []*string{&ami},
	}

	resp, err := svc.DescribeImages(input)
	if err != nil {
		return nil, err
	}

	if types := resp.Images; len(types) > 0 {
		return types[0], nil
	}

	return nil, fmt.Errorf("instance os info for ami %s not found", ami)
}

func ec2InstanceType(svc *ec2.EC2, instanceType string) (*ec2.InstanceTypeInfo, error) {
	input := ec2.DescribeInstanceTypesInput{
		InstanceTypes: []*string{aws.String(instanceType)},
	}
	resp, err := svc.DescribeInstanceTypes(&input)
	if err != nil {
		return nil, err
	}

	if types := resp.InstanceTypes; len(types) > 0 {
		return types[0], nil
	}

	return nil, fmt.Errorf("instance type %s not found", instanceType)
}

func BeautyHostFromMeta(v *cloudmeta.AWSMeta) fields.Fields {
	attrs := fields.NewFields(
		fields.ExternalUUIDField(v.InstanceID),
		fields.ExternalFlavorField(v.InstanceType),
		fields.PrivateIPField(v.PrivateIP),
		fields.ImageIDField(v.ImageID),
		fields.CreateTimeField(v.PendingTime),
	)

	// TODO: hard code fix
	if f := cache.GetFactory("aws"); f != nil {
		attrs.SetField(fields.FactoryField(f.GetID()))

		if zone := cache.GetFactoryZone(f.GetID(), v.Zone); zone != nil {
			attrs.SetField(fields.ZoneField(zone.GetID()))
		}
	}

	return attrs
}

func BeautySecurityGroups(i *ec2.SecurityGroup) fields.Fields {
	attrs := fields.NewFields(
		fields.StringPField("security_id", i.GroupId),
		fields.StringPField("name", i.GroupName),
		fields.StringPField(fields.DescFieldKey, i.Description),
		// fields.NumberPField("available_amount", i.AvailableInstanceAmount),
		// fields.NumberPField("ecs_count", i.EcsCount),
	)

	return attrs
}

func BeautyRule(i *ec2.SecurityGroupRule) fields.Fields {
	attrs := fields.NewFields(
		fields.StringPField("rule_id", i.SecurityGroupRuleId),
		fields.StringField("policy", "ACCEPT"),
		fields.StringPField("description", i.Description),
	)

	// direction & ip port range
	if isEgress := *i.IsEgress; isEgress {
		attrs.With(
			fields.StringField("direction", "egress"),
			fields.StringPField("dest_ip", i.CidrIpv4),
			fields.StringField("dest_port", fmt.Sprintf("%d/%d", *i.FromPort, *i.ToPort)),
			fields.NilField("ip"),
			fields.NilField("port"),
		)
	} else {
		attrs.With(
			fields.StringField("direction", "ingress"),
			fields.StringPField("ip", i.CidrIpv4),
			fields.StringField("port", fmt.Sprintf("%d/%d", *i.FromPort, *i.ToPort)),
			fields.NilField("dest_ip"),
			fields.NilField("dest_port"),
		)
	}

	// protocol
	if protocol := fields.Value(i.IpProtocol); protocol == "-1" {
		attrs.Set("protocol", "ALL")
	} else {
		attrs.Set("protocol", strings.ToUpper(protocol))
	}

	return attrs
}
