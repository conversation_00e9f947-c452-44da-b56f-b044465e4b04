package ec2

import (
	"time"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
)

func (s *Syncer) clean(conds ...fields.Field) (err error) {
	conds = append(conds,
		fields.Not(fields.ExternalStatusFieldKey, fields.ExternalStatusDeleted.Value),
	)
	resp, err := restclient.ListAll[models.Machine](conds...)
	if err != nil {
		return
	}

	for _, m := range resp.Results {
		if perr := restclient.Patch(&m, fields.NewFields(fields.ExternalStatusDeleted)); perr != nil {
			err = perr
		}
	}

	return
}

func (s *Syncer) CleanMachine(t int64) (err error) {
	if cerr := s.CleanOutdatedMachineInAccount(t); cerr != nil {
		err = cerr
	}

	if cerr := s.CleanOutdatedMachineWithoutAccount(t, -time.Hour*4); cerr != nil {
		err = cerr
	}

	return
}

func (s *Syncer) CleanOutdatedMachineInAccount(t int64, additions ...fields.Field) (err error) {
	conds := append(
		[]fields.Field{
			fields.FactoryAccountField(s.cred.Account.GetID()),
			fields.NumberField("updated_before", t),
		},
		additions...)
	return s.clean(conds...)
}

func (s *Syncer) CleanOutdatedMachineWithoutAccount(t int64, delta time.Duration) (err error) {
	ago := time.Unix(t, 0).Add(delta).Unix()

	conds := []fields.Field{
		fields.FactoryField(s.cred.Factory.GetID()),
		fields.IsNull(fields.FactoryAccountFieldKey),
		fields.NamedField("updated_before", ago),
	}
	return s.clean(conds...)
}

func (s *Syncer) CleanOutdatedSecurityGroups(t int64, conds ...fields.Field) error {
	conds = append(conds,
		fields.LessThan("update_at", t),
		fields.FactoryField(s.cred.Factory.GetID()),
		fields.FactoryAccountField(s.cred.Account.GetID()),
	)

	resp, err := restclient.ListAll[models.Security](conds...)
	if err != nil {
		return err
	}

	for _, r := range resp.Results {
		if oerr := restclient.Delete(r); oerr != nil {
			err = oerr
		}
	}

	return err
}
