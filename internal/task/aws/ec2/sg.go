package ec2

import (
	"time"

	"github.com/aws/aws-sdk-go/service/ec2"
	"github.com/fatih/color"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/cache"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
)

func (s *Syncer) SyncSecurityGroupsInRegionAndClean(svc *ec2.EC2, region *models.Region) (err error) {
	start := time.Now().Unix()

	err = s.SyncSecurityGroups(svc)
	if err == nil {
		s.CleanOutdatedSecurityGroups(start, fields.RegionField(region.GetID()))
	}
	return
}

func (s *Syncer) SyncSecurityGroups(svc *ec2.EC2) (err error) {
	input := &ec2.DescribeSecurityGroupsInput{
		MaxResults: fields.Int64(100),
	}

	factoryAttrs := fields.FieldList(
		fields.FactoryField(s.cred.Factory.GetID()),
		fields.FactoryAccountField(s.cred.Account.GetID()),
	)

	vpcer := cache.VPCCacher()

	for token, started := "", false; token != "" || !started; started = true {
		if token != "" {
			input.SetNextToken(token)
		}

		outputs, derr := svc.DescribeSecurityGroups(input)
		if derr != nil {
			return derr
		}
		token = fields.Value(outputs.NextToken)

		for _, g := range outputs.SecurityGroups {
			attrs := BeautySecurityGroups(g).With(factoryAttrs...)

			if vpc, err := vpcer(fields.PString(g.VpcId)); err == nil {
				attrs.SetField(fields.VpcField(vpc.GetID()))
				if r, found := s.regions[*svc.Config.Region]; found {
					attrs.SetField(fields.RegionField(r.GetID()))
				}
			}

			conds := fields.FieldList(
				*attrs.GetField("security_id"),
			)

			if sg, perr := restclient.PostOrPatch[models.Security](conds, attrs); perr == nil {
				color.Green("sync sg %s ok", *g.GroupId)

				ruleSyncStart := time.Now().Unix()
				if srerr := s.SyncRules(svc, *g.GroupId, sg.GetID()); srerr != nil {
					err = srerr
					s.log.Error("sync sg rule error", "sg", *g.GroupId, "error", srerr)
				} else {
					color.Green("  sync rules %s ok", *g.GroupId)

					// cleanup rules
					restclient.DeleteSubResource[models.Security]("rules", sg.SecurityID, fields.NumberField(
						"updated_before", ruleSyncStart,
					))

				}
			} else {
				s.log.Error("sync sg error", "sg", *g.GroupId, "error", perr)
			}
		}
	}

	return
}

func (s *Syncer) SyncRules(svc *ec2.EC2, sg string, sgPk int) (err error) {
	input := &ec2.DescribeSecurityGroupRulesInput{
		Filters: []*ec2.Filter{
			{Name: fields.Pointer("group-id"), Values: []*string{fields.Pointer(sg)}},
		},
	}

	for token, started := "", false; token != "" || !started; started = true {
		if token != "" {
			input.SetNextToken(token)
		}

		outputs, derr := svc.DescribeSecurityGroupRules(input)
		if derr != nil {
			return derr
		}
		token = fields.Value(outputs.NextToken)

		for _, r := range outputs.SecurityGroupRules {
			attrs := BeautyRule(r).With(
				fields.NumberField("security", sgPk),
			)
			conds := []fields.Field{
				fields.StringPField("rule_id", r.SecurityGroupRuleId),
			}

			if _, perr := restclient.PostOrPatch[models.Rule](conds, attrs); perr != nil {
				err = perr
				s.log.Error("sync rule error", "rule", *r.SecurityGroupRuleId, "error", perr)
			}
		}
	}

	return err
}
