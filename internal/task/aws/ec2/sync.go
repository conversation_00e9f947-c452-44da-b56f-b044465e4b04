package ec2

import (
	"log/slog"

	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/ec2"
	"gitlab.lilithgame.com/yunwei/pkg/logger"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
	task "gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/task/aws"
)

type Syncer struct {
	cred    *task.FactoryCrendential
	regions map[string]models.Region
	zones   map[string]models.Zone
	log     *slog.Logger
}

func NewSyncer(factoryKey string) (*Syncer, error) {
	cred, err := task.CreateCredentialWithAccount(factoryKey)
	if err != nil {
		return nil, err
	}

	s := &Syncer{
		cred: cred,
		// TODO: use cache
		zones: LoadZones(
			fields.NamedField("factory__id", cred.Factory.GetID()),
			fields.Unlimited,
		),
		regions: LoadRegions(
			fields.NamedField("factory__id", cred.Factory.GetID()),
			fields.Unlimited,
		), // TODO: load zones by vendor is better

		log: logger.Slog().With("factory", cred.Factory.Name, "account", cred.Account.Name),
	}

	return s, nil
}

// Sync sync all resource in across all regions
func (s *Syncer) Sync() (err error) {
	for _, region := range s.regions {
		s.log.Debug("syncing in region", "region", region.Name)

		if serr := s.SyncRegion(&region); serr != nil {
			err = serr
		}
	}
	return
}

func (s *Syncer) SyncRegion(region *models.Region) error {
	cfg := &aws.Config{
		Credentials: s.cred.Credential,
		Region:      &region.RegionID,
	}

	sess, err := session.NewSession(cfg)
	if err != nil {
		return err
	}

	svc := ec2.New(sess)

	s.SyncMachineInRegionAndClean(svc, region)
	s.SyncSecurityGroupsInRegionAndClean(svc, region)

	return err
}
