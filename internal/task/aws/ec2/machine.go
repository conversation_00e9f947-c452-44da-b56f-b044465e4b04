package ec2

import (
	"time"

	"github.com/aws/aws-sdk-go/service/ec2"
	"github.com/fatih/color"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/category"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
)

func (s *Syncer) SyncMachineInRegionAndClean(svc *ec2.EC2, region *models.Region) (err error) {
	start := time.Now().Unix()
	err = s.SyncMachine(svc)
	if err == nil {
		s.CleanOutdatedMachineInAccount(start, fields.RegionField(region.GetID()))
	}
	return
}

func (s *Syncer) SyncMachine(svc *ec2.EC2) (err error) {
	input := ec2.DescribeInstancesInput{
		MaxResults: fields.Int64(100),
	}

	for token, started := "", false; token != "" || !started; started = true {
		if token != "" {
			input.SetNextToken(token)
		}

		resp, derr := svc.DescribeInstances(&input)
		if derr != nil {
			return derr
		}
		token = fields.Value(resp.NextToken)

		if rvs := resp.Reservations; len(rvs) > 0 {
			for _, rv := range rvs {
				for _, host := range rv.Instances {
					attr := BeautyHost(host, svc)
					attr.With(
						fields.FactoryField(s.cred.Factory.GetID()),
						fields.FactoryAccountField(s.cred.Account.GetID()),
					)

					// zone record id
					if az := fields.Value(host.Placement.AvailabilityZone); az != "" {
						if zoneID, exists := s.zones[az]; exists {
							attr.SetField(fields.ZoneField(zoneID.GetID()))
						}
					}

					//  补充产品信息
					if productId := attr.GetInt(fields.ProductFieldKey); productId == nil {
						if productId = category.GetMatchedProductID(s.cred.Account.Channel); productId != nil {
							attr.Set(fields.ProductFieldKey, *productId)
						}
					}

					conds := []fields.Field{
						fields.ExternalUUIDField(attr.GetString(fields.ExternalUUIDFieldKey)),
					}

					if _, perr := restclient.PostOrPatch[models.Machine](conds, attr); perr != nil {
						err = perr
						color.Red("syncing machine failed %v with data: %+v", err, attr)
					}
				}
			}
		}

	}

	return
}
