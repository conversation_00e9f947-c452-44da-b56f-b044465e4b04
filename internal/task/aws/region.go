package aws

import (
	"fmt"

	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/awserr"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/ec2"
	log "gitlab.lilithgame.com/yunwei/pkg/logger"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/cache"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/util"
)

// 列举regions
func EC2Regions(sess *session.Session, all ...bool) ([]*ec2.Region, error) {
	svc := ec2.New(sess)

	listAll := true
	if len(all) > 0 {
		listAll = all[0]
	}

	input := &ec2.DescribeRegionsInput{
		AllRegions: aws.Bool(listAll),
	}

	result, err := svc.DescribeRegions(input)
	if err != nil {
		if aerr, ok := err.(awserr.Error); ok {
			switch aerr.Code() {
			case "AuthFailure", "InvalidClientTokenId":
				log.Warn(aerr.Error())
			default:
				log.Error(aerr.Error())
			}
		} else {
			log.Error(aerr.Error())
		}
		return nil, err
	}
	return result.Regions, nil
}

// 获取region下的 AZ 列表
func EC2AvailableZones(sess *session.Session) ([]*ec2.AvailabilityZone, error) {
	svc := ec2.New(sess)

	input := &ec2.DescribeAvailabilityZonesInput{}

	result, err := svc.DescribeAvailabilityZones(input)
	if err != nil {
		if aerr, ok := err.(awserr.Error); ok {
			switch aerr.Code() {
			case "AuthFailure", "InvalidClientTokenId":
				log.Warn(aerr.Error())
			default:
				log.Error(aerr.Error())
			}
		} else {
			log.Error(aerr.Error())
		}
		return nil, err
	}

	return result.AvailabilityZones, nil
}

// func RegionTask(ak, sk string, AWS_REG_MAP_FROM_CMDB map[string]float64) []*ec2.Region {
func RegionTask(ak, sk string, regionMap map[string]int) []*ec2.Region {
	// 初始化AWS
	sess, err := NewDefaultSession(ak, sk)
	if err != nil {
		log.Errorf("initialize aws session failed: %s", err.Error())
		return nil
	}

	// 获取EC2 所有地域
	regions, err := EC2Regions(sess, false)
	if err != nil {
		log.Errorf("fetch all ec2 regions failed: %s", err.Error())
		return nil
	}

	regionLenthFromAPI := len(regions)
	zoneLenthFromAPI := 0
	regionTotal := 0
	zoneTotal := 0

	f := cache.GetFactory("aws")
	factoryID := f.GetID()

	// 遍历region
	for _, region := range regions {
		log.Debug("found region", "region", *region.RegionName)
		regionTotal++

		reg := *region.RegionName
		regID := *region.RegionName

		var regionRecord models.Region
		hasRecord, err := restclient.Find(&regionRecord,
			fields.NamedField("region_id", regID),
			fields.NamedField("factory__name", "亚马逊"),
		)
		if err != nil {
			log.Error("Region: " + reg + " len(cmdbResultList) != 0/1, Check it!" + err.Error())
			continue
		}

		attr := util.RegionAttrBuilder(regID, reg, factoryID)
		if !hasRecord {
			// 如果CMDB中不存在该region，则添加该region
			restclient.Post[models.Region](attr)
		} else {
			restclient.PatchByID[models.Region](regionRecord.GetID(), attr)
		}

		// 遍历每个region下的Availability Zone
		azs, err := EC2AvailableZones(session.Must(NewSession(ak, sk, WithRegionOption(reg))))
		if err != nil {
			log.Error("list zones in region failed", "region", reg, "error", err.Error())
			continue
		}

		if len(azs) == 0 {
			continue
		}

		zoneLenthFromAPI += len(azs)

		for _, az := range azs {
			var zoneRecord models.Zone
			hasRecord, err := restclient.Find(&zoneRecord,
				fields.NamedField("zone_id", *az.ZoneId),
				fields.NamedField("factory__name", "亚马逊"),
			)
			if err != nil {
				log.Error("AZ: " + *az.ZoneId + " len(cmdbResultList) != 0/1, Check it! " + err.Error())
				continue
			}

			attr := util.ZoneAttrBuilder(regionMap[regID], *az.ZoneId, *az.ZoneName, "", "", regionRecord.Factory)
			if !hasRecord {
				// 如果CMDB中不存在该zone，则添加该zone
				restclient.Post[models.Zone](attr)
			} else {
				zoneTotal++
				restclient.PatchByID[models.Zone](zoneRecord.GetID(), attr)
			}
		}
	}

	fmt.Println("AWS Region total(API): ", regionLenthFromAPI)
	fmt.Println("AWS Region total(OMA): ", regionTotal)
	fmt.Println("AWS AZ total(API): ", zoneLenthFromAPI)
	fmt.Println("AWS AZ total(OMA): ", zoneTotal)

	return regions
}
