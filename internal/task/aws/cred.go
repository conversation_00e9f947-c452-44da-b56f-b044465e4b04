package aws

import (
	"fmt"
	"strings"

	"github.com/aws/aws-sdk-go/aws/credentials"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/cache"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
)

type FactoryCrendential struct {
	Factory    *models.Factory          `json:"factory"`
	Account    *models.FactoryAccount   `json:"account"`
	Credential *credentials.Credentials `json:"credential"`
}

func CreateCredential(secretName string) (*credentials.Credentials, error) {
	var secrets restclient.AWSCredential
	if err := restclient.UnmarshalSecret(secretName, &secrets); err != nil {
		return nil, err
	}

	cred := credentials.NewStaticCredentials(secrets.AccessKeyID, secrets.AccessKeySecret, "")
	return cred, nil
}

func CreateCredentialWithAccount(accountKey string) (*FactoryCrendential, error) {
	var factoryKey string = strings.Split(accountKey, "-")[0]

	if f := cache.GetFactory(factoryKey); f != nil {
		account, err := getAccount(f.GetID(), accountKey)
		if err != nil {
			return nil, err
		}

		cred, err := CreateCredential(account.KMSAccount)
		if err != nil {
			return nil, err
		}

		return &FactoryCrendential{
			Factory:    f,
			Account:    account,
			Credential: cred,
		}, nil
	}

	return nil, fmt.Errorf("get factory %s failed", factoryKey)
}

func getAccount(fid int, accountKey string) (*models.FactoryAccount, error) {
	var account models.FactoryAccount

	has, err := restclient.Find(&account,
		fields.NamedField("key_name", accountKey),
		fields.FactoryField(fid),
	)
	if err != nil {
		return nil, err
	}

	if !has {
		return nil, fmt.Errorf("account %s not found", accountKey)
	}

	// fmt.Printf("account %+v\n", account)

	return &account, nil
}
