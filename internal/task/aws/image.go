package aws

import (
	// 	"asset-syncer/internal/middleware"
	// 	"asset-syncer/internal/util"
	// 	"fmt"

	// 	"github.com/aws/aws-sdk-go/aws"
	// 	"github.com/aws/aws-sdk-go/aws/awserr"
	// 	"github.com/aws/aws-sdk-go/aws/credentials"
	// 	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/ec2"
)

func ImageTask(ak, sk string, regionmap map[string]float64, regions []*ec2.Region) {
	// var ALL_IMAGE_LIST = []string{}
	// var total int
	// for _, region := range regionList {
	// 初始化AWS
	// sess, _ := session.NewSession(
	// 	&aws.Config{
	// 		Region:      aws.String(*region.RegionName),
	// 		Credentials: credentials.NewStaticCredentials(AK, SK, ""),
	// 		Endpoint:    aws.String("http://" + *region.Endpoint),
	// 		// Endpoint:    aws.String("http://ec2.ap-southeast-1.amazonaws.com"),
	// 	})
	// svc := ec2.New(sess)
	// input := &ec2.DescribeImagesInput{
	// 	Owners: []*string{
	// 		aws.String("amazon"),
	// 	},
	// 	Filters: []*ec2.Filter{
	// 		{
	// 			Name: aws.String("architecture"),
	// 			Values: []*string{aws.String("x86_64")},
	// 		},
	// 		{
	// 			Name: aws.String("name"),
	// 			Values: []*string{
	// 				// aws.String("CentOS*"),
	// 				aws.String("Ubuntu*"),
	// 			},
	// 		},
	// 		{
	// 			Name: aws.String("state"),
	// 			Values: []*string{aws.String("available")},
	// 		},
	// 	},
	// }
	// result, err := svc.DescribeImages(input)
	// if err != nil {
	// 	if aerr, ok := err.(awserr.Error); ok {
	// 		switch aerr.Code() {
	// 		case "AuthFailure", "InvalidClientTokenId":
	// 			log.Warn(aerr.Error())
	// 		default:
	// 			log.Error(aerr.Error())
	// 		}
	// 	} else {
	// 		log.Error(aerr.Error())
	// 	}
	// 	continue
	// }
	// imageList := result.Images
	// for _, image := range imageList {
	// attr := util.ImageAttrBuilder(*image.ImageId, *image.Name, *image.Description, *image.Name, *image.ImageOwnerAlias, "", AWS_REG_MAP_FROM_CMDB[*region.RegionName], 3)
	// fmt.Println("attr:", attr)
	// }
	// }
}
