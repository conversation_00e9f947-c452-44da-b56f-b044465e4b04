package eks

import (
	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/ec2"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
)

func LoadRegions(conds ...fields.Field) map[string]models.Region {
	records, err := restclient.ListAll[models.Region](conds...)
	if err != nil {
		panic(err)
	}

	v := make(map[string]models.Region)

	for _, r := range records.Results {
		v[r.RegionID] = r
	}

	return v
}

func LoadZones(conditions ...fields.Field) map[string]models.Zone {
	page, err := restclient.List[models.Zone](conditions...)
	if err != nil {
		panic(err)
	}

	v := make(map[string]models.Zone)

	for _, zone := range page.Results {
		v[zone.Name] = zone
	}

	return v
}

func (s *Syncer) availableRegions() ([]string, error) {
	regions := make([]string, 0)

	cfg := &aws.Config{Credentials: s.cred.Credential}

	sess, err := session.NewSession(cfg)
	if err != nil {
		return nil, err
	}

	svc := ec2.New(sess, aws.NewConfig().WithRegion("ap-southeast-1"))

	if rgs, err := svc.DescribeRegions(&ec2.DescribeRegionsInput{}); err == nil {
		for _, r := range rgs.Regions {
			regions = append(regions, *r.RegionName)
		}
	} else {
		return nil, err
	}

	return regions, nil
}
