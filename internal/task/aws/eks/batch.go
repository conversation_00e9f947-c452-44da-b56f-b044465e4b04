package eks

import (
	"fmt"
	"os"
	"time"

	"github.com/fatih/color"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/factory"
)

type BatchSyncer struct {
	syncers []*Syncer
}

func NewBatchSyncer(s ...*Syncer) *BatchSyncer {
	b := &BatchSyncer{
		syncers: make([]*Syncer, 0),
	}

	b.Append(s...)

	return b
}

func NewBatchSyncerWithFactoryKeys(keys ...string) *BatchSyncer {
	batcher := NewBatchSyncer()

	for _, key := range keys {
		if s, err := NewSyncer(key); err == nil {
			batcher.Append(s)
		} else {
			fmt.Fprintf(os.Stderr, "initialize aws ec2 syncer for factory %s error: %v\n", key, err)
		}
	}

	return batcher
}

func NewDefaultBatcherSyncer() *BatchSyncer {
	return NewBatchSyncerWithFactoryKeys(
		factory.AWSFarlight.String(),
		factory.AWSPlat.String(),
		factory.AWSPlat2.String(),
		factory.AWSSamo.String(),
		factory.AWSMona.String(),
		factory.AWSAd.String(),
	)
}

func (bs *BatchSyncer) Len() int {
	return len(bs.syncers)
}

func (bs *BatchSyncer) Append(ss ...*Syncer) {
	if len(ss) > 0 {
		bs.syncers = append(bs.syncers, ss...)
	}
}

func (bs *BatchSyncer) Sync() error {
	var err error

	for _, syncer := range bs.syncers {
		startAt := time.Now().Unix()
		if serr := syncer.Sync(); serr == nil {
			syncer.Clean(startAt)
		} else {
			err = serr
			color.Red("%s", serr.Error())
		}
	}

	return err
}

func SyncAll() error {
	batcher := NewDefaultBatcherSyncer()
	return batcher.Sync()
}
