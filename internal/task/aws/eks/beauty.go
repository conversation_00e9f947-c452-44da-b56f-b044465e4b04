package eks

import (
	aks "github.com/aws/aws-sdk-go/service/eks"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/category"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
)

func Beauty(c *aks.Cluster) fields.Fields {
	attrs := fields.NewFields(
		fields.StringPField("cluster_id", c.Arn),
		fields.StringPField("cluster_name", c.Name),
		fields.StringPField("cluster_version", c.Version),
		fields.CreateTimeField(*c.CreatedAt),
		// fields.RegionIDField(region),
	)

	// product
	if productID := category.Category(*c.Name, ""); productID != nil {
		attrs.Set(fields.ProductFieldKey, fields.PNumber(productID))
	}

	return attrs
}
