package eks

import (
	"fmt"
	"time"

	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/ec2"
	aks "github.com/aws/aws-sdk-go/service/eks"
	"github.com/fatih/color"
	"gitlab.lilithgame.com/yunwei/pkg/logger"

	beauty "gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/beauty/aws"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/category"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
	task "gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/task/aws"
)

type Syncer struct {
	cred    *task.FactoryCrendential
	regions map[string]models.Region
}

func NewSyncer(accountKey string) (*Syncer, error) {
	cred, err := task.CreateCredentialWithAccount(accountKey)
	if err != nil {
		return nil, err
	}

	return &Syncer{
		cred: cred,
		regions: LoadRegions(
			fields.NamedField("factory__name", "亚马逊"),
			fields.Unlimited,
		), // TODO: load zones by vendor is better
	}, nil
}

func (s *Syncer) Sync() error {
	var err error

	availableRegions, err := s.availableRegions()
	if err != nil {
		return err
	}

	for _, reg := range availableRegions {
		color.Green("sync in %s", reg)
		if serr := s.SyncRegionalCluster(reg); serr != nil {
			err = serr
			logger.Error("sync regional cluster error", "cluster", reg, "error", err)
		}
	}
	return err
}

func (s *Syncer) SyncRegionalCluster(region string, fs ...fields.Field) error {
	cfg := &aws.Config{
		Credentials: s.cred.Credential,
		Region:      &region,
	}

	sess, err := session.NewSession(cfg)
	if err != nil {
		return err
	}

	svc := aks.New(sess, aws.NewConfig().WithRegion(region))
	ec2Svc := ec2.New(sess, aws.NewConfig().WithRegion(region))

	factoryAttr := fields.FieldList(
		fields.FactoryField(s.cred.Factory.GetID()),
		fields.FactoryAccountField(s.cred.Account.GetID()),
	)
	err = svc.ListClustersPages(&aks.ListClustersInput{}, func(page *aks.ListClustersOutput, lastPage bool) bool {
		for _, cluster := range page.Clusters {
			if output, err := svc.DescribeCluster(&aks.DescribeClusterInput{Name: cluster}); err == nil {
				clusterInfo := output.Cluster

				color.Blue("%s %s id: %s\n", *cluster, *clusterInfo.Version, aws.StringValue(clusterInfo.Id))

				clusterAttrs := Beauty(clusterInfo).
					With(fields.RegionIDField(region)).
					With(factoryAttr...)

				// 尝试补充 product
				if productId := clusterAttrs.GetInt(fields.ProductFieldKey); productId == nil {
					//  补充产品信息
					if productId = category.GetMatchedProductID(s.cred.Account.Channel); productId != nil {
						clusterAttrs.Set(fields.ProductFieldKey, *productId)
					}
				}

				clusterCond := fields.FieldList(
					*clusterAttrs.GetField("cluster_id"),
				)

				// 同步集群
				if k, perr := restclient.PostOrPatch[models.K8SCluster](clusterCond, clusterAttrs); perr == nil {
					// 同步节点
					startAt := time.Now().Unix()
					if serr := s.SyncClusterNodes(svc, ec2Svc, cluster, clusterInfo.Arn); serr != nil {
						color.Red("list nodeGroups in %s => %s", *clusterInfo.Name, serr.Error())
					} else {
						//  清理本集群过期节点
						restclient.DeleteSubResource[models.K8SCluster](
							"nodes",
							k.GetID(),
							fields.NumberField("updated_before", startAt),
						)
					}
				}
			}
		}

		return true
	})

	return err
}

func findGroupInstances(ec2Svc *ec2.EC2, clusterName string, nodeGroupName string) []*ec2.Instance {
	if output, err := ec2Svc.DescribeInstances(&ec2.DescribeInstancesInput{
		Filters: []*ec2.Filter{
			{Name: aws.String("tag:eks:cluster-name"), Values: []*string{aws.String(clusterName)}},
			{Name: aws.String("tag:eks:nodegroup-name"), Values: []*string{aws.String(nodeGroupName)}},
		},
	}); err == nil {
		instances := make([]*ec2.Instance, 0)
		for _, res := range output.Reservations {
			instances = append(instances, res.Instances...)
		}
		return instances
	}
	return nil
}

func (s *Syncer) SyncClusterNodes(svc *aks.EKS, ec2Svc *ec2.EC2, cluster *string, clusterId *string) error {
	err := svc.ListNodegroupsPages(&aks.ListNodegroupsInput{ClusterName: cluster}, func(lno *aks.ListNodegroupsOutput, b bool) bool {
		for _, group := range lno.Nodegroups {
			input := &aks.DescribeNodegroupInput{
				ClusterName:   cluster,
				NodegroupName: group,
			}

			if nodeOutput, err := svc.DescribeNodegroup(input); err == nil {
				groupInfo := nodeOutput.Nodegroup

				color.Yellow("%s %s\n", *group, *groupInfo.ReleaseVersion)

				instances := findGroupInstances(ec2Svc, *cluster, *group)
				for _, i := range instances {
					instanceFields := beauty.EC2TagAsFields(i)
					fmt.Printf("instance tags: %+v\n", instanceFields)
					color.Green("  instance: %s %s\n", *i.InstanceId, instanceFields.GetString("Name"))

					// patch record
					conds := []fields.Field{
						fields.StringPField("instance_id", i.InstanceId),
					}

					attrs := fields.NewFields(conds...)

					attrs.With(
						fields.StringPField("cluster", clusterId),
						fields.StringPField("instance_id", i.InstanceId),
						fields.StringField("instance_name", instanceFields.GetString("Name")),
					)

					//  同步 node 信息
					if _, perr := restclient.PostOrPatch[models.K8SNode](conds, attrs); perr != nil {
						color.Red("update k8s machine => %s", perr.Error())
						err = perr
					}
				}
			} else {
				color.Red("list nodes in %s => %s", *cluster, err.Error())
			}
		}

		return true
	})

	return err
}
