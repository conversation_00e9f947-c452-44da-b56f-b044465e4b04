package eip

import (
	"strings"

	"github.com/aws/aws-sdk-go/service/ec2"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/category"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
)

func BeautyWith(i *ec2.Address, additions ...fields.Field) fields.Fields {
	return Beauty(i).With(additions...)
}

func Beauty(i *ec2.Address) fields.Fields {
	attr := fields.NewFields(
		fields.ExternalUUIDField(*i.AllocationId),
		fields.StringPField("public_ip", i.PublicIp),
		fields.NilField("version"),
	)

	// 名称
	for _, tag := range i.Tags {
		if fields.PValueApplyEqual(tag.Key, strings.ToLower, "name") {
			attr.With(fields.StringPField("name", tag.Value))

			// 一旦找到了name，就尝试确定产品
			if productId := category.Category(*tag.Value, ""); productId != nil {
				attr.With(fields.NumberPField(fields.ProductFieldKey, productId))
			}

			break
		}
	}

	// 绑定的资源
	if i.InstanceId != nil {
		attr.With(
			fields.StringPField("bind_id", i.InstanceId),
			fields.StringField("bind_type", "Ec2"),
		)
	} else if i.NetworkInterfaceId != nil {
		attr.With(
			fields.StringPField("bind_id", i.NetworkInterfaceId),
			fields.StringField("bind_type", "Nat"),
		)
	} else {
		attr.With(fields.NilField("bind_id"), fields.NilField("bind_type"))
	}

	// 保留最后一次绑定信息
	if bindId := attr.GetString("bind_id"); bindId != "" {
		attr.With(fields.StringField("last_bind_id", bindId))
	}

	if bindType := attr.GetString("bind_type"); bindType != "" {
		attr.With(fields.StringField("last_bind_type", bindType))
	}

	// status
	if attr.GetString("bind_id") == "" {
		attr.With(fields.ExternalStatusField("Available"))
	} else {
		attr.With(fields.ExternalStatusField("Allocated"))
	}

	return attr
}
