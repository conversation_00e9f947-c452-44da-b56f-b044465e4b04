package eip

import (
	"time"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
)

func (s *Syncer) Clean(t int64) (err error) {
	if cerr := s.CleanOutdatedRecords(t); cerr != nil {
		err = cerr
	}

	if cerr := s.CleanEmptyAccountOutdatedRecords(t, -time.Hour*4); cerr != nil {
		err = cerr
	}

	return
}

func (s *Syncer) clean(conds ...fields.Field) (err error) {
	conds = append(conds,
		fields.FactoryField(s.cred.Factory.GetID()),
	)

	resp, err := restclient.ListAll[models.ElasticIP](conds...)
	if err != nil {
		return
	}

	for _, m := range resp.Results {
		if perr := restclient.Delete(m); perr != nil {
			err = perr
		}
	}

	return
}

func (s *Syncer) CleanOutdatedRecords(t int64) (err error) {
	conds := []fields.Field{
		fields.FactoryAccountField(s.cred.Account.GetID()),
		fields.LessThan("update_at", t),
	}
	return s.clean(conds...)
}

func (s *Syncer) CleanEmptyAccountOutdatedRecords(t int64, delta time.Duration) (err error) {
	ago := time.Unix(t, 0).Add(delta).Unix()

	conds := []fields.Field{
		fields.FactoryField(s.cred.Factory.GetID()),
		fields.IsNull(fields.FactoryAccountFieldKey),
		fields.LessThan("update_at", ago),
	}
	return s.clean(conds...)
}
