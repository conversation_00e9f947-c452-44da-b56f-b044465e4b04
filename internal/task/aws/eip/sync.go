package eip

import (
	"time"

	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/ec2"
	"github.com/fatih/color"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/category"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
	awstask "gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/task/aws"
)

type Syncer struct {
	cred    *awstask.FactoryCrendential
	regions map[string]models.Region
	zones   map[string]models.Zone
}

func NewSyncer(factoryKey string) (*Syncer, error) {
	cred, err := awstask.CreateCredentialWithAccount(factoryKey)
	if err != nil {
		return nil, err
	}

	return &Syncer{
		cred: cred,
		// TODO: use cache
		zones: LoadZones(
			fields.NamedField("factory__id", cred.Factory.GetID()),
			fields.Unlimited,
		),
		regions: LoadRegions(
			fields.NamedField("factory__id", cred.Factory.GetID()),
			fields.Unlimited,
		), // TODO: load zones by vendor is better
	}, nil
}

func (s *Syncer) SyncAll() (err error) {
	for region := range s.regions {
		if serr := s.SyncRegion(region); serr != nil {
			err = serr
		}
	}
	return
}

func (s *Syncer) SyncRegion(region string) error {
	cfg := &aws.Config{
		Credentials: s.cred.Credential,
		Region:      &region,
	}

	sess, err := session.NewSession(cfg)
	if err != nil {
		return err
	}

	svc := ec2.New(sess)
	input := ec2.DescribeAddressesInput{}

	resp, err := svc.DescribeAddresses(&input)
	if err != nil {
		return err
	}

	commonAttr := []fields.Field{
		fields.FactoryField(s.cred.Factory.GetID()),
		fields.FactoryAccountField(s.cred.Account.GetID()),
	}

	// if r, ok := s.regions[region]; ok {
	// 	commonAttr = append(commonAttr, fields.NamedField(fields.RegionFieldKey, r.GetID()))
	// }

	for _, addr := range resp.Addresses {
		attr := BeautyWith(addr, commonAttr...)

		//  尝试通过账号的渠道信息确定产品
		if productId := attr.GetInt(fields.ProductFieldKey); productId == nil {
			if productId = category.GetMatchedProductID(s.cred.Account.Channel); productId != nil {
				attr.With(fields.NamedPField(fields.ProductFieldKey, productId))
			}
		}

		conds := []fields.Field{
			*attr.GetField(fields.ExternalUUIDFieldKey),
		}

		if _, perr := restclient.PostOrPatch[models.ElasticIP](conds, attr); perr != nil {
			err = perr
			color.Red("Synced faild: %v", err)
		} else {
			color.Green("Synced: %+v", attr)
		}
	}

	return err
}

func Sync(accountKey string) error {
	s, err := NewSyncer(accountKey)
	if err != nil {
		return err
	}

	startAt := time.Now().Unix()
	if err = s.SyncAll(); err != nil {
		return err
	}

	return s.Clean(startAt)
}
