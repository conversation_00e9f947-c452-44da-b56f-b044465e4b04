package mysql

import (
	"fmt"
	"time"

	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/rds"
	"github.com/fatih/color"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
	awstask "gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/task/aws"
)

type Syncer struct {
	cred    *awstask.FactoryCrendential
	regions map[string]models.Region
}

func NewSyncer(factoryKey string) (*Syncer, error) {
	cred, err := awstask.CreateCredentialWithAccount(factoryKey)
	if err != nil {
		return nil, err
	}

	return &Syncer{
		cred: cred,
		regions: LoadRegions(
			fields.NamedField("factory__id", cred.Factory.GetID()),
			fields.Unlimited,
		),
	}, nil
}

func (s *Syncer) SyncAll() (err error) {
	for region, rg := range s.regions {
		fmt.Println("syncing region: ", region)

		startAt := time.Now().Unix()
		if serr := s.SyncRegion(region); serr != nil {
			err = serr
			color.Red("sync error in region %s, %v", region, err)
		} else {
			s.CleanOutdatedRecords(startAt, fields.RegionField(rg.GetID()))
		}
	}
	return
}

func (s *Syncer) SyncRegion(region string) error {
	cfg := &aws.Config{
		Credentials: s.cred.Credential,
		Region:      &region,
	}

	sess, err := session.NewSession(cfg)
	if err != nil {
		return err
	}

	svc := rds.New(sess)
	input := rds.DescribeDBInstancesInput{
		MaxRecords: aws.Int64(100),
		// Filters: []*rds.Filter{
		// 	{
		// Name: aws.String("engine"),
		// Values: aws.StringSlice([]string{
		// "aurora-mysql",
		// "mysql",
		// "postgres",
		// "sqlserver",
		// "oracle",
		// }),
		// },
		// },
	}

	commonAttr := []fields.Field{
		fields.FactoryField(s.cred.Factory.GetID()),
		fields.FactoryAccountField(s.cred.Account.GetID()),
	}
	for marker, started := "", false; !started || marker != ""; started = true {
		if marker != "" {
			input.SetMarker(marker)
		}

		resp, derr := svc.DescribeDBInstances(&input)
		if derr != nil {
			return derr
		}

		for _, r := range resp.DBInstances {
			if engine := fields.PLowerString(r.Engine); engine == "docdb" {
				continue
			}

			attr := BeautyWith(r, commonAttr...)

			// region
			if reg, ok := s.regions[region]; ok {
				attr.SetField(fields.RegionField(reg.GetID()))
			}

			cond := []fields.Field{
				*attr.GetField(fields.ExternalUUIDFieldKey),
			}

			if _, perr := restclient.PostOrPatch[models.MySQL](cond, attr); perr != nil {
				color.Red("sync mysql error: %+v", perr)
				err = perr
			} else {
				fmt.Printf("sync ok mysql: %+v\n", attr)
			}
		}

		marker = fields.Value(resp.Marker)
	}

	return err
}

func Sync(account string) error {
	s, err := NewSyncer(account)
	if err != nil {
		return err
	}

	startAt := time.Now().Unix()
	if err = s.SyncAll(); err != nil {
		return err
	}

	return s.Clean(startAt)
}
