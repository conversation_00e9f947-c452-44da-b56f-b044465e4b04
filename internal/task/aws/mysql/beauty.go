package mysql

import (
	"time"

	"github.com/aws/aws-sdk-go/service/rds"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
)

func BeautyWith(i *rds.DBInstance, additions ...fields.Field) fields.Fields {
	return Beauty(i).With(additions...)
}

func Beauty(r *rds.DBInstance) fields.Fields {
	attr := fields.NewFields(
		fields.StringPField(fields.ExternalUUIDFieldKey, r.DbiResourceId),
		fields.StringPField(fields.ExternalNameFieldKey, r.DBInstanceIdentifier),
		fields.LowerStringPField("engine", r.Engine),
		fields.StringPField("version", r.EngineVersion),
		fields.StringPField("flavor_name", r.DBInstanceClass),
		fields.StringField(fields.CreateTimeFieldKey, r.InstanceCreateTime.Format(time.RFC3339)),
	)

	if ep := r.Endpoint; ep != nil {
		attr.SetString("conn", ep.Address)
		attr.SetOrNil("port", ep.Port)
	}

	// external_tags
	if tags := r.TagList; len(tags) > 0 {
		tagList := make([]map[string]any, 0)
		for _, tag := range tags {
			tagList = append(tagList, map[string]any{
				"k": *tag.Key,
				"v": *tag.Value,
			})
		}
		attr.SetJSON(fields.ExternalTagsFieldKey, tagList)
	}

	// rename status
	status := fields.PLowerString(r.DBInstanceStatus)
	if status == "available" {
		status = "running"
	}
	attr.SetField(fields.ExternalStatusField(status))

	return attr
}
