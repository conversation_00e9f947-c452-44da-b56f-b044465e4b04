package elb2

import (
	"fmt"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
	awstask "gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/task/aws"
)

type Syncer struct {
	cred    *awstask.FactoryCrendential
	regions map[string]models.Region
}

func NewSyncer(accountKey string) (*Syncer, error) {
	cred, err := awstask.CreateCredentialWithAccount(accountKey)
	if err != nil {
		return nil, err
	}

	regions := restclient.LoadRegions(
		fields.NamedField("factory__name", "亚马逊"),
		fields.Unlimited,
	)

	fmt.Printf("find %d regions\n", len(regions))

	return &Syncer{
		cred:    cred,
		regions: regions,
	}, nil
}
