package elb2

import (
	"fmt"

	"gitlab.lilithgame.com/yunwei/pkg/logger"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
)

func (s *Syncer) Clean(cond ...fields.Field) error {
	if len(cond) == 0 {
		return fmt.Errorf("no condition")
	}

	cond = append(cond,
		fields.FactoryField(s.cred.Factory.GetID()),
		fields.FactoryAccountField(s.cred.Account.GetID()),
		fields.In("lb_type", "ALB", "NLB", "GLB"),
		fields.Unlimited,
	)

	resp, err := restclient.List[models.SLB](cond...)
	if err != nil {
		return err
	}

	for _, r := range resp.Results {
		if oerr := restclient.Delete(r); oerr != nil {
			err = oerr
		}
		logger.Printf("remove %d(slb_id: %s) %v", r.GetID(), r.SlbID, err)
	}

	logger.Debugf("remove %d %s records", resp.Count, LBType)

	return err
}
