package elb2

import (
	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/credentials"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/elbv2"
)

func CreateELBV2Client(creds *credentials.Credentials, region string) (*elbv2.ELBV2, error) {
	sess, err := session.NewSession()
	if err != nil {
		return nil, err
	}

	cfg := aws.NewConfig().
		WithCredentials(creds).
		WithRegion(region)

	svc := elbv2.New(sess, cfg)
	return svc, nil
}
