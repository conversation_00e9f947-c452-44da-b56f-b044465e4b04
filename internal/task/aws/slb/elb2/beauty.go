package elb2

import (
	"time"

	"github.com/aws/aws-sdk-go/service/elbv2"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
)

func Beauty(l *elbv2.LoadBalancer) fields.Fields {
	fz := fields.NewFields(
		fields.NamedPField(fields.ExternalUUIDFieldKey, l.LoadBalancerName),
		fields.NamedPField(fields.ExternalNameFieldKey, l.LoadBalancerName),
		fields.NamedPField("address", l.DNSName),
		fields.NamedPField("address_type", l.Scheme),
		fields.NamedPField("ip_version", l.IpAddressType),
	)

	status := fields.PLowerString(l.State.Code)
	if status == "active" {
		status = "running"
	}
	fz.SetField(fields.ExternalStatusField(status))

	if t := l.Type; t != nil {
		fz.Set("lb_type", BeautyLbType(*t))
	}

	if t := l.CreatedTime; t != nil {
		fz.Set(fields.<PERSON><PERSON>T<PERSON>, t.<PERSON>(time.RFC3339))
	}

	return fz
}

func BeautyWith(l *elbv2.LoadBalancer, additions ...fields.Field) fields.Fields {
	fz := Beauty(l)
	fz.With(additions...)
	return fz
}

func BeautyLbType(t string) string {
	// ref: https://docs.aws.amazon.com/elasticloadbalancing/latest/APIReference/Welcome.html
	switch t {
	case "application":
		// return "ELBv2-L7"
		return "ALB"
	case "network":
		// return "ELBv2-L4"
		return "NLB"
	case "gateway":
		// return "ELBv2-L3"
		return "GLB"
	}

	return t
}
