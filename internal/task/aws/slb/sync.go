package slb

import (
	"time"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/factory"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/task/aws/slb/elb"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/task/aws/slb/elb2"
)

type Syncer interface {
	Sync() error
	Clean(cond ...fields.Field) error
}

func SyncWithAccount(accountKey string) error {
	syncers := make([]Syncer, 0)

	// elb
	elbSyncer, err := elb.NewSyncer(accountKey)
	if err != nil {
		return err
	}
	syncers = append(syncers, elbSyncer)

	// alb/nlb/glb
	elb2Syncer, err := elb2.NewSyncer(accountKey)
	if err != nil {
		return err
	}
	syncers = append(syncers, elb2Syncer)

	// start syncing
	for _, syncer := range syncers {
		t := time.Now().Unix()
		if serr := syncer.Sync(); serr != nil {
			err = serr
		} else {
			// clean outdated records
			if cerr := syncer.Clean(fields.LessThan("update_at", t)); cerr != nil {
				err = cerr
			}
		}
	}

	return err
}

func SyncAll() error {
	var err error
	for _, account := range factory.AWSFactories {
		if serr := SyncWithAccount(account.String()); serr != nil {
			err = serr
		}
	}

	return err
}
