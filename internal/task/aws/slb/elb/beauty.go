package elb

import (
	"time"

	elbv1 "github.com/aws/aws-sdk-go/service/elb"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
)

func Beauty(l *elbv1.LoadBalancerDescription) fields.Fields {
	fz := fields.NewFields(
		fields.NamedField("lb_type", LBType),
		fields.ExternalStatusField("RUNNING"),
		fields.NamedField("network_type", nil),
		fields.NamedPField(fields.ExternalUUIDFieldKey, l.Load<PERSON>alancerName),
		fields.NamedPField(fields.ExternalNameFieldKey, l.<PERSON>),
		fields.NamedPField("address", l.DNSName),
		fields.NamedPField("address_type", l.Scheme),
	)

	if t := l.CreatedTime; t != nil {
		fz.Set(fields.CreateTimeFieldKey, t.Format(time.RFC3339))
	}

	return fz
}

func BeautyWith(l *elbv1.LoadBalancerDescription, additions ...fields.Field) fields.Fields {
	fz := Beauty(l)
	fz.With(additions...)
	return fz
}
