package elb

import (
	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/credentials"
	"github.com/aws/aws-sdk-go/aws/session"
	elbv1 "github.com/aws/aws-sdk-go/service/elb"
)

func CreateELBClient(creds *credentials.Credentials, region string) (*elbv1.ELB, error) {
	sess, err := session.NewSession()
	if err != nil {
		return nil, err
	}

	cfg := aws.NewConfig().
		WithCredentials(creds).
		WithRegion(region)

	svc := elbv1.New(sess, cfg)
	return svc, nil
}
