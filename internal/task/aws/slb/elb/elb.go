package elb

import (
	"fmt"

	"github.com/aws/aws-sdk-go/aws"
	elbv1 "github.com/aws/aws-sdk-go/service/elb"
	"github.com/fatih/color"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
)

const LBType = "ELB"

func (s *Syncer) Sync() error {
	for _, region := range s.regions {
		if err := s.SyncWithRegion(region.RegionID); err != nil {
			color.Red("sync region %s error: %v\n", region.RegionID, err)
		} else {
			color.Green("sync region %s success\n", region.RegionID)
		}
	}

	return nil
}

func (s *Syncer) SyncWithRegion(region string) error {
	cli, err := CreateELBClient(s.cred.Credential, region)
	if err != nil {
		return err
	}

	var marker string
	var started bool

	vpcgetter := VpcGetter()

	input := &elbv1.DescribeLoadBalancersInput{
		PageSize: fields.Int64(100),
	}

	for ; marker != "" || !started; started = true {
		if marker != "" {
			input.SetMarker(marker)
		}

		resp, err := cli.DescribeLoadBalancers(input)
		if err != nil {
			return err
		}

		fmt.Printf("found %d slbs\n", len(resp.LoadBalancerDescriptions))

		for _, l := range resp.LoadBalancerDescriptions {
			attrs := BeautyWith(l,
				fields.FactoryField(s.cred.Factory.GetID()),
				fields.FactoryAccountField(s.cred.Account.GetID()),
				fields.RegionField(s.regions[region].GetID()),
			)

			if vpc, err := vpcgetter(*l.VPCId); err == nil {
				attrs.Set("vpc", vpc.GetID())
			}

			conds := []fields.Field{
				fields.NamedPField(fields.ExternalUUIDFieldKey, l.LoadBalancerName),
				fields.FactoryField(s.cred.Factory.GetID()),
				fields.FactoryAccountField(s.cred.Account.GetID()),
			}

			if _, err = restclient.PostOrPatch[models.SLB](conds, attrs); err != nil {
				color.Red("sync slb %s error: %v\n", *l.LoadBalancerName, err)
			}

		}

		marker = aws.StringValue(resp.NextMarker)
	}

	return err
}
