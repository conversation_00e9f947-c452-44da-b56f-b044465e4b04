package elb

import (
	"fmt"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
)

func getVPCRecord(vpcId string) (*models.VPC, error) {
	var vpc models.VPC
	has, err := restclient.Find(&vpc, fields.NamedField("vpc_id", vpcId))
	if err != nil {
		return nil, err
	}

	if !has {
		return nil, fmt.Errorf("no such vpc record with id %s", vpcId)
	}

	return &vpc, nil
}

func VpcGetter() func(vpcId string) (*models.VPC, error) {
	vpcList := make(map[string]*models.VPC)

	return func(vpcId string) (*models.VPC, error) {
		if vpc, ok := vpcList[vpcId]; ok {
			return vpc, nil
		}

		vpc, err := getVPCRecord(vpcId)
		if err != nil {
			return nil, err
		}

		vpcList[vpc.Name] = vpc

		return vpc, nil
	}
}
