package aws

import (
	"fmt"

	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/credentials"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/pkg/errors"
)

// Ref: https://docs.aws.amazon.com/AmazonRDS/latest/UserGuide/Concepts.RegionsAndAvailabilityZones.html#Concepts.RegionsAndAvailabilityZones.Regions
const defaultRegion = "ap-southeast-1" // Asian Pacific 亚太(新加坡)

func endpointForRegion(region string) string {
	return fmt.Sprintf("http://ec2.%s.amazonaws.com", region)
}

// ConfigOption is function to configure `aws.Config`
type ConfigOption func(*aws.Config)

// WithEndpointOption configure `aws.Config.Endpoint`
func WithEndpointOption(endpoint string) ConfigOption {
	return func(c *aws.Config) {
		c.WithEndpoint(endpoint)
	}
}

// WithRegionOption configure `aws.Config.Region` as well as `aws.Config.Endpoint`
func WithRegionOption(region string) ConfigOption {
	return func(c *aws.Config) {
		c.WithRegion(region).WithEndpoint(endpointForRegion(region))
	}
}

// NewSession creates a fresh new `aws.session.Session` instance
func NewSession(ak, sk string, opts ...ConfigOption) (*session.Session, error) {
	cfg := &aws.Config{Credentials: credentials.NewStaticCredentials(ak, sk, "")}

	// configure options
	for _, opt := range opts {
		opt(cfg)
	}

	sess, err := session.NewSession(cfg)
	if err != nil {
		return nil, errors.Wrap(err, "new aws session failed")
	}

	return sess, nil
}

// NewDefaultSession create a new `aws.session.Session` with `defaultRegion`
func NewDefaultSession(ak, sk string) (*session.Session, error) {
	// return NewSession(ak, sk, WithRegionOption(defaultRegion))
	return NewSessionWithRegion(ak, sk, defaultRegion)
}

func NewSessionWithRegion(ak, sk string, region string) (*session.Session, error) {
	return NewSession(ak, sk, WithRegionOption(region))
}
