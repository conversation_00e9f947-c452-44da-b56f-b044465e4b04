package aws

import (
	"fmt"
	"strings"
	"sync"

	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/awserr"
	"github.com/aws/aws-sdk-go/aws/credentials"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/ec2"
	log "gitlab.lilithgame.com/yunwei/pkg/logger"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/util"
)

type vpcChanData struct {
	vpcTotal    int32
	vpcList     []string
	subnetTotal int
	subnetList  []string
}

func VPCTask(
	ak, sk string,
	AWS_REG_MAP_FROM_CMDB map[string]int,
	regionList []*ec2.Region,
	AWS_ZONE_MAP_FROM_CMDB map[string]int,
	factoryId int,
	accountID int,
) {
	// start := time.Now()
	ALL_REGION_VPC_LIST := []string{}
	ALL_REGION_SUBNET_LIST := []string{}
	var allRegionVpcTotal int32
	var allRegionSubnetTotal int

	ch := make(chan vpcChanData, 100)
	var wg, consume, product sync.WaitGroup

	wg.Add(1)
	consume.Add(len(regionList))

	for _, region := range regionList {
		product.Add(1)
		go func(region ec2.Region) {
			ALL_VPC_LIST := []string{}
			ALL_SUBNET_LIST := []string{}
			var vpcTotal int32
			var subnetTotal int
			reg := *region.RegionName
			edp := *region.Endpoint
			sess, _ := session.NewSession(
				&aws.Config{
					Region:      aws.String(reg),
					Credentials: credentials.NewStaticCredentials(ak, sk, ""),
					Endpoint:    aws.String(edp),
				})
			svc := ec2.New(sess)
			input := &ec2.DescribeVpcsInput{}
			input.MaxResults = aws.Int64(500)
			result, _ := svc.DescribeVpcs(input)
			for _, vpc := range result.Vpcs {
				tags := vpc.Tags
				cidr := *vpc.CidrBlock
				if strings.HasPrefix(cidr, "172") {
					continue
				}

				vpcId := *vpc.VpcId
				ALL_VPC_LIST = append(ALL_VPC_LIST, vpcId)
				attr := util.VpcAttrBuilder(
					AWS_REG_MAP_FROM_CMDB[reg],
					vpcId,
					"",
					"",
					cidr,
					factoryId,
					accountID,
				)

				var vpcRecord models.VPC
				hasVpcRecord, err := restclient.Find(&vpcRecord, fields.NamedField("vpc_id", vpcId))
				if err != nil {
					log.Error("query vpc records error", "error", err, "vpc_id", vpcId)
					continue
				}

				var vpcName string
				if !hasVpcRecord {
					if len(tags) == 0 {
						vpcName = " "
					} else {
						for _, tag := range tags {
							if *tag.Key == "Name" {
								vpcName = *tag.Value
							} else {
								vpcName = " "
							}
						}
					}
					attr["name"] = vpcName
					restclient.Post[models.VPC](attr)

					hasVpcRecord, err = restclient.Find(&models.VPC{}, fields.NamedField("vpc_id", vpcId))
					vpcTotal++

					// FIXME: hack
					if err != nil || !hasVpcRecord {
						continue
					}
				} else {
					if len(tags) == 0 {
						vpcName = " "
					} else {
						for _, tag := range tags {
							if *tag.Key == "Name" {
								vpcName = *tag.Value
							} else {
								continue
							}
						}
					}

					attr.SetField(fields.NamedField("name", vpcName))
					restclient.PatchByID[models.VPC](vpcRecord.ID, attr)
					vpcTotal++
				}

				// 遍历vpc中的subnet
				subnetInput := &ec2.DescribeSubnetsInput{
					Filters: []*ec2.Filter{
						{
							Name: aws.String("vpc-id"),
							Values: []*string{
								aws.String(vpcId),
							},
						},
					},
					MaxResults: aws.Int64(500),
				}
				res, err := svc.DescribeSubnets(subnetInput)
				if err != nil {
					if aerr, ok := err.(awserr.Error); ok {
						switch aerr.Code() {
						default:
							fmt.Println("svc.DescribeSubnets awserr.Error", aerr.Error())
						}
					} else {
						fmt.Println("svc.DescribeSubnets !awserr.Error", err.Error())
					}
					return
				}

				// vpcCmdbId := cmdbResultList[0].(map[string]interface{})["id"].(float64)
				vpcCmdbId := vpcRecord.ID
				for _, subnet := range res.Subnets {
					tags := subnet.Tags
					cidr := *subnet.CidrBlock
					subnetId := *subnet.SubnetId
					zone := *subnet.AvailabilityZone
					attr := util.SubnetAttrBuilder(
						subnetId,
						"",
						"",
						"",
						cidr,
						false,
						0,
						vpcCmdbId,
						AWS_ZONE_MAP_FROM_CMDB[zone],
					)

					var subnetRecord models.Subnet
					hasSubnet, err := restclient.Find(&subnetRecord, fields.NamedField("subnet_id", subnetId))
					subnetName := ""
					if err != nil {
						log.Error("Subnet: "+subnetId+" len(cmdbResultList) != 0/1, Check it!", err.Error())
					} else if !hasSubnet {
						fmt.Println("import aws subnet: ", tags, subnetId, zone)
						if len(tags) == 0 {
							subnetName = " "
						} else {
							for _, tag := range tags {
								if *tag.Key == "Name" {
									subnetName = *tag.Value
								} else {
									continue
								}
							}
						}
						attr["name"] = subnetName
						restclient.Post[models.Subnet](attr)
						subnetTotal++
					} else {
						if len(tags) == 0 {
							subnetName = " "
						} else {
							for _, tag := range tags {
								if *tag.Key == "Name" {
									subnetName = *tag.Value
								} else {
									continue
								}
							}
						}
						attr.SetField(fields.NamedField("name", subnetName))
						restclient.PatchByID[models.Subnet](subnetRecord.ID, attr)
						subnetTotal++
					}
					ALL_SUBNET_LIST = append(ALL_SUBNET_LIST, subnetId)
				}
			}
			chanData := vpcChanData{
				vpcTotal:    vpcTotal,
				vpcList:     ALL_VPC_LIST,
				subnetTotal: subnetTotal,
				subnetList:  ALL_SUBNET_LIST,
			}
			ch <- chanData
			product.Done()
		}(*region)
	}

	go func() {
		defer wg.Done()
		for c := range ch {
			allRegionVpcTotal += c.vpcTotal
			ALL_REGION_VPC_LIST = append(ALL_REGION_VPC_LIST, c.vpcList...)
			allRegionSubnetTotal += c.subnetTotal
			ALL_REGION_SUBNET_LIST = append(ALL_REGION_SUBNET_LIST, c.subnetList...)
			consume.Done()
		}
	}()

	go func() {
		product.Wait()
		consume.Wait()
		close(ch)
	}()
	wg.Wait()
	// spend := time.Since(start)
	// fmt.Println("Factory Id:", factoryId, ", AWS VPC total:", allRegionVpcTotal, ", AWS Subnet total:", allRegionSubnetTotal, ", Spend:", spend)

	// 清理cmdb中无用数据
	// subnet
	cleanOutdatedSubnet(factoryId, ALL_REGION_SUBNET_LIST)
	// vpc
	cleanOutdatedVpc(factoryId, ALL_REGION_VPC_LIST)
}

func cleanOutdatedSubnet(fid int, subnetRecords []string) {
	results, err := restclient.List[models.Subnet](fields.NamedField("vpc__factory", fid))
	if err != nil {
		log.Error("cleanOutdatedSubnet", "error", err, "factory", fid)
		return
	}

	for _, subnet := range results.Results {
		if !util.IsContain(subnetRecords, subnet.SubnetID) {
			if err := restclient.Delete(subnet); err != nil {
				log.Error("clean outdated subnet failed", "error", err, "factory", fid, "subnet", subnet.SubnetID)
			}
		}
	}
}

func cleanOutdatedVpc(fid int, vpcRecords []string) {
	results, err := restclient.List[models.VPC](fields.NamedField("factory", fid))
	if err != nil {
		log.Error("find outdated vpc by factory failed", "error", err, "factory", fid)
		return
	}

	for _, vpc := range results.Results {
		if !util.IsContain(vpcRecords, vpc.VpcID) {
			if err := restclient.Delete(vpc); err != nil {
				log.Error("clean outdated vpc faield", "error", err, "factory", fid, "vpc", vpc.VpcID)
			}
		}
	}
}
