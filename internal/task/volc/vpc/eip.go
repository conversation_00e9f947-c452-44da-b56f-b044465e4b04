package vpc

import (
	"github.com/fatih/color"
	"github.com/volcengine/volcengine-go-sdk/service/vpc"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/category"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
)

func (s *Syncer) SyncEIP(svc *vpc.VPC) (err error) {
	input := &vpc.DescribeEipAddressesInput{
		PageSize: fields.Int64(50),
	}

	for pn := int64(1); ; pn++ {
		input.SetPageNumber(pn)

		resp, cerr := svc.DescribeEipAddresses(input)
		if cerr != nil {
			return cerr
		}

		color.Cyan("total %d eips in %s", fields.Value(resp.TotalCount), resp.Metadata.Region)

		for n, eip := range resp.EipAddresses {
			attrs := BeautyEIP(eip).With(
				fields.FactoryField(s.cred.Factory.GetID()),
				fields.FactoryAccountField(s.cred.Account.GetID()),
			)

			//  补充产品信息
			if productId := attrs.GetInt(fields.ProductFieldKey); productId == nil {
				if productId = category.GetMatchedProductID(s.cred.Account.Channel); productId != nil {
					attrs.Set(fields.ProductFieldKey, *productId)
				}
			}

			conds := []fields.Field{
				fields.NamedPField(fields.ExternalUUIDFieldKey, eip.AllocationId),
			}

			if _, perr := restclient.PostOrPatch[models.ElasticIP](conds, attrs); perr != nil {
				err = perr
				color.Red("[%2d] sync eip faield, %v", n, perr)
			} else {
				color.Green("[%2d] sync eip ok, %+v", n, attrs)
			}
		}

		if len(resp.EipAddresses) < int(*resp.PageSize) {
			break
		}
	}

	return err
}
