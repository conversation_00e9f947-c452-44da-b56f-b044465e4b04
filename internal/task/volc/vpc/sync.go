package vpc

import (
	"time"

	"github.com/volcengine/volcengine-go-sdk/volcengine"
	"github.com/volcengine/volcengine-go-sdk/volcengine/session"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/factory"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
	vtask "gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/task/volc"
)

type Syncer struct {
	cred    *vtask.FactoryCrendential
	regions map[string]models.Region

	globalFields []fields.Field
}

func NewSyncer(accountKey string) (*Syncer, error) {
	cred, err := vtask.CreateCredentialWithAccount(accountKey)
	if err != nil {
		return nil, err
	}

	globalFields := fields.FieldList(
		fields.FactoryField(cred.Factory.GetID()),
		fields.FactoryAccountField(cred.Account.GetID()),
	)

	return &Syncer{
		cred:         cred,
		globalFields: globalFields,
		regions: restclient.LoadRegions(
			fields.NamedField("factory__name", "火山云"),
			fields.Unlimited,
		),
	}, nil
}

func (s *Syncer) NewSession(region string) (*session.Session, error) {
	config := volcengine.NewConfig().
		WithCredentials(s.cred.Credential).
		WithRegion(region)

	return session.NewSession(config)
}

func Sync[T ~string](factoryKey T) error {
	s, err := NewSyncer(string(factoryKey))
	if err != nil {
		return err
	}

	start := time.Now().Unix()

	for regionID := range s.regions {
		if serr := s.SyncInRegion(regionID); serr != nil {
			err = serr
		}
	}

	if err == nil {
		s.CleanVPC(start)
		s.CleanSecurityGroups(start)
		s.CleanEIP(start)
	}

	return err
}

func SyncMany(keys ...factory.FactoryKeyType) error {
	var err error

	for _, key := range keys {
		if serr := Sync(key); serr != nil {
			err = serr
		}
	}

	return err
}

func SyncAll() error {
	return SyncMany(factory.VolcFactories...)
}
