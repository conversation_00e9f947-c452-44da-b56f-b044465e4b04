package vpc

import (
	"github.com/fatih/color"
	"github.com/volcengine/volcengine-go-sdk/service/vpc"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
)

func (s *Syncer) SyncInRegion(region string) error {
	sess, err := s.NewSession(region)
	if err != nil {
		return err
	}
	svc := vpc.New(sess)

	for _, err := range []error{
		s.SyncVPC(svc),
		s.SyncSecurityGroups(svc),
		s.SyncEIP(svc),
	} {
		if err != nil {
			return err
		}
	}

	return nil
}

func (s *Syncer) SyncVPC(svc *vpc.VPC) (err error) {
	input := &vpc.DescribeVpcsInput{
		PageSize: fields.Int64(100),
	}

	for token, started := "", false; token != "" || !started; started = true {
		if token != "" {
			input.SetNextToken(token)
		}

		resp, derr := svc.DescribeVpcs(input)
		if derr != nil {
			return derr
		}
		token = fields.Value(resp.NextToken)

		for _, item := range resp.Vpcs {
			attrs := BeautyVPC(item).With(s.globalFields...)

			if region, found := s.regions[resp.Metadata.Region]; found {
				attrs.SetField(fields.RegionField(region.GetID()))
			}

			conds := fields.FieldList(*attrs.GetField("vpc_id"))

			if _, perr := restclient.PostOrPatch[models.VPC](conds, attrs); perr != nil {
				err = perr
				color.Red("  sync vpc faield, %v", perr)
			} else {
				color.Green("  sync vpc ok, %v", attrs)
			}
		}
	}

	return err
}
