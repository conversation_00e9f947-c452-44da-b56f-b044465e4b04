package vpc

import (
	"gitlab.lilithgame.com/yunwei/pkg/logger"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
)

func (s *Syncer) CleanVPC(t int64, conds ...fields.Field) error {
	conds = append(conds,
		fields.LessThan("update_at", t),
		fields.FactoryField(s.cred.Factory.GetID()),
		fields.FactoryAccountField(s.cred.Account.GetID()),
		fields.Unlimited,
	)

	resp, err := restclient.List[models.VPC](conds...)
	if err != nil {
		return err
	}

	for _, r := range resp.Results {
		if oerr := restclient.Delete(r); oerr != nil {
			err = oerr
		}
	}

	logger.Debugf("remove %d vpc records update before %d", resp.Count, t)

	return err
}

func (s *Syncer) CleanSecurityGroups(t int64, conds ...fields.Field) error {
	conds = append(conds,
		fields.LessThan("update_at", t),
		fields.FactoryField(s.cred.Factory.GetID()),
		fields.FactoryAccountField(s.cred.Account.GetID()),
		fields.Unlimited,
	)

	resp, err := restclient.List[models.Security](conds...)
	if err != nil {
		return err
	}

	for _, r := range resp.Results {
		if oerr := restclient.Delete(r); oerr != nil {
			err = oerr
		}
	}

	logger.Debugf("remove %d security groups which updated before %d", resp.Count, t)

	return err
}

func (s *Syncer) CleanEIP(t int64, conds ...fields.Field) error {
	conds = append(conds,
		fields.LessThan("update_at", t),
		fields.FactoryField(s.cred.Factory.GetID()),
		fields.FactoryAccountField(s.cred.Account.GetID()),
		fields.In("version", "eip", "anycast"),
		fields.Unlimited,
	)

	resp, err := restclient.List[models.ElasticIP](conds...)
	if err != nil {
		return err
	}

	for _, r := range resp.Results {
		if oerr := restclient.Delete(r); oerr != nil {
			err = oerr
		}
		logger.Printf("remove %d(%s, version: %s) %v\n", r.GetID(), r.PublicIP, r.Version, err)
	}

	logger.Debugf("remove %d eip records update before %d", resp.Count, t)

	return err
}
