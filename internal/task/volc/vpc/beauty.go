package vpc

import (
	"fmt"

	"github.com/volcengine/volcengine-go-sdk/service/vpc"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/category"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
)

func BeautyEIP(i *vpc.EipAddressForDescribeEipAddressesOutput) fields.Fields {
	attrs := fields.NewFields(
		fields.NamedField("version", "eip"),

		fields.NamedPField("name", i.Name),
		fields.NamedPField("state", i.Status),

		fields.ExternalStatusField(fields.Value(i.Status)),
		fields.NamedPField(fields.PublicIPFieldKey, i.EipAddress),
		fields.NamedPField(fields.ExternalUUIDFieldKey, i.AllocationId),

		fields.NamedPField("bind_id", i.InstanceId),
		fields.NamedPField("bind_type", i.InstanceType),
		fields.NamedPField("isp", i.ISP),

		fields.NamedPField(fields.CreateTimeFieldKey, i.AllocationTime),
	)

	//  保留最后绑定的信息
	if bindId := fields.Value(i.InstanceId); bindId != "" {
		attrs.With(fields.NamedField("last_bind_id", bindId))
		attrs.With(fields.NamedPField("last_bind_type", i.InstanceType))
	}

	if productId := category.Category(*i.Name, ""); productId != nil {
		attrs.Set("product", productId)
	}

	return attrs
}

func BeautyVPC(i *vpc.VpcForDescribeVpcsOutput) fields.Fields {
	attrs := fields.NewFields(
		fields.StringPField("vpc_id", i.VpcId),
		fields.StringPField("name", i.VpcName),
		fields.StringPField("cidr_block", i.CidrBlock),
		fields.UpperStatusField(fields.Value(i.Status)),
		fields.NamedPField(fields.CreateTimeFieldKey, i.CreationTime),
		fields.StringPField("desc", i.Description),
	)

	if productId := category.Category(*i.VpcName, ""); productId != nil {
		attrs.Set("product", productId)
	}
	return attrs
}

func BeautySecurityGroup(i *vpc.SecurityGroupForDescribeSecurityGroupsOutput) fields.Fields {
	attrs := fields.NewFields(
		fields.StringPField("security_id", i.SecurityGroupId),
		fields.StringPField("name", i.SecurityGroupName),

		fields.NamedPField("service_managed", i.ServiceManaged),
		fields.StringPField("group_type", i.Type),
		fields.NamedPField(fields.CreateTimeFieldKey, i.CreationTime),
		fields.StringPField("desc", i.Description),
	)

	return attrs
}

func BeautySecurityGroupRule(i *vpc.PermissionForDescribeSecurityGroupAttributesOutput) fields.Fields {
	direction := fields.Value(i.Direction)

	attrs := fields.NewFields(
		fields.StringField("direction", direction),
		fields.UpperStringField("policy", fields.Value(i.Policy)),
		fields.UpperStringField("protocol", fields.Value(i.Protocol)),
		fields.IntPField("priority", i.Priority),
		fields.StringPField("description", i.Description),
		fields.StringPField(fields.CreateTimeFieldKey, i.CreationTime),
		// fields.StringPField("update_time", i.UpdateTime),
	)

	portRange := fmt.Sprintf("%d/%d", *i.PortStart, *i.PortEnd)

	switch direction {
	case "egress":
		attrs.SetOrNil("dest_ip", i.CidrIp)
		attrs.WithString("port", &portRange)
		attrs.SetOrNil("dest_sg", i.SourceGroupId)

	case "ingress":
		attrs.SetOrNil("ip", i.CidrIp)
		attrs.SetString("dest_port", &portRange)
		attrs.SetOrNil("src_sg", i.SourceGroupId)
	}

	return attrs
}
