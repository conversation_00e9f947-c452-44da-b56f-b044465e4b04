package vpc

import (
	"github.com/fatih/color"
	"github.com/volcengine/volcengine-go-sdk/service/vpc"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/cache"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
)

func (s *Syncer) SyncSecurityGroups(svc *vpc.VPC) (err error) {
	input := &vpc.DescribeSecurityGroupsInput{
		PageSize: fields.Int64(100),
	}

	vpcer := cache.VPCCacher()

	for token, started := "", false; token != "" || !started; started = true {
		if token != "" {
			input.SetNextToken(token)
		}

		resp, derr := svc.DescribeSecurityGroups(input)
		if derr != nil {
			return derr
		}
		token = fields.Value(resp.NextToken)

		for _, item := range resp.SecurityGroups {
			attrs := BeautySecurityGroup(item).With(s.globalFields...)

			if region, found := s.regions[resp.Metadata.Region]; found {
				attrs.SetField(fields.RegionField(region.GetID()))
			}

			if vpc, err := vpcer(fields.PString(item.VpcId)); err == nil {
				attrs.SetField(fields.VpcField(vpc.GetID()))
			}

			conds := fields.FieldList(*attrs.GetField("security_id"))

			if sg, perr := restclient.PostOrPatch[models.Security](conds, attrs); perr != nil {
				err = perr
				color.Red("  sync sg faield, %v", perr)
			} else {
				color.Green("  sync sg ok, %v", attrs)

				// TODO: need test
				s.SyncSecurityGroupRules(svc, sg.SecurityID)
			}
		}
	}

	return err
}

func (s *Syncer) SyncSecurityGroupRules(svc *vpc.VPC, sg string) (err error) {
	input := &vpc.DescribeSecurityGroupAttributesInput{
		SecurityGroupId: fields.Pointer(sg),
	}

	resp, derr := svc.DescribeSecurityGroupAttributes(input)
	if derr != nil {
		return derr
	}

	attrs := make([]fields.Fields, 0)

	for _, perm := range resp.Permissions {
		attr := BeautySecurityGroupRule(perm)
		attrs = append(attrs, attr)
	}

	restclient.PostSubResource[models.Security]("rules", sg, fields.Fields{
		"rules": attrs,
	})

	return err
}
