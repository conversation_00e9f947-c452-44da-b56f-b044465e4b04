package vks

import (
	"fmt"
	"time"

	"github.com/volcengine/volcengine-go-sdk/service/vke"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/category"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
)

func (s *Syncer) Sync(region string) error {
	sess, err := s.NewSession(region)
	if err != nil {
		return err
	}
	svc := vke.New(sess)

	const pageSize = 50
	input := &vke.ListClustersInput{
		PageSize: fields.Int32(pageSize),
	}

	for page, count := int32(1), 0; ; page++ {
		input.SetPageNumber(page)

		resp, err := svc.ListClusters(input)
		if err != nil {
			return err
		}

		for _, cluster := range resp.Items {
			startAt := time.Now().Unix()

			// sync cluster
			attrs := fields.NewFields(
				fields.StringPField("cluster_id", cluster.Id),
				fields.StringPField("cluster_name", cluster.Name),
				fields.StringPField("cluster_version", cluster.KubernetesVersion),
				fields.CreateTimeField(*cluster.CreateTime),
				fields.FactoryField(s.cred.Factory.GetID()),
				fields.FactoryAccountField(s.cred.Account.GetID()),
				fields.RegionIDField(region),
			)

			// product
			if productId := category.Category(*cluster.Name, ""); productId != nil {
				attrs.Set(fields.ProductFieldKey, *productId)
			}

			conds := fields.FieldList(
				*attrs.GetField("cluster_id"),
			)

			if k, perr := restclient.PostOrPatch[models.K8SCluster](conds, attrs); perr == nil {
				if *cluster.NodeStatistics.TotalCount != 0 {
					if serr := s.SyncClusterNodes(svc, *cluster.Id); serr == nil {
						cleanUpCluster(k.GetID(), startAt)
					}
				}
			}
		}

		count += len(resp.Items)

		if count >= int(*resp.TotalCount) {
			break
		}
	}

	return nil
}

func cleanUpCluster(clusterPk int, t int64) error {
	return restclient.DeleteSubResource[models.K8SCluster](
		"nodes",
		clusterPk,
		fields.NumberField("updated_before", t),
	)
}

func (s *Syncer) SyncClusterNodes(svc *vke.VKE, clusterID string) (err error) {
	const PageSize = 50

	input := &vke.ListNodesInput{
		PageSize: fields.Int32(PageSize),
		Filter: &vke.FilterForListNodesInput{
			ClusterIds: []*string{&clusterID},
		},
	}

	for page := int32(1); ; page++ {
		input.SetPageNumber(page)

		if nodeResp, lerr := svc.ListNodes(input); lerr == nil {
			for _, node := range nodeResp.Items {
				if fields.Value(node.IsVirtual) {
					continue
				}

				fmt.Printf("  %s: %s (virtual: %v)\n",
					fields.Value(node.InstanceId),
					fields.PString(node.Name),
					fields.Value(node.IsVirtual),
				)

				attrs := fields.NewFields(
					fields.StringField("cluster", clusterID),
					fields.StringPField(fields.InstanceIDFieldKey, node.InstanceId),
					fields.StringPField(fields.InstanceNameFieldKey, node.Name),
				)

				conds := fields.FieldList(*attrs.GetField(fields.InstanceIDFieldKey))
				if _, perr := restclient.PostOrPatch[models.K8SNode](conds, attrs); perr != nil {
					err = perr
				}
			}

			if len(nodeResp.Items) < PageSize {
				break
			}
		}
	}

	return
}
