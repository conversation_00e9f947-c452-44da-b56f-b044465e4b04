package vslb

import (
	"strings"

	"github.com/volcengine/volcengine-go-sdk/service/clb"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/category"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
)

// Beauty returns a fields.Fields object that contains the beautified fields of
// the clb.LoadBalancerForDescribeLoadBalancersOutput object.
//
// ref: https://www.volcengine.com/docs/6406/71772
func Beauty(l *clb.LoadBalancerForDescribeLoadBalancersOutput) fields.Fields {
	attr := fields.NewFields(
		fields.NamedField("lb_type", LBType),
		fields.NamedPField(fields.ExternalUUIDFieldKey, l.LoadBalancerId),
		fields.NamedPField(fields.ExternalNameFieldKey, l.LoadBalancerName),
		fields.NamedPField("ip_version", l.AddressIpV<PERSON>ion),
		fields.NamedPField("address_type", l.Type),
		fields.ExternalStatusField(fields.PString(l.Status)),
		fields.NamedPField(fields.CreateTimeFieldKey, l.CreateTime),

		fields.NamedField("vpc", nil),
	)

	status := fields.PLowerString(l.Status)
	if status == "active" {
		status = "running"
	}
	attr.SetField(fields.ExternalStatusField(status))

	if eipId := fields.Value(l.EipID); eipId != "" {
		//  公网地址
		attr.SetString("address", l.EipAddress)
	} else {
		// 私网地址
		attr.SetString("address", l.EniAddress)
	}

	if productID := resolveProductId(l); productID != nil {
		fields.SetValue(attr, fields.ProductFieldKey, productID)
	}

	return attr
}

func BeautyWith(l *clb.LoadBalancerForDescribeLoadBalancersOutput, additions ...fields.Field) fields.Fields {
	return Beauty(l).With(additions...)
}

func resolveClusterNameFromTags(tags []*clb.TagForDescribeLoadBalancersOutput) string {
	for _, tag := range tags {
		if strings.HasPrefix(*tag.Key, "volc:vke:cluster-id") {
			return category.ResolveClusterNameByID(*tag.Value)
		}
	}
	return ""
}

func resolveProductId(l *clb.LoadBalancerForDescribeLoadBalancersOutput) *int {
	if productID := category.Category(*l.LoadBalancerName, ""); productID != nil {
		return productID
	}

	if clusterName := resolveClusterNameFromTags(l.Tags); clusterName != "" {
		if productID := category.Category(clusterName, ""); productID != nil {
			return productID
		}
	}
	return nil
}

// BeautyListener returns a fields.Fields object that contains the beautified fields of
func BeautyListener(l *clb.ListenerForDescribeListenersOutput) fields.Fields {
	attr := fields.NewFields(
		fields.StringPField(fields.ExternalUUIDFieldKey, l.ListenerId),
		fields.StringPField(fields.ExternalNameFieldKey, l.ListenerName),
		fields.StringField(fields.ExternalStatusFieldKey, fields.PUpperString(l.Status)),

		fields.StringPField("acl_status", l.AclStatus),
		fields.StringPField("acl_type", l.AclType),

		fields.StringPField("scheduler", l.Scheduler),
		fields.StringPField("proto", l.Protocol),

		fields.NumberPField("port", l.Port),

		fields.CreateTimeField(fields.PString(l.CreateTime)),
	)

	// TODO: only first acl
	for _, acl := range l.AclIds {
		attr.SetString("acl_uuid", acl)
		break
	}

	return attr
}
