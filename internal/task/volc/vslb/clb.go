package vslb

import (
	"github.com/fatih/color"
	"github.com/volcengine/volcengine-go-sdk/service/clb"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
)

const LBType = "CLB"

func (s *Syncer) Sync(region string) error {
	sess, err := s.NewSession(region)
	if err != nil {
		return err
	}
	svc := clb.New(sess)

	return s.SyncLoadblanceInstances(svc, region)
}

func (s *Syncer) SyncLoadblanceInstances(svc *clb.CLB, region string) error {
	input := &clb.DescribeLoadBalancersInput{
		PageSize: fields.Int64(100),
	}

	var err error
	for n := int64(1); ; n++ {
		input.SetPageNumber(n)

		output, derr := svc.DescribeLoadBalancers(input)
		if derr != nil {
			return derr
		}

		for _, lb := range output.LoadBalancers {
			s.syncLoadblanceInstance(lb, region)
			s.SyncListeners(svc, lb.LoadBalancerId)
		}

		if len(output.LoadBalancers) < int(*output.PageSize) {
			break
		}
	}

	return err
}

func (s *Syncer) syncLoadblanceInstance(lb *clb.LoadBalancerForDescribeLoadBalancersOutput, region string) error {
	var err error

	// common fields
	commons := []fields.Field{
		fields.FactoryField(s.cred.Factory.GetID()),
		fields.FactoryAccountField(s.cred.Account.GetID()),
	}
	if r, ok := s.regions[region]; ok {
		commons = append(commons, fields.RegionField(r.GetID()))
	}

	attr := BeautyWith(lb, commons...)

	conds := []fields.Field{
		fields.StringPField(fields.ExternalUUIDFieldKey, lb.LoadBalancerId),
	}

	if _, perr := restclient.PostOrPatch[models.SLB](conds, attr); perr != nil {
		err = perr
		color.Red("syncLoadblanceInstance: %v", err)
	} else {
		color.Green("  success: %s %s", *lb.LoadBalancerId, *lb.LoadBalancerName)
	}

	return err
}

func (s *Syncer) SyncListeners(svc *clb.CLB, lbID *string) error {
	commons := []fields.Field{
		fields.FactoryField(s.cred.Factory.GetID()),
		fields.FactoryAccountField(s.cred.Account.GetID()),
		fields.StringPField("lb", lbID),
	}

	input := &clb.DescribeListenersInput{
		PageSize:       fields.Int64(100),
		LoadBalancerId: lbID,
	}

	var err error
	for n := int64(1); ; n++ {
		input.SetPageNumber(n)

		output, derr := svc.DescribeListeners(input)
		if derr != nil {
			return derr
		}

		for _, ln := range output.Listeners {
			attr := BeautyListener(ln).With(commons...)

			conds := []fields.Field{
				fields.StringPField(fields.ExternalUUIDFieldKey, ln.ListenerId),
			}

			if _, perr := restclient.PostOrPatch[models.SLBListener](conds, attr); perr != nil {
				err = perr
				color.Red("SyncListeners: %v", err)
			} else {
				color.Green("  listener: %s %s", *ln.ListenerId, *ln.ListenerName)
			}
		}

		if len(output.Listeners) < int(*input.PageSize) {
			break
		}

	}

	return err
}
