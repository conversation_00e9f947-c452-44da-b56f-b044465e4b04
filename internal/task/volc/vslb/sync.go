package vslb

import (
	"time"

	"github.com/volcengine/volcengine-go-sdk/volcengine"
	"github.com/volcengine/volcengine-go-sdk/volcengine/session"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/factory"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
	vtask "gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/task/volc"
)

type Syncer struct {
	cred    *vtask.FactoryCrendential
	regions map[string]*models.Region
}

func NewSyncer(accountKey string) (*Syncer, error) {
	cred, err := vtask.CreateCredentialWithAccount(accountKey)
	if err != nil {
		return nil, err
	}

	return &Syncer{
		cred: cred,
		regions: LoadRegions(
			fields.NamedField("factory__name", "火山云"),
			fields.Unlimited,
		),
	}, nil
}

func (s *Syncer) NewSession(region string) (*session.Session, error) {
	config := volcengine.NewConfig().
		WithCredentials(s.cred.Credential).
		WithRegion(region)

	return session.NewSession(config)
}

func Sync(account string) error {
	s, err := NewSyncer(account)
	if err != nil {
		return err
	}

	start := time.Now().Unix()

	for region := range s.regions {
		if serr := s.Sync(region); serr != nil {
			err = serr
		}
	}

	if err == nil {
		err = s.Clean(start)
	}

	return err
}

func SyncMany(keys ...factory.FactoryKeyType) error {
	var err error

	for _, key := range keys {
		if serr := Sync(key.String()); serr != nil {
			err = serr
		}
	}

	return err
}

func SyncAll() error {
	return SyncMany(factory.VolcFactories...)
}
