package vslb

import (
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
)

func LoadZones(kwargs ...fields.Field) map[string]models.Zone {
	zonePage, err := restclient.List[models.Zone](kwargs...)
	if err != nil {
		return nil
	}

	v := make(map[string]models.Zone)
	for _, zone := range zonePage.Results {
		v[zone.ZoneID] = zone
	}

	return v
}

func LoadRegions(conds ...fields.Field) map[string]*models.Region {
	regions, err := restclient.List[models.Region](conds...)
	if err != nil {
		return nil
	}

	v := make(map[string]*models.Region)
	for _, r := range regions.Results {
		v[r.RegionID] = &r
	}

	return v
}
