package vdisk

import (
	"github.com/fatih/color"
	"github.com/volcengine/volcengine-go-sdk/service/storageebs"
	"gitlab.lilithgame.com/yunwei/pkg/logger"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/category"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
)

func (s *Syncer) Sync() error {
	var err error

	// FIXME: Need fix
	for _, region := range []string{
		"cn-shanghai",
		"cn-beijing",
		"cn-guangzhou",
		"ap-southeast-1",
	} {
		if serr := s.SyncInRegion(region); serr != nil {
			err = serr
		}
	}

	return err
}

func (s *Syncer) SyncInRegion(region string) error {
	sess, err := s.NewSession(region)
	if err != nil {
		return err
	}

	svc := storageebs.New(sess)

	for n, ps := int32(1), int32(100); err == nil; n++ {
		input := &storageebs.DescribeVolumesInput{
			PageNumber: fields.Pointer(n),
			PageSize:   fields.Pointer(ps),
		}

		resp, derr := svc.DescribeVolumes(input)
		if derr != nil {
			return derr
		}

		for _, v := range resp.Volumes {
			attrs := Beauty(v)
			attrs.With(
				fields.FactoryField(s.cred.Factory.GetID()),
				fields.FactoryAccountField(s.cred.Account.GetID()),
			)

			if z, exists := s.zones[*v.ZoneId]; exists {
				attrs.With(
					fields.ZoneField(z.GetID()),
					fields.RegionField(z.RegionName),
				)
			}

			// product rewrite if not set
			if productID := attrs.GetInt(fields.ProductFieldKey); productID == nil {
				if productID = category.GetMatchedProductID(s.cred.Account.Channel); productID != nil {
					fields.SetValue(attrs, fields.ProductFieldKey, productID)
				}
			}

			// Sync record
			conds := []fields.Field{
				fields.NamedPField("disk_id", v.VolumeId),
			}

			_, perr := restclient.PostOrPatch[models.BlockDisk](conds, attrs)
			if perr != nil {
				logger.Error("sync machine error", "error", err, "attrs", attrs)
				err = perr
			} else {
				color.Green("sync disk success: %s %s", *v.VolumeId, *v.VolumeName)
			}
		}

		if len(resp.Volumes) < int(*resp.PageSize) {
			break
		}
	}

	return err
}
