package vdisk

import (
	"strings"

	"github.com/volcengine/volcengine-go-sdk/service/storageebs"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/category"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
)

func Beauty(v *storageebs.VolumeForDescribeVolumesOutput) fields.Fields {
	attr := fields.NewFields(
		fields.NamedPField("disk_id", v.VolumeId),
		fields.NamedPField("name", v.VolumeName),
		fields.NamedPField("category", v.VolumeType),
		fields.NamedPField("disk_type", v.Kind),
		fields.NamedPField("device", v.DeviceName),
		fields.NamedPField("instance_id", v.InstanceId),
		fields.NamedPField("create_time", v.CreatedAt),
	)

	if diskSize, err := v.Size.Int64(); err == nil {
		attr.Set("size", diskSize)
	}

	// rename status
	status := fields.PLowerString(v.Status)
	if status == "attached" {
		status = "running"
	}
	attr.SetField(fields.StatusField(status))

	// tags
	externalTags := make([]string, 0)
	for _, tag := range v.Tags {
		externalTags = append(externalTags, *tag.Key+":"+fields.Value(tag.Value))
	}
	attr.Set("tags", strings.Join(externalTags, ","))

	// product
	if productID := category.Category(*v.VolumeName, ""); productID != nil {
		attr.SetField(fields.ProductField(*productID))
	}

	if v.InstanceId != nil {
		attr.SetString("last_bind_instance", v.InstanceId)
	}

	return attr
}
