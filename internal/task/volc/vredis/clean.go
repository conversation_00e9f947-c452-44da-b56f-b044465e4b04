package vredis

import (
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
)

func (s *Syncer) Clean(t int64, cond ...fields.Field) {
	cond = append(cond,
		fields.FactoryField(s.cred.Factory.GetID()),
		fields.FactoryAccountField(s.cred.Account.GetID()),
		fields.LessThan("update_at", t),
	)
	if resp, err := restclient.ListAll[models.Redis](cond...); err == nil {
		for _, m := range resp.Results {
			restclient.Delete(m)
		}
	}
}
