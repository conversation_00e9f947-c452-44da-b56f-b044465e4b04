package vredis

import (
	"time"

	"github.com/volcengine/volcengine-go-sdk/volcengine"
	"github.com/volcengine/volcengine-go-sdk/volcengine/session"
	"gitlab.lilithgame.com/yunwei/pkg/logger"
	"gitlab.lilithgame.com/yunwei/pkg/wrap"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/factory"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
	vtask "gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/task/volc"
)

type Syncer struct {
	cred  *vtask.FactoryCrendential
	zones map[string]models.Zone
}

func NewSyncer(accountKey string) (*Syncer, error) {
	cred, err := vtask.CreateCredentialWithAccount(accountKey)
	if err != nil {
		return nil, err
	}

	return &Syncer{
		cred: cred,
		zones: LoadZones(
			fields.NamedField("factory__name", "火山云"),
			fields.Unlimited,
		),
	}, nil
}

func (s *Syncer) NewSession(region string) (*session.Session, error) {
	config := volcengine.NewConfig().
		WithCredentials(s.cred.Credential).
		WithRegion(region)

	return session.NewSession(config)
}

func SyncAll(factoryKeys ...factory.FactoryKeyType) error {
	var err error

	for _, key := range factoryKeys {
		t := time.Now().Unix()
		s := wrap.Unwrap(NewSyncer(key.String()))

		if serr := s.Sync(); serr != nil {
			logger.Printf("sync error", "error", err, "account", key)
			err = serr
		} else {
			s.Clean(t)
		}
	}

	return err
}
