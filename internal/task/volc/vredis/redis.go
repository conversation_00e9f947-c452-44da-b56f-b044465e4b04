package vredis

import (
	"github.com/fatih/color"
	"github.com/volcengine/volcengine-go-sdk/service/redis"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/category"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
)

func (s *Syncer) Sync() error {
	var err error

	// TODO: do not hard code
	for _, region := range []string{
		"cn-shanghai",
		"cn-beijing",
		"cn-guangzhou",
		"ap-southeast-1",
	} {
		if serr := s.SyncInRegion(region); serr != nil {
			err = serr
		}
	}

	return err
}

func (s *Syncer) SyncInRegion(region string) error {
	sess, err := s.NewSession(region)
	if err != nil {
		return err
	}

	svc := redis.New(sess)

	input := &redis.DescribeDBInstancesInput{}
	input.SetPageSize(100)
	input.SetRegionId(region)

	for n := int32(1); err == nil; n++ {
		input.SetPageNumber(n)

		resp, derr := svc.DescribeDBInstances(input)
		if derr != nil {
			return derr
		}

		for _, i := range resp.Instances {
			detail, _ := svc.DescribeDBInstanceDetail(&redis.DescribeDBInstanceDetailInput{
				InstanceId: i.InstanceId,
			})

			attrs := BeautyDetail(detail).With(
				fields.FactoryField(s.cred.Factory.GetID()),
				fields.FactoryAccountField(s.cred.Account.GetID()),
			)

			// region
			if z, exists := s.zones[*i.ZoneIds[0]]; exists {
				attrs.With(fields.RegionField(z.Region))
			}

			// product rewrite if not set
			if productID := attrs.GetInt(fields.ProductFieldKey); productID == nil {
				if productID = category.GetMatchedProductID(s.cred.Account.Channel); productID != nil {
					fields.SetValue(attrs, fields.ProductFieldKey, productID)
				}
			}

			// Sync record
			conds := []fields.Field{
				fields.NamedPField(fields.ExternalUUIDFieldKey, i.InstanceId),
			}

			if _, perr := restclient.PostOrPatch[models.Redis](conds, attrs); perr != nil {
				color.Red("sync redis instance error", "error", perr, "attrs", attrs)
				err = perr
			} else {
				color.Green("sync redis instance success: %s, attr: %+v", *i.InstanceId, attrs)

				// sync whitelist
				if swerr := s.SyncWhitelist(svc, *i.InstanceId, region); swerr != nil {
					color.Red("  sync whiltelist error, %v", swerr)
				}
			}
		}

		if len(resp.Instances) < 100 {
			break
		}
	}

	return err
}
