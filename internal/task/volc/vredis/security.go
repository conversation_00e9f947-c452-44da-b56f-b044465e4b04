package vredis

import (
	"time"

	"github.com/fatih/color"
	"github.com/volcengine/volcengine-go-sdk/service/redis"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
)

func (s *Syncer) SyncWhitelist(svc *redis.REDIS, dbID string, region string) (err error) {
	input := &redis.DescribeAllowListsInput{
		InstanceId: fields.Pointer(dbID),
		RegionId:   fields.Pointer(region),
	}
	resp, derr := svc.DescribeAllowLists(input)
	if derr != nil {
		return derr
	}

	color.Blue("sync acl with %s, count %d", dbID, len(resp.AllowLists))

	startAt := time.Now().Unix()
	for _, acl := range resp.AllowLists {
		attrs := fields.Fields{
			"group_id":   fields.Value(acl.AllowListId),
			"group_name": fields.Value(acl.AllowListName),
			"db":         dbID,
		}

		if detailResp, detailErr := svc.DescribeAllowListDetail(&redis.DescribeAllowListDetailInput{
			AllowListId: acl.AllowListId,
		}); detailErr == nil {
			attrs.SetString("ip_list", detailResp.AllowList)
		}

		conds := fields.FieldList(
			fields.StringField("db", dbID),
			*attrs.GetField("group_name"),
		)
		if _, perr := restclient.PostOrPatch[models.RedisWhitelist](conds, attrs); perr != nil {
			err = perr
			color.Red("  sync redis whitelist err, %v", perr)
		} else {
			color.Green("  sync redis whitelist ok, %+v", attrs)
		}
	}

	// clean outdated whitelists
	if err == nil {
		restclient.DeleteSubResource[models.Redis](
			"whitelist",
			dbID,
			fields.NumberField("updated_before", startAt),
		)
	}

	return err
}
