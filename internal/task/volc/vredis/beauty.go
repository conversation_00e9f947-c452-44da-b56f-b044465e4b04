package vredis

import (
	"fmt"
	"strings"

	"github.com/volcengine/volcengine-go-sdk/service/redis"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/category"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
)

func BeautyWith(d *redis.InstanceForDescribeDBInstancesOutput, additions ...fields.Field) fields.Fields {
	attr := Beauty(d)
	attr.With(additions...)
	return attr
}

func Beauty(d *redis.InstanceForDescribeDBInstancesOutput) fields.Fields {
	attr := fields.NewFields(
		fields.NamedPField(fields.ExternalUUIDFieldKey, d.InstanceId),
		fields.NamedPField(fields.ExternalNameFieldKey, d.InstanceName),
		fields.ExternalStatusField(*d.Status),
		fields.NamedPField(fields.CreateTimeFieldKey, d.CreateTime),
		fields.NamedField("engine", "Redis"),
		fields.NilField("edition"),
		fields.NilField("port"),
		fields.NamedPField("version", d.EngineVersion),
		fields.NamedPField("capacity", d.Capacity.Total),
	)

	// redis type: 0: cluster, 1: standard
	if types := fields.PLowerString(d.InstanceClass); types == "standalone" {
		attr.Set("types", 1)
	} else {
		attr.Set("types", 0)
	}

	// product
	if productID := category.Category(*d.InstanceName, ""); productID != nil {
		attr.SetField(fields.ProductField(*productID))
	}

	return attr
}

func BeautyDetail(d *redis.DescribeDBInstanceDetailOutput) fields.Fields {
	attr := fields.NewFields(
		fields.NamedPField(fields.ExternalUUIDFieldKey, d.InstanceId),
		fields.NamedPField(fields.ExternalNameFieldKey, d.InstanceName),
		fields.ExternalStatusField(*d.Status),
		fields.NamedPField(fields.CreateTimeFieldKey, d.CreateTime),
		fields.NamedField("engine", "Redis"),
		fields.NilField("edition"),
		fields.NilField("port"),
		fields.NamedPField("version", d.EngineVersion),
		fields.NamedPField("capacity", d.Capacity.Total),
	)

	// redis type: 0: cluster, 1: standard
	if types := fields.PLowerString(d.InstanceClass); types == "standalone" {
		attr.Set("types", 1)
	} else {
		attr.Set("types", 0)
	}

	// conn
	conn := make([]string, 0)
	for _, addr := range d.VisitAddrs {
		conn = append(conn, fmt.Sprintf("%s:%s", *addr.Address, *addr.Port))
	}
	attr.With(fields.NamedField("conn", strings.Join(conn, ",")))

	// port
	attr.SetAsInt("port", d.VisitAddrs[0].Port)

	// product
	if productID := category.Category(*d.InstanceName, ""); productID != nil {
		attr.SetField(fields.ProductField(*productID))
	}

	return attr
}
