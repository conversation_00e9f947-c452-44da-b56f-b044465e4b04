package vmysql

import (
	"strings"
	"time"

	"github.com/fatih/color"
	rds "github.com/volcengine/volcengine-go-sdk/service/rdsmysqlv2"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
)

func (s *Syncer) SyncWhitelist(svc *rds.RDSMYSQLV2, dbID string, region string) (err error) {
	input := &rds.DescribeAllowListsInput{
		InstanceId: fields.Pointer(dbID),
		RegionId:   fields.Pointer(region),
	}
	resp, derr := svc.DescribeAllowLists(input)
	if derr != nil {
		return derr
	}

	color.Blue("sync acl with %s, count %d", dbID, len(resp.AllowLists))

	startAt := time.Now().Unix()
	for _, acl := range resp.AllowLists {
		attrs := fields.Fields{
			// "group_id":   fields.Value(acl.AllowListId),
			"group_name": fields.Value(acl.AllowListName),
			"ip_type":    *acl.AllowListType,
			"db":         dbID,
		}

		// get ip list
		iplist := []string{}
		if bindinfo := acl.SecurityGroupBindInfos; len(bindinfo) > 0 {
			for _, allowlist := range bindinfo {
				for _, ip := range allowlist.IpList {
					iplist = append(iplist, *ip)
				}
			}
		}
		attrs.Set("ip_list", strings.Join(iplist, ","))

		// if detailResp, detailErr := svc.DescribeAllowListDetail(&rds.DescribeAllowListDetailInput{
		// 	AllowListId: acl.AllowListId,
		// }); detailErr == nil {
		// 	attrs.SetString("ip_list", detailResp.AllowList)
		// }

		conds := fields.FieldList(
			fields.StringField("db", dbID),
			*attrs.GetField("group_name"),
		)
		if _, perr := restclient.PostOrPatch[models.MySQLWhitelist](conds, attrs); perr != nil {
			err = perr
			color.Red("  sync mysql whitelist err, %v", perr)
		} else {
			color.Green("  sync mysql whitelist ok, %+v", attrs)
		}
	}

	// clean outdated whitelists
	if err == nil {
		restclient.DeleteSubResource[models.MySQL](
			"whitelist",
			dbID,
			fields.NumberField("updated_before", startAt),
		)
	}

	return err
}
