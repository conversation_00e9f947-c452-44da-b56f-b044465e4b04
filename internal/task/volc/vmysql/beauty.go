package vmysql

import (
	"strings"

	mysql "github.com/volcengine/volcengine-go-sdk/service/rdsmysqlv2"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/category"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
)

func BeautyWith(d *mysql.InstanceForDescribeDBInstancesOutput, additions ...fields.Field) fields.Fields {
	attr := Beauty(d)
	attr.With(additions...)
	return attr
}

func Beauty(d *mysql.InstanceForDescribeDBInstancesOutput) fields.Fields {
	attr := fields.NewFields(
		fields.NamedPField(fields.ExternalUUIDFieldKey, d.InstanceId),
		fields.NamedPField(fields.ExternalNameFieldKey, d.InstanceName),
		fields.ExternalStatusField(*d.InstanceStatus),
		fields.NamedField("engine", "MySQL"),
		fields.NamedPField("version", d.DBEngineVersion),
		fields.NamedPField(fields.CreateTimeFieldKey, d.CreateTime),
	)

	// product
	if productID := category.Category(*d.InstanceName, ""); productID != nil {
		attr.SetField(fields.ProductField(*productID))
	}

	return attr
}

func BeautyDetail(d *mysql.DescribeDBInstanceDetailOutput) fields.Fields {
	attr := fields.NewFields()

	// basic info
	basic := d.BasicInfo
	attr.With(
		fields.NamedPField(fields.ExternalUUIDFieldKey, basic.InstanceId),
		fields.NamedPField(fields.ExternalNameFieldKey, basic.InstanceName),
		fields.ExternalStatusField(*basic.InstanceStatus),
		// fields.NamedField("engine", "mysql"),
		// fields.NamedPField("version", basic.DBEngineVersion),
		fields.NamedPField(fields.CreateTimeFieldKey, basic.CreateTime),

		fields.NumberPField("cpu", basic.VCPU),
		fields.NumberField("mem", (*basic.Memory)*1024),
		fields.NumberPField("disk", basic.StorageSpace),
		fields.StringPField("flavor_name", basic.NodeSpec),
	)
	// engine and version
	if version := *basic.DBEngineVersion; version != "" {
		// parse 'MySQL_5_7' to 'mysql' and '5.7'
		parts := strings.Split(version, "_")
		if len(parts) == 3 {
			attr.Set("engine", strings.ToLower(parts[0]))
			attr.Set("version", strings.Join(parts[1:], "."))
		}
	}
	// external_tags
	if tags := basic.Tags; len(tags) > 0 {
		tagList := make([]map[string]any, 0)
		for _, tag := range tags {
			tagList = append(tagList, map[string]any{
				"k": *tag.Key,
				"v": *tag.Value,
			})
		}
		attr.SetJSON(fields.ExternalTagsFieldKey, tagList)
	}

	// endpoints
	ep := d.Endpoints[0]
	attr.SetAsInt("port", ep.Addresses[0].Port)

	// conn
	conn := make([]string, 0)
	for _, a := range ep.Addresses {
		conn = append(conn, *a.Domain)
	}
	attr.Set("conn", strings.Join(conn, ","))

	// product
	if productID := category.Category(*basic.InstanceName, ""); productID != nil {
		attr.SetField(fields.ProductField(*productID))
	}

	return attr
}
