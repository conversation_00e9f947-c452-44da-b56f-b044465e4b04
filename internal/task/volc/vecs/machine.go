package vecs

import (
	"time"

	"github.com/fatih/color"
	"github.com/volcengine/volcengine-go-sdk/service/ecs"
	"github.com/volcengine/volcengine-go-sdk/service/vpc"
	"github.com/volcengine/volcengine-go-sdk/volcengine/session"
	"gitlab.lilithgame.com/yunwei/pkg/logger"

	beauty "gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/beauty/volc"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/category"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
	vpc1 "gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/task/volc/vpc"
)

func (s *Syncer) Sync() error {
	var err error

	startTime := time.Now().Unix()

	for _, region := range []string{
		"cn-shanghai",
		"cn-beijing",
		"cn-guangzhou",
		// "ap-southease-1",
	} {
		if serr := s.SyncInRegion(region); serr != nil {
			err = serr
		}
	}

	if err == nil {
		s.Clean(startTime)
	}

	return err
}

func (s *Syncer) SyncInRegion(region string) error {
	sess, err := s.NewSession(region)
	if err != nil {
		return err
	}

	svc := ecs.New(sess)

	for nextToken, started := "", false; nextToken != "" || !started; started = true {
		input := &ecs.DescribeInstancesInput{}
		if nextToken != "" {
			input.SetNextToken(nextToken)
		}

		resp, err := svc.DescribeInstances(input)
		if err != nil {
			return err
		}

		for _, instance := range resp.Instances {
			attrs := beauty.BeautyHost(instance)
			attrs.With(
				fields.FactoryField(s.cred.Factory.GetID()),
				fields.FactoryAccountField(s.cred.Account.GetID()),
			)

			if z, exists := s.zones[*instance.ZoneId]; exists {
				attrs.SetField(fields.ZoneField(z.GetID()))
			}

			// product rewrite if not set
			if productID := attrs.GetInt(fields.ProductFieldKey); productID == nil {
				if productID = category.GetMatchedProductID(s.cred.Account.Channel); productID != nil {
					fields.SetValue(attrs, fields.ProductFieldKey, productID)
				}
			}

			// Sync record
			conds := []fields.Field{
				fields.ExternalUUIDField(fields.PString(instance.InstanceId)),
			}

			_, err = restclient.PostOrPatch[models.Machine](conds, attrs)
			if err != nil {
				logger.Error("sync machine error", "error", err, "attrs", attrs)
				color.Red("sync machine %s %s failed\n", fields.PString(instance.InstanceId), fields.PString(instance.InstanceName))
			} else {
				color.Green("sync machine %s %s %s\n",
					fields.PString(instance.InstanceId),
					fields.PString(instance.InstanceName),
					fields.PString(instance.Status),
				)
			}

			s.syncAssociatedEIPs(sess, instance.InstanceId)
		}

		nextToken = fields.PString(resp.NextToken)
	}

	return err
}

func (s *Syncer) syncAssociatedEIPs(sess *session.Session, instanceId *string) error {
	cli := vpc.New(sess)

	input := &vpc.DescribeEipAddressesInput{
		AssociatedInstanceId: instanceId,
	}
	output, err := cli.DescribeEipAddresses(input)
	if err != nil {
		return err
	}

	for _, eip := range output.EipAddresses {
		attr := vpc1.BeautyEIP(eip).With(
			fields.FactoryField(s.cred.Factory.GetID()),
			fields.FactoryAccountField(s.cred.Account.GetID()),
		)
		cond := []fields.Field{
			fields.ExternalUUIDField(fields.PString(eip.AllocationId)),
		}
		restclient.PostOrPatch[models.ElasticIP](cond, attr)
	}

	return nil
}
