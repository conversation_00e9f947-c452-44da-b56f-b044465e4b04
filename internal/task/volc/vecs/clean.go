package vecs

import (
	"time"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
)

func (s *Syncer) Clean(t int64, conds ...fields.Field) error {
	var err error

	if cerr := s.CleanOutdatedMachine(t, conds...); cerr != nil {
		err = cerr
	}

	if cerr := s.CleanEmptyAccountMachine(t, -time.Hour*4, conds...); cerr != nil {
		err = cerr
	}

	return err
}

func (s *Syncer) CleanOutdatedMachine(t int64, cond ...fields.Field) error {
	cond = append(cond,
		fields.FactoryField(s.cred.Factory.GetID()),
		fields.FactoryAccountField(s.cred.Account.GetID()),
		fields.NumberField("updated_before", t),
		fields.Not(fields.ExternalStatusFieldKey, fields.ExternalStatusDeleted.Value),
	)
	resp, ferr := restclient.ListAll[models.Machine](cond...)
	if ferr != nil {
		return ferr
	}

	var err error
	for _, m := range resp.Results {
		if _, perr := restclient.PatchAttrs[models.Machine](m.GetID(), fields.ExternalStatusDeleted); perr != nil {
			err = perr
		}
	}

	return err
}

func (s *Syncer) CleanEmptyAccountMachine(t int64, delta time.Duration, cond ...fields.Field) (err error) {
	cond = append(cond,
		fields.FactoryField(s.cred.Factory.GetID()),
		fields.IsNull(fields.FactoryAccountFieldKey),
		fields.Not(fields.ExternalStatusFieldKey, fields.ExternalStatusDeleted.Value),
	)

	ago := time.Unix(t, 0).Add(delta).Unix()
	cond = append(cond, fields.NumberField("updated_before", ago))

	if resp, derr := restclient.ListAll[models.Machine](cond...); derr == nil {
		for _, m := range resp.Results {
			if _, perr := restclient.PatchAttrs[models.Machine](m.GetID(), fields.ExternalStatusDeleted); perr != nil {
				err = perr
			}
		}
	}
	return
}
