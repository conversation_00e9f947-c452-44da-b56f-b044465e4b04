package vecs

import (
	"fmt"
	"os"

	"github.com/volcengine/volcengine-go-sdk/service/ecs"
	"github.com/volcengine/volcengine-go-sdk/volcengine/session"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
)

func (s *Syncer) SyncRegion() error {
	regionsRecorded, err := restclient.List[models.Region](
		fields.NamedField("factory__id", s.cred.Factory.GetID()),
		fields.Unlimited,
	)
	if err != nil {
		return err
	}

	recordMap := make(map[string]int)
	for _, r := range regionsRecorded.Results {
		recordMap[r.RegionID] = 1
	}

	regions, err := s.ListRegions()
	if err != nil {
		return fmt.Errorf("list regions from cloud error: %w", err)
	}

	for _, r := range regions {
		if _, ok := recordMap[r]; !ok {
			attrs := fields.NewFields(
				fields.NamedField("region_id", r),
				fields.NamedField("name", r),
				fields.NamedField("factory", s.cred.Factory.GetID()),
			)
			if _, err := restclient.Post[models.Region](attrs); err != nil {
				fmt.Fprintf(os.Stderr, "create region %s error: %v\n", r, err)
			}
		}
	}

	return nil
}

func (s *Syncer) SyncZones() error {
	// region 在数据库中的记录
	regionsRecorded, err := restclient.List[models.Region](
		fields.NamedField("factory__id", s.cred.Factory.GetID()),
		fields.Unlimited,
	)
	if err != nil {
		return fmt.Errorf("list regions from db failed, %v", err)
	}

	if regionsRecorded.Count == 0 {
		return fmt.Errorf("no region recorded, please sync first")
	}

	// zone 在数据库中的记录
	zonesRecorded, err := restclient.List[models.Zone](
		fields.NamedField("factory__id", s.cred.Factory.GetID()),
		fields.Unlimited,
	)
	if err != nil {
		return err
	}

	zonesRecordedMap := make(map[string]int)
	for _, z := range zonesRecorded.Results {
		zonesRecordedMap[z.ZoneID] = z.Region
	}

	// 从云端获取 zone, 并比对数据库中的记录
	for _, r := range regionsRecorded.Results {
		zones, err := s.ListZones(r.Name)
		if err != nil {
			fmt.Fprintf(os.Stderr, "list zones from cloud error: %v\n", err)
			continue
		}

		for _, zone := range zones {
			if _, ok := zonesRecordedMap[zone]; !ok {

				has, _ := restclient.Find(&models.Zone{},
					fields.NamedField("zone_id", zone),
					fields.FactoryField(s.cred.Factory.GetID()),
				)
				if has {
					continue
				}

				attrs := fields.NewFields(
					fields.NamedField("zone_id", zone),
					fields.NamedField("name", zone),
					fields.NamedField("region", r.GetID()),
					fields.NamedField("factory", s.cred.Factory.GetID()),
				)
				if _, err := restclient.Post[models.Zone](attrs); err != nil {
					fmt.Fprintf(os.Stderr, "create zone %s error: %v\n", zone, err)
				}
			}
		}
	}

	return nil
}

func (s *Syncer) ListRegions() ([]string, error) {
	sess := session.Must(s.NewSession("cn-shanghai"))
	svc := ecs.New(sess)

	input := &ecs.DescribeRegionsInput{}
	resp, err := svc.DescribeRegions(input)
	if err != nil {
		return nil, err
	}

	regions := make([]string, 0)
	for _, reg := range resp.Regions {
		regions = append(regions, *reg.RegionId)
	}
	return regions, nil
}

func (s *Syncer) ListZones(region string) ([]string, error) {
	sess := session.Must(s.NewSession(region))
	svc := ecs.New(sess)

	input := &ecs.DescribeZonesInput{}
	resp, err := svc.DescribeZones(input)
	if err != nil {
		return nil, err
	}

	zones := make([]string, 0)
	for _, zone := range resp.Zones {
		zones = append(zones, *zone.ZoneId)
	}

	return zones, nil
}

func LoadZones(kwargs ...fields.Field) map[string]models.Zone {
	zonePage, err := restclient.List[models.Zone](kwargs...)
	if err != nil {
		return nil
	}

	v := make(map[string]models.Zone)
	for _, zone := range zonePage.Results {
		v[zone.ZoneID] = zone
	}

	return v
}
