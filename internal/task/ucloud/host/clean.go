package host

import (
	"time"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
)

func (s *Syncer) Clean(t int64, conds ...fields.Field) error {
	var err error

	if cerr := s.cleanOutdatedMachine(t, conds...); cerr != nil {
		err = cerr
	}

	if cerr := s.cleanEmptyAccountMachine(t, -time.Hour*4, conds...); cerr != nil {
		err = cerr
	}

	return err
}

func (s *Syncer) cleanOutdatedMachine(t int64, conds ...fields.Field) error {
	conds = append(conds,
		fields.FactoryAccountField(s.cred.Account.GetID()),
		fields.NumberField("updated_before", t),
		fields.Not(fields.ExternalStatusFieldKey, fields.ExternalStatusDeleted.Value),
	)
	if resp, err := restclient.ListAll[models.Machine](conds...); err == nil {
		for _, m := range resp.Results {
			restclient.PatchAttrs[models.Machine](m.GetID(), fields.ExternalStatusDeleted)
		}
	}

	return nil
}

func (s *Syncer) cleanEmptyAccountMachine(t int64, delta time.Duration, conds ...fields.Field) error {
	conds = append(conds,
		fields.FactoryField(s.cred.Factory.GetID()),
		fields.IsNull(fields.FactoryAccountFieldKey),
		fields.Not(fields.ExternalStatusFieldKey, fields.ExternalStatusDeleted.Value),
	)

	ago := time.Unix(t, 0).Add(delta).Unix()
	conds = append(conds, fields.NamedField("updated_before", ago))

	if resp, err := restclient.ListAll[models.Machine](conds...); err == nil {
		for _, m := range resp.Results {
			restclient.PatchAttrs[models.Machine](m.GetID(), fields.ExternalStatusDeleted)
		}
	}

	return nil
}
