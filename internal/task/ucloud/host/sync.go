package host

import (
	"fmt"
	"time"

	"github.com/fatih/color"
	"github.com/ucloud/ucloud-sdk-go/services/uhost"
	"github.com/ucloud/ucloud-sdk-go/ucloud"
	"gitlab.lilithgame.com/yunwei/pkg/logger"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/category"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
	ucloudtask "gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/task/ucloud"
)

type Syncer struct {
	cred     *ucloudtask.FactoryCrendential
	regions  map[string]string
	projects []string
	zones    map[string]models.Zone
}

func NewSyncer(accountKey string) (*Syncer, error) {
	fmt.Printf("new ucloud syncer with account %s\n", accountKey)
	cred, err := ucloudtask.CreateCredentialWithAccount(accountKey)
	if err != nil {
		return nil, fmt.Errorf("create credential error: %w", err)
	}

	regions, err := ListRegion(cred.Credential)
	if err != nil {
		return nil, fmt.Errorf("list region error: %w", err)
	}

	projects, err := ListProject(cred.Credential)
	if err != nil {
		return nil, fmt.Errorf("list project error: %w", err)
	}

	zones := LoadZones(
		fields.NamedField("factory__id", cred.Factory.GetID()),
		fields.Unlimited,
	)

	// sync regions
	if serr := SyncRegions(cred.Factory.GetID(), regions); serr != nil {
		color.Red("sync regions error, %w", serr)
	} else {
		color.Green("sync regions success")
	}

	return &Syncer{
		cred:     cred,
		regions:  regions,
		projects: projects,
		zones:    zones,
	}, nil
}

func (s *Syncer) Sync() error {
	var err error
	for region := range s.regions {
		for _, project := range s.projects {
			if serr := s.SyncProject(region, project); serr != nil {
				color.Red("sync project %s in region %s error, %w", project, region, serr)
				err = serr
			}
		}
	}
	return err
}

func (s *Syncer) SyncProject(region string, projectID string) error {
	cfg := ucloud.NewConfig()
	client := uhost.NewClient(&cfg, s.cred.Credential)

	req := client.NewDescribeUHostInstanceRequest()
	req.Region = fields.Pointer(region)
	req.ProjectId = fields.Pointer(projectID)

	for total, count := 0, 0; count <= total; {
		fmt.Printf("sync uhost %d/%d\n", count, total)

		resp, err := client.DescribeUHostInstance(req)
		if err != nil {
			return nil
		}
		total = resp.TotalCount

		hosts := resp.UHostSet
		if len(hosts) == 0 {
			break
		}
		count += len(hosts)

		// 同步存在的 uhost
		for _, host := range hosts {
			attrs := Beauty(host)
			attrs.With(
				fields.ZoneField(s.zones[host.Zone].GetID()),
				fields.DescField(projectID),
				fields.FactoryField(s.cred.Factory.GetID()),
				fields.FactoryAccountField(s.cred.Account.GetID()),
			)

			if productID := attrs.GetInt(fields.ProductFieldKey); productID == nil {
				if productID = category.GetMatchedProductID(s.cred.Account.Channel); productID != nil {
					attrs.SetField(fields.ProductField(*productID))
				} else {
					attrs.SetField(fields.NilField(fields.ProductFieldKey))
				}
			}

			// update or add new
			cond := []fields.Field{
				fields.ExternalUUIDField(host.UHostId),
			}

			if _, err := restclient.PostOrPatch[models.Machine](cond, attrs); err != nil {
				logger.Error("sync uhost error",
					"host", attrs.GetString(fields.ExternalNameFieldKey),
					"uuid", attrs.GetString(fields.ExternalUUIDFieldKey),
					"error", err,
				)
			} else {
				color.Green("sync uhost success %s", attrs.GetString(fields.ExternalNameFieldKey))
			}
		}

		req.Offset = ucloud.Int(ucloud.IntValue(req.Offset) + len(hosts))
	}

	return nil
}

func Sync(account string) error {
	s, err := NewSyncer(account)
	if err != nil {
		return err
	}

	t := time.Now().Unix()
	if serr := s.Sync(); serr != nil {
		logger.Error("uhost sync error", "error", serr, "account", account)
		return serr
	}

	return s.Clean(t)
}
