package host

import (
	"strings"
	"time"

	"github.com/ucloud/ucloud-sdk-go/services/uhost"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/category"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
)

func Beauty(host uhost.UHostInstanceSet) fields.Fields {
	attrs := fields.NewFields(
		fields.ExternalNameField(host.Name),
		fields.ExternalStatusField(host.State),
		fields.ExternalUUIDField(host.UHostId),
		fields.OSNameField(host.OsName),
		fields.CPUField(host.CPU),
		fields.ExternalFlavorField(host.MachineType),
		fields.ChargeTypeField(host.ChargeType),
		fields.MemoryField(host.Memory/1024), // GiB
		fields.CreateTimeField(time.Unix(int64(host.CreateTime), 0).Format(time.RFC3339)),
	)

	if et := time.Unix(int64(host.ExpireTime), 0).Format(time.RFC3339); et != "" {
		attrs.Set("expired_time", et)
	}

	for _, ipset := range host.IPSet {
		if strings.ToLower(ipset.Type) == "private" {
			attrs.SetField(fields.PrivateIPField(ipset.IP))
		} else {
			attrs.SetField(fields.PublicIPField(ipset.IP))
		}
	}

	// override public ip if not exists
	if attrs.Get(fields.PublicIPFieldKey) == nil {
		attrs.SetField(fields.PublicIPField(""))
	}

	if productID := category.Category(host.Name, ""); productID != nil {
		attrs.SetField(fields.ProductField(*productID))
	}

	return attrs
}
