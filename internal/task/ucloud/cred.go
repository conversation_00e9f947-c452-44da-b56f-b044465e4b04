package ucloud

import (
	"fmt"
	"strings"

	"github.com/ucloud/ucloud-sdk-go/ucloud/auth"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/cache"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
)

type FactoryCrendential struct {
	Factory    *models.Factory        `json:"factory"`
	Account    *models.FactoryAccount `json:"account"`
	Credential *auth.Credential       `json:"credential"`
}

func CreateCredential(secretName string) (*auth.Credential, error) {
	var secrets restclient.UcloudCredential

	if err := restclient.UnmarshalSecret(secretName, &secrets); err != nil {
		return nil, err
	}

	cred := auth.NewCredential()
	cred.PrivateKey = secrets.PrivateKey
	cred.PublicKey = secrets.PublicKey

	return &cred, nil
}

func CreateCredentialWithAccount(accountKey string) (*FactoryCrendential, error) {
	var factoryKey string

	factory := strings.Split(accountKey, "-")
	if len(factory) > 1 {
		factoryKey = factory[0]
	} else {
		return nil, fmt.Errorf("invalid factory account key %s", accountKey)
	}

	if f := cache.GetFactory(factoryKey); f != nil {
		account, err := getAccount(f.GetID(), accountKey)
		if err != nil {
			return nil, err
		}

		cred, err := CreateCredential(account.KMSAccount)
		if err != nil {
			return nil, err
		}

		return &FactoryCrendential{
			Factory:    f,
			Account:    account,
			Credential: cred,
		}, nil
	}

	return nil, fmt.Errorf("unknown factory %s", factoryKey)
}

func getAccount(fid int, accountKey string) (*models.FactoryAccount, error) {
	var account models.FactoryAccount

	has, err := restclient.Find(&account,
		fields.NamedField("key_name", accountKey),
		fields.FactoryField(fid),
	)
	if err != nil {
		return nil, err
	}

	if !has {
		return nil, fmt.Errorf("account %s not found", accountKey)
	}

	fmt.Printf("account %+v\n", account)

	return &account, nil
}
