package k8s

import (
	"time"

	"github.com/ucloud/ucloud-sdk-go/services/uk8s"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/category"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
)

func Beauty(c uk8s.ClusterSet) fields.Fields {
	attrs := fields.NewFields(
		fields.StringField("cluster_id", c.ClusterId),
		fields.StringField("cluster_name", c.ClusterName),
		fields.StringField("cluster_version", c.K8sVersion),
		fields.CreateTimeField(time.Unix(int64(c.CreateTime), 0).Format(time.RFC3339)),
		// fields.ExternalStatusField(c.Status),
	)

	if productID := category.Category(c.ClusterName, ""); productID != nil {
		attrs.SetField(fields.ProductField(*productID))
	}

	return attrs
}
