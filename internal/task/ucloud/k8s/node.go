package k8s

import (
	"github.com/ucloud/ucloud-sdk-go/services/uk8s"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
)

func (s *Syncer) SyncNodes(cli *uk8s.UK8SClient, cluster string) error {
	resp, err := cli.ListUK8SClusterNodeV2(&uk8s.ListUK8SClusterNodeV2Request{
		ClusterId: fields.Pointer(cluster),
	})
	if err != nil {
		return err
	}

	for _, node := range resp.NodeSet {
		s.sync2CMDB(node, fields.StringField("cluster", cluster))
	}

	return nil
}

func (s *Syncer) sync2CMDB(node uk8s.NodeInfoV2, additions ...fields.Field) error {
	conds := fields.FieldList(
		fields.StringField("instance_id", node.InstanceId),
	)

	attrs := fields.NewFields(additions...)
	attrs.With(
		fields.StringField("instance_id", node.InstanceId),
		fields.StringField("instance_name", node.InstanceName),
	)

	_, err := restclient.PostOrPatch[models.K8SNode](conds, attrs)
	return err
}
