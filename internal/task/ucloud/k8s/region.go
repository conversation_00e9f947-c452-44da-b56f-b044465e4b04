package k8s

import (
	"fmt"

	"github.com/fatih/color"
	"github.com/ucloud/ucloud-sdk-go/services/uaccount"
	"github.com/ucloud/ucloud-sdk-go/ucloud"
	"github.com/ucloud/ucloud-sdk-go/ucloud/auth"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
)

func ListRegion(cred *auth.Credential) (map[string]string, error) {
	fmt.Printf("cred: %+v\n", cred)

	cfg := ucloud.NewConfig()
	cli := uaccount.NewClient(&cfg, cred)

	req := cli.NewGetRegionRequest()
	resp, err := cli.GetRegion(req)
	if err != nil {
		return nil, err
	}

	regions := make(map[string]string)
	for _, reg := range resp.Regions {
		regions[reg.Region] = RegionShortIDsMap[reg.Region]
	}

	return regions, nil
}

func LoadZones(conds ...fields.Field) map[string]models.Zone {
	zonePage, err := restclient.List[models.Zone](conds...)
	if err != nil {
		return nil
	}

	v := make(map[string]models.Zone)
	for _, zone := range zonePage.Results {
		v[zone.ZoneID] = zone
	}

	return v
}

func SyncRegion(factory int, name string, code string) error {
	conds := []fields.Field{
		fields.NamedField("factory__id", factory),
		fields.NamedField("region_id", code),
	}

	attr := fields.NewFields(
		fields.NamedField("region_id", code),
		fields.NamedField("name", name),
		fields.FactoryField(factory),
	)

	if _, err := restclient.PostOrPatch[models.Region](conds, attr); err != nil {
		return err
	}

	return nil
}

func SyncRegions(factory int, regions map[string]string) error {
	var err error
	for code, name := range regions {
		if serr := SyncRegion(factory, name, code); serr != nil {
			err = serr
		} else {
			color.Green("sync region %s %s success", name, code)
		}
	}
	return err
}
