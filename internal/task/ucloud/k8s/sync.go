package k8s

import (
	"fmt"
	"time"

	"github.com/fatih/color"
	"github.com/ucloud/ucloud-sdk-go/services/uk8s"
	"github.com/ucloud/ucloud-sdk-go/ucloud"
	"gitlab.lilithgame.com/yunwei/pkg/logger"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/category"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
	ucloudtask "gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/task/ucloud"
)

type Syncer struct {
	cred     *ucloudtask.FactoryCrendential
	regions  map[string]string
	projects []string
	zones    map[string]models.Zone
}

func NewSyncer(accountKey string) (*Syncer, error) {
	cred, err := ucloudtask.CreateCredentialWithAccount(accountKey)
	if err != nil {
		return nil, fmt.Errorf("create credential error: %w", err)
	}

	regions, err := ListRegion(cred.Credential)
	if err != nil {
		return nil, fmt.Errorf("list region error: %w", err)
	}

	projects, err := ListProject(cred.Credential)
	if err != nil {
		return nil, fmt.Errorf("list project error: %w", err)
	}

	zones := LoadZones(
		fields.NamedField("factory__id", cred.Factory.GetID()),
		fields.Unlimited,
	)

	// sync regions
	if serr := SyncRegions(cred.Factory.GetID(), regions); serr != nil {
		color.Red("sync regions error, %w", serr)
	} else {
		color.Green("sync regions success")
	}

	return &Syncer{
		cred:     cred,
		regions:  regions,
		projects: projects,
		zones:    zones,
	}, nil
}

func (s *Syncer) Sync() error {
	var err error
	for region := range s.regions {
		for _, project := range s.projects {
			if serr := s.SyncProject(region, project); serr != nil {
				logger.Error("sync uk8s with project failed",
					"project", project,
					"region", region,
					"error", serr,
				)
				err = serr
			}
		}
	}
	return err
}

func (s *Syncer) SyncProject(region string, projectID string) error {
	cfg := ucloud.NewConfig()
	client := uk8s.NewClient(&cfg, s.cred.Credential)

	req := client.NewListUK8SClusterV2Request()
	req.Region = fields.Pointer(region)
	req.ProjectId = fields.Pointer(projectID)

	limit := 20
	req.Limit = fields.Int(limit)

	for offset := 0; ; offset = limit * offset {
		req.Offset = fields.Int(offset)

		resp, err := client.ListUK8SClusterV2(req)
		if err != nil {
			return err
		}

		// 同步存在的 uhost
		clusters := resp.ClusterSet
		for _, cluster := range clusters {
			attrs := Beauty(cluster).
				With(fields.StringField("cloud_project", projectID)).
				With(
					fields.RegionIDField(region),
					fields.FactoryField(s.cred.Factory.GetID()),
					fields.FactoryAccountField(s.cred.Account.GetID()),
				)

			if productID := attrs.GetInt(fields.ProductFieldKey); productID == nil {
				if productID = category.GetMatchedProductID(s.cred.Account.Channel); productID != nil {
					attrs.SetField(fields.ProductField(*productID))
				}
			}

			// update or add new
			cond := []fields.Field{
				fields.ExternalUUIDField(cluster.ClusterId),
			}

			if k, err := restclient.PostOrPatch[models.K8SCluster](cond, attrs); err != nil {
				logger.Error("sync uhost cluster error",
					"cluster", cluster.ClusterName,
					"cluster_id", cluster.ClusterId,
					"error", err,
				)
			} else {
				logger.Infof("sync uhost cluster success %s", attrs.GetString(fields.ExternalNameFieldKey))
				startAt := time.Now().Unix()
				if err := s.SyncNodes(client, cluster.ClusterId); err == nil {
					restclient.DeleteSubResource[models.K8SCluster](
						"nodes",
						k.GetID(),
						fields.NumberField("updated_before", startAt),
					)
				}
			}
		}

		if len(clusters) < limit {
			break
		}
	}

	return nil
}

func Sync(account string) error {
	s, err := NewSyncer(account)
	if err != nil {
		return err
	}

	t := time.Now().Unix()
	if serr := s.Sync(); serr != nil {
		logger.Error("uhost sync error", "error", serr, "account", account)
		return serr
	}

	return s.Clean(t)
}
