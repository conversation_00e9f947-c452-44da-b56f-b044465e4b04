package sg

import (
	"github.com/ucloud/ucloud-sdk-go/services/uaccount"
	"github.com/ucloud/ucloud-sdk-go/ucloud"
	"github.com/ucloud/ucloud-sdk-go/ucloud/auth"
)

func ListProject(cred *auth.Credential) ([]string, error) {
	cfg := ucloud.NewConfig()

	cli := uaccount.NewClient(&cfg, cred)

	req := cli.NewGetProjectListRequest()
	// req.IsFinance = ucloud.String("Yes")
	resp, err := cli.GetProjectList(req)
	if err != nil {
		return nil, err
	}

	projects := make([]string, 0)
	for _, project := range resp.ProjectSet {
		projects = append(projects, project.ProjectId)
	}

	return projects, nil
}
