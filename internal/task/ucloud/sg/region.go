package sg

import (
	"github.com/ucloud/ucloud-sdk-go/services/uaccount"
	"github.com/ucloud/ucloud-sdk-go/ucloud"
	"github.com/ucloud/ucloud-sdk-go/ucloud/auth"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
)

func ListRegion(cred *auth.Credential) (map[string]string, error) {
	cfg := ucloud.NewConfig()
	cli := uaccount.NewClient(&cfg, cred)

	req := cli.NewGetRegionRequest()
	resp, err := cli.GetRegion(req)
	if err != nil {
		return nil, err
	}

	regions := make(map[string]string)
	for _, reg := range resp.Regions {
		regions[reg.Region] = RegionShortIDsMap[reg.Region]
	}

	return regions, nil
}

func LoadRegions(conds ...fields.Field) map[string]*models.Region {
	page, err := restclient.ListAll[models.Region](conds...)
	if err != nil {
		return nil
	}

	v := make(map[string]*models.Region)
	for _, r := range page.Results {
		v[r.RegionID] = &r
	}

	return v
}

func LoadZones(conds ...fields.Field) map[string]models.Zone {
	zonePage, err := restclient.List[models.Zone](conds...)
	if err != nil {
		return nil
	}

	v := make(map[string]models.Zone)
	for _, zone := range zonePage.Results {
		v[zone.ZoneID] = zone
	}

	return v
}
