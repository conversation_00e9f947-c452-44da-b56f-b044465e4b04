package sg

import (
	"fmt"
	"os"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/factory"
)

type BatchSyncer struct {
	syncers []*Syncer
}

func NewBatchSyncer(s ...*Syncer) *BatchSyncer {
	b := &BatchSyncer{
		syncers: s,
	}

	return b
}

func NewDefaultBatchSyncer() *BatchSyncer {
	batcher := NewBatchSyncer()

	for _, key := range []factory.FactoryKeyType{
		factory.Ucloud,
		factory.UcloudDGame,
	} {
		if s, err := NewSyncer(key.String()); err == nil {
			batcher.Append(s)
		} else {
			fmt.Fprintf(os.Stderr, "init ucloud syncer factory %s failed, %v", key, err)
		}
	}

	return batcher
}

func (bs *BatchSyncer) Len() int {
	return len(bs.syncers)
}

func (bs *BatchSyncer) Append(s ...*Syncer) {
	bs.syncers = append(bs.syncers, s...)
}

func (bs *BatchSyncer) Sync() error {
	var err error

	for _, s := range bs.syncers {
		if err := s.Sync(); err != nil {
			return err
		}
	}

	return err
}
