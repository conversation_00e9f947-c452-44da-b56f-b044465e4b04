package sg

import (
	"fmt"
	"time"

	"github.com/fatih/color"
	"github.com/ucloud/ucloud-sdk-go/services/unet"
	"github.com/ucloud/ucloud-sdk-go/ucloud"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/category"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
	utask "gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/task/ucloud"
)

type Syncer struct {
	cred     *utask.FactoryCrendential
	projects []string
	regions  map[string]*models.Region
}

func NewSyncer(accountKey string) (*Syncer, error) {
	fmt.Printf("new ucloud syncer with account %s\n", accountKey)
	cred, err := utask.CreateCredentialWithAccount(accountKey)
	if err != nil {
		return nil, err
	}

	projects, err := ListProject(cred.Credential)
	if err != nil {
		return nil, err
	}

	regionNames, err := ListRegion(cred.Credential)
	if err != nil {
		return nil, err
	}

	regions := LoadRegions(fields.NamedField("factory__id", cred.Factory.GetID()))

	availabeRegions := make(map[string]*models.Region)
	for _, name := range regionNames {
		if r, found := regions[name]; found {
			availabeRegions[name] = r
		}
	}

	return &Syncer{
		cred:     cred,
		projects: projects,
		regions:  availabeRegions,
	}, nil
}

func (s *Syncer) Sync() error {
	for _, project := range s.projects {
		for region := range s.regions {
			if serr := s.SyncProject(region, project); serr != nil {
				return serr
			}
		}
	}

	return nil
}

func (s *Syncer) SyncProject(region string, projectID string) error {
	cfg := ucloud.NewConfig()
	client := unet.NewClient(&cfg, s.cred.Credential)

	req := client.NewDescribeFirewallRequest()

	req.Limit = fields.Int(50)
	req.SetProjectId(projectID)
	req.SetRegion(region)

	for n := 0; ; n++ {
		req.Offset = fields.Int(n * 50)

		resp, err := client.DescribeFirewall(req)
		if err != nil {
			return err
		}

		for _, firewall := range resp.DataSet {
			attr := Beauty(firewall).With(
				fields.FactoryField(s.cred.Factory.GetID()),
				fields.FactoryAccountField(s.cred.Account.GetID()),
			)

			if r, found := s.regions[region]; found {
				attr.SetField(fields.RegionField(r.GetID()))
			}

			//  根据账号的渠道获取产品id
			if productId := attr.GetInt(fields.ProductFieldKey); productId == nil {
				if productId = category.GetMatchedProductID(s.cred.Account.Channel); productId != nil {
					attr.Set(fields.ProductFieldKey, fields.Value(productId))
				}
			}

			cond := []fields.Field{
				fields.StringField("security_id", firewall.FWId),
			}
			if sg, perr := restclient.PostOrPatch[models.Security](cond, attr); perr != nil {
				err = perr
				return err
			} else {
				color.Green("synced firewall %s %s", firewall.FWId, firewall.Name)

				// 同步安全组规则
				s.SyncRules(firewall.Rule, sg.SecurityID)
			}
		}

		if len(resp.DataSet) < *req.Limit {
			break
		}
	}

	return nil
}

func (s *Syncer) SyncRules(rules []unet.FirewallRuleSet, sg string) error {
	attrs := make([]fields.Fields, 0)
	for _, rule := range rules {
		attrs = append(attrs, BeautyRule(rule))
	}

	return restclient.PostSubResource[models.Security]("rules", sg, fields.Fields{
		"rules": attrs,
	})
}

func (s *Syncer) Clean(t int64, conds ...fields.Field) error {
	conds = append(conds,
		fields.FactoryAccountField(s.cred.Account.GetID()),
		fields.NamedField("updated_before", t),
	)
	if resp, err := restclient.ListAll[models.Security](conds...); err == nil {
		for _, m := range resp.Results {
			restclient.Delete(m)
		}
	}

	return nil
}

func Sync(account string) error {
	s, err := NewSyncer(account)
	if err != nil {
		return err
	}

	t := time.Now().Unix()
	if serr := s.Sync(); serr != nil {
		return serr
	}
	return s.Clean(t)
}
