package sg

import (
	"time"

	"github.com/ucloud/ucloud-sdk-go/services/unet"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/category"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
)

func Beauty(i unet.FirewallDataSet) fields.Fields {
	attrs := fields.NewFields(
		fields.StringField("security_id", i.FWId),
		fields.StringField("name", i.Name),
		fields.NumberField("ecs_count", i.ResourceCount),
		fields.DescField(i.Remark),

		fields.StringField("group_type", i.Type),
	)

	if createTime := time.Unix(int64(i.CreateTime), 0).Format("2006-01-02 15:04:05"); createTime != "" {
		attrs.SetField(fields.CreateTimeField(createTime))
	}

	if productId := category.Category(i.Name, ""); productId != nil {
		attrs.SetField(fields.ProductField(productId))
	}

	return attrs
}

func BeautyRule(i unet.FirewallRuleSet) fields.Fields {
	attrs := fields.NewFields(
		fields.StringField("direction", "ingress"),
		fields.UpperStringField("policy", i.RuleAction),
		fields.UpperStringField("protocol", i.ProtocolType),
		fields.StringField("priority", i.Priority),
		fields.StringField("ip", i.SrcIP),

		// fields.StringPField("dest_ip", i.d),
		fields.StringField("dest_port", i.DstPort),
	)

	return attrs
}
