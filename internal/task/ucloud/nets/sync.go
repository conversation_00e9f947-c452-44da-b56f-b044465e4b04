package nets

import (
	"fmt"
	"time"

	"github.com/fatih/color"
	"github.com/ucloud/ucloud-sdk-go/services/unet"
	"github.com/ucloud/ucloud-sdk-go/ucloud"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/category"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
	ucloudtask "gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/task/ucloud"
)

type Syncer struct {
	cred     *ucloudtask.FactoryCrendential
	projects []string
	regions  map[string]string
}

func NewSyncer(accountKey string) (*Syncer, error) {
	fmt.Printf("new ucloud syncer with account %s\n", accountKey)
	cred, err := ucloudtask.CreateCredentialWithAccount(accountKey)
	if err != nil {
		return nil, err
	}

	projects, err := ListProject(cred.Credential)
	if err != nil {
		return nil, err
	}

	regions, err := ListRegion(cred.Credential)
	if err != nil {
		return nil, err
	}

	return &Syncer{
		cred:     cred,
		projects: projects,
		regions:  regions,
	}, nil
}

func (s *Syncer) Sync() error {
	for _, project := range s.projects {
		for region := range s.regions {
			if serr := s.SyncProject(region, project); serr != nil {
				return serr
			}
		}
	}

	return nil
}

func (s *Syncer) SyncProject(region string, projectID string) error {
	cfg := ucloud.NewConfig()
	client := unet.NewClient(&cfg, s.cred.Credential)

	req := client.NewDescribeEIPRequest()
	req.Limit = fields.Int(50)
	req.SetProjectId(projectID)
	req.SetRegion(region)

	for n := 0; ; n++ {
		req.Offset = fields.Int(n * 50)

		resp, err := client.DescribeEIP(req)
		if err != nil {
			return err
		}

		for _, eip := range resp.EIPSet {
			attr := Beauty(eip)
			attr.With(
				fields.FactoryField(s.cred.Factory.GetID()),
				fields.FactoryAccountField(s.cred.Account.GetID()),
			)

			//  根据账号的渠道获取产品id
			if productId := attr.GetInt(fields.ProductFieldKey); productId == nil {
				if productId = category.GetMatchedProductID(s.cred.Account.Channel); productId != nil {
					attr.Set(fields.ProductFieldKey, fields.Value(productId))
				}
			}

			cond := []fields.Field{
				fields.StringField("external_uuid", eip.EIPId),
			}
			if _, perr := restclient.PostOrPatch[models.ElasticIP](cond, attr); perr != nil {
				err = perr
				return err
			} else {
				color.Green("synced eip %s %s", eip.EIPId, eip.Name)
			}
		}

		if len(resp.EIPSet) < *req.Limit {
			break
		}
	}

	return nil
}

func (s *Syncer) Clean(t int64, conds ...fields.Field) error {
	conds = append(conds,
		fields.FactoryAccountField(s.cred.Account.GetID()),
		fields.NamedField("updated_before", t),
	)
	if resp, err := restclient.ListAll[models.ElasticIP](conds...); err == nil {
		for _, m := range resp.Results {
			restclient.Delete(m)
		}
	}

	return nil
}

func Sync(account string) error {
	s, err := NewSyncer(account)
	if err != nil {
		return err
	}

	t := time.Now().Unix()
	if serr := s.Sync(); serr != nil {
		return serr
	}
	return s.Clean(t)
}
