package nets

import (
	"time"

	"github.com/ucloud/ucloud-sdk-go/services/unet"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/category"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
)

func Beauty(i unet.UnetEIPSet) fields.Fields {
	attrs := fields.NewFields(
		fields.NamedField(fields.ExternalUUIDFieldKey, i.EIPId),
		fields.NamedField("name", i.Name),
		fields.NamedField("version", "eip"),
		fields.NamedField("public_ip", i.EIPAddr[0]),
		fields.ExternalStatusField(i.Status),
		fields.NamedField("state", i.Status),
	)

	if createTime := time.Unix(int64(i.CreateTime), 0).Format("2006-01-02 15:04:05"); createTime != "" {
		attrs.SetField(fields.CreateTimeField(createTime))
	}

	//  公网 IP(第一个)
	for _, eip := range i.EIPAddr {
		attrs.With(
			fields.PublicIPField(eip.IP),
			fields.NamedField("isp", eip.OperatorName),
		)
		break
	}

	attrs.With(
		fields.NamedField("bind_id", i.Resource.ResourceID),
		fields.NamedField("bind_type", i.Resource.ResourceType),
	)

	if bindType := i.Resource.ResourceType; bindType != "" {
		attrs.With(
			fields.NamedField("last_bind_id", i.Resource.ResourceID),
			fields.NamedField("last_bind_type", bindType),
		)
	}

	if productId := category.Category(i.Name, ""); productId != nil {
		attrs.SetField(fields.ProductField(productId))
	}

	return attrs
}
