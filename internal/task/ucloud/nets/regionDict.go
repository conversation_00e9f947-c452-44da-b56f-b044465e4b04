package nets

var RegionShortIDsMap = map[string]string{
	"cn-bj2":       "华北一",
	"cn-gd":        "广州",
	"cn-qz":        "福建",
	"cn-sh2":       "上海二",
	"cn-wlcb":      "华北二",
	"afr-nigeria":  "拉各斯",
	"bra-saopaulo": "圣保罗",
	"ge-fra":       "法兰克福",
	"hk":           "香港",
	"idn-jakarta":  "雅加达",
	"ind-mumbai":   "孟买",
	"jpn-tky":      "东京",
	"kr-seoul":     "首尔",
	"ph-mnl":       "马尼拉",
	"sg":           "新加坡",
	"th-bkk":       "曼谷",
	"tw-tp":        "台北",
	"uae-dubai":    "迪拜",
	"uk-london":    "伦敦",
	"us-ca":        "洛杉矶",
	"us-ws":        "华盛顿",
	"vn-sng":       "胡志明市",
}

var ZoneShortIDsMap = map[string]string{
	"cn-bj2-02":       "华北一可用区B",
	"cn-bj2-03":       "华北一可用区C",
	"cn-bj2-04":       "华北一可用区D",
	"cn-bj2-05":       "华北一可用区E",
	"cn-gd-02":        "广州可用区B",
	"cn-qz-01":        "福建GPU可用区A",
	"cn-sh2-01":       "上海二可用区A",
	"cn-sh2-02":       "上海二可用区B",
	"cn-sh2-03":       "上海二可用区C",
	"cn-wlcb-01":      "华北二可用区A",
	"afr-nigeria-01":  "拉各斯可用区A",
	"bra-saopaulo-01": "圣保罗可用区A",
	"ge-fra-01":       "法兰克福可用区A",
	"hk-01":           "香港可用区A",
	"hk-02":           "香港可用区B",
	"idn-jakarta-01":  "雅加达可用区A",
	"ind-mumbai-01":   "孟买可用区A",
	"jpn-tky-01":      "东京可用区A",
	"kr-seoul-01":     "首尔可用区A",
	"ph-mnl-01":       "马尼拉可用区A",
	"sg-01":           "新加坡可用区A",
	"sg-02":           "新加坡可用区B",
	"th-bkk-01":       "曼谷可用区A",
	"th-bkk-02":       "曼谷可用区B",
	"tw-tp-01":        "台北可用区A",
	"uae-dubai-01":    "迪拜可用区A",
	"uk-london-01":    "伦敦可用区A",
	"us-ca-01":        "洛杉矶可用区A",
	"us-ws-01":        "华盛顿可用区A",
	"vn-sng-01":       "胡志明市可用区A",
}
