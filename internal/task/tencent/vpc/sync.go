package vpc

import (
	"log/slog"

	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/profile"
	vpc "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/vpc/v20170312"
	"gitlab.lilithgame.com/yunwei/pkg/logger"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/task/tencent"
)

type Syncer struct {
	cred *tencent.FactoryCredential
	log  *slog.Logger
}

func NewSyncer(accountKey string) (*Syncer, error) {
	cred, err := tencent.CreateCredentialWithAccount(accountKey)
	if err != nil {
		return nil, err
	}

	log := logger.Slog().With("factory", cred.Factory.Name, "account", cred.Account.Name)

	s := &Syncer{cred: cred, log: log}
	return s, nil
}

func (s *Syncer) Sync() error {
	regions, err := restclient.ListAll[models.Region](fields.NamedField("factory__id", s.cred.Factory.GetID()))
	if err != nil {
		return err
	}

	var lastError error
	for _, region := range regions.Results {
		if err := s.SyncInRegion(&region); err != nil {
			lastError = err
		}
	}

	return lastError
}

const pageSize int64 = 100

func (s *Syncer) SyncInRegion(region *models.Region) error {
	client, err := vpc.NewClient(s.cred.Credential, region.RegionID, profile.NewClientProfile())
	if err != nil {
		return err
	}

	var lastError error
	var failedResources []string

	// 同步 VPC，即使失败也继续同步其他资源
	if err := s.SyncVPC(client, region); err != nil {
		lastError = err
		failedResources = append(failedResources, "VPC")
		s.log.Error("VPC sync failed, continuing with other resources", "region", region.RegionID, "error", err)
	} else {
		s.log.Debug("VPC sync completed successfully", "region", region.RegionID)
	}

	// 同步 EIP，即使失败也继续同步其他资源
	if err := s.SyncEIP(client, region); err != nil {
		lastError = err
		failedResources = append(failedResources, "EIP")
		s.log.Error("EIP sync failed, continuing with other resources", "region", region.RegionID, "error", err)
	} else {
		s.log.Debug("EIP sync completed successfully", "region", region.RegionID)
	}

	// 同步安全组
	if err := s.SyncSG(client, region); err != nil {
		lastError = err
		failedResources = append(failedResources, "SecurityGroup")
		s.log.Error("Security Group sync failed", "region", region.RegionID, "error", err)
	} else {
		s.log.Debug("Security Group sync completed successfully", "region", region.RegionID)
	}

	// 记录同步总结
	if len(failedResources) > 0 {
		s.log.Warn("Region sync completed with failures", "region", region.RegionID, "failed_resources", failedResources)
	} else {
		s.log.Info("Region sync completed successfully", "region", region.RegionID)
	}

	return lastError
}

func Sync(account string) error {
	s, err := NewSyncer(account)
	if err != nil {
		return err
	}

	if err := s.Sync(); err != nil {
		return err
	}

	return nil
}
