package vpc

import (
	"fmt"

	vpc "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/vpc/v20170312"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
)

// SyncSG syncs security groups in the specified region
func (s *Syncer) SyncSG(client *vpc.Client, region *models.Region) error {
	request := vpc.NewDescribeSecurityGroupsRequest()
	request.Limit = fields.Pointer("100")

	var lastError error

	for page := int64(0); ; page++ {
		request.Offset = fields.Pointer(fmt.Sprintf("%d", page*pageSize))

		response, err := client.DescribeSecurityGroups(request)
		if err != nil {
			s.log.Error("Failed to describe security groups", "region", region.RegionID, "error", err)
			return err
		}

		for _, sg := range response.Response.SecurityGroupSet {
			conds := []fields.Field{
				fields.FactoryField(s.cred.Factory.GetID()),
				fields.ExternalUUIDField(*sg.SecurityGroupId),
			}

			// TODO: Implement BeautySecurityGroup function in beauty.go
			attrs := fields.NewFields().
				With(fields.FactoryField(s.cred.Factory.GetID())).
				With(fields.FactoryAccountField(s.cred.Account.GetID())).
				With(fields.RegionField(region.GetID())).
				With(fields.ExternalUUIDField(*sg.SecurityGroupId)).
				With(fields.ExternalNameField(*sg.SecurityGroupName))

			if _, perr := restclient.PostOrPatch[models.Security](conds, attrs); perr != nil {
				lastError = perr
				s.log.Error("Failed to sync security group", "id", *sg.SecurityGroupId, "region", region.RegionID, "error", perr)
			} else {
				s.log.Debug("Synced security group", "id", *sg.SecurityGroupId, "region", region.RegionID, "attr", attrs)
			}
		}

		if len(response.Response.SecurityGroupSet) < int(pageSize) {
			break
		}
	}

	return lastError
}
