package vpc

import (
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
)

// Clean 清理指定时间戳之前的过期数据
func (s *Syncer) Clean(timestamp int64) error {
	var lastError error

	// 清理过期的 VPC
	if err := s.cleanVPCs(timestamp); err != nil {
		lastError = err
		s.log.Error("Failed to clean VPCs", "error", err)
	} else {
		s.log.Info("VPC cleanup completed successfully")
	}

	// 清理过期的 EIP
	if err := s.cleanEIPs(timestamp); err != nil {
		lastError = err
		s.log.Error("Failed to clean EIPs", "error", err)
	} else {
		s.log.Info("EIP cleanup completed successfully")
	}

	// 清理过期的安全组
	if err := s.cleanSecurityGroups(timestamp); err != nil {
		lastError = err
		s.log.Error("Failed to clean security groups", "error", err)
	} else {
		s.log.Info("Security group cleanup completed successfully")
	}

	if lastError == nil {
		s.log.Info("All VPC resources cleanup completed successfully", "timestamp", timestamp)
	} else {
		s.log.Warn("VPC resources cleanup completed with some failures", "timestamp", timestamp)
	}

	return lastError
}

// cleanVPCs 清理过期的 VPC 资源
func (s *Syncer) cleanVPCs(timestamp int64) error {
	conds := []fields.Field{
		fields.FactoryField(s.cred.Factory.GetID()),
		fields.FactoryAccountField(s.cred.Account.GetID()),
		fields.NumberField("updated_before", timestamp),
	}

	resp, err := restclient.ListAll[models.VPC](conds...)
	if err != nil {
		s.log.Error("Failed to list VPCs for cleanup", "error", err)
		return err
	}

	var deletedCount int
	var lastError error

	for _, vpc := range resp.Results {
		if err := restclient.Delete(vpc); err != nil {
			lastError = err
			s.log.Error("Failed to delete VPC", "id", vpc.GetID(), "error", err)
		} else {
			deletedCount++
			s.log.Debug("Deleted VPC", "id", vpc.GetID())
		}
	}

	s.log.Info("Deleted expired VPCs", "count", deletedCount, "total", resp.Count, "timestamp", timestamp)
	return lastError
}

// cleanEIPs 清理过期的 EIP 资源
func (s *Syncer) cleanEIPs(timestamp int64) error {
	conds := []fields.Field{
		fields.FactoryField(s.cred.Factory.GetID()),
		fields.FactoryAccountField(s.cred.Account.GetID()),
		fields.NumberField("updated_before", timestamp),
	}

	resp, err := restclient.ListAll[models.ElasticIP](conds...)
	if err != nil {
		s.log.Error("Failed to list EIPs for cleanup", "error", err)
		return err
	}

	var deletedCount int
	var lastError error

	for _, eip := range resp.Results {
		if err := restclient.Delete(eip); err != nil {
			lastError = err
			s.log.Error("Failed to delete EIP", "id", eip.GetID(), "error", err)
		} else {
			deletedCount++
			s.log.Debug("Deleted EIP", "id", eip.GetID())
		}
	}

	s.log.Info("Deleted expired EIPs", "count", deletedCount, "total", resp.Count, "timestamp", timestamp)
	return lastError
}

// cleanSecurityGroups 清理过期的安全组资源
func (s *Syncer) cleanSecurityGroups(timestamp int64) error {
	conds := []fields.Field{
		fields.FactoryField(s.cred.Factory.GetID()),
		fields.FactoryAccountField(s.cred.Account.GetID()),
		fields.NumberField("updated_before", timestamp),
	}

	resp, err := restclient.ListAll[models.Security](conds...)
	if err != nil {
		s.log.Error("Failed to list security groups for cleanup", "error", err)
		return err
	}

	var deletedCount int
	var lastError error

	for _, sg := range resp.Results {
		if err := restclient.Delete(sg); err != nil {
			lastError = err
			s.log.Error("Failed to delete security group", "id", sg.GetID(), "error", err)
		} else {
			deletedCount++
			s.log.Debug("Deleted security group", "id", sg.GetID())
		}
	}

	s.log.Info("Deleted expired security groups", "count", deletedCount, "total", resp.Count, "timestamp", timestamp)
	return lastError
}

// CleanByRegion 按区域清理过期数据
func (s *Syncer) CleanByRegion(region *models.Region, timestamp int64) error {
	var lastError error

	// 清理指定区域的 VPC
	if err := s.cleanVPCsByRegion(region, timestamp); err != nil {
		lastError = err
		s.log.Error("Failed to clean VPCs in region", "region", region.RegionID, "error", err)
	}

	// 清理指定区域的 EIP
	if err := s.cleanEIPsByRegion(region, timestamp); err != nil {
		lastError = err
		s.log.Error("Failed to clean EIPs in region", "region", region.RegionID, "error", err)
	}

	// 清理指定区域的安全组
	if err := s.cleanSecurityGroupsByRegion(region, timestamp); err != nil {
		lastError = err
		s.log.Error("Failed to clean security groups in region", "region", region.RegionID, "error", err)
	}

	if lastError == nil {
		s.log.Info("Region cleanup completed successfully", "region", region.RegionID, "timestamp", timestamp)
	}

	return lastError
}

// cleanVPCsByRegion 清理指定区域的过期 VPC
func (s *Syncer) cleanVPCsByRegion(region *models.Region, timestamp int64) error {
	conds := []fields.Field{
		fields.FactoryField(s.cred.Factory.GetID()),
		fields.FactoryAccountField(s.cred.Account.GetID()),
		fields.RegionField(region.GetID()),
		fields.NumberField("updated_before", timestamp),
	}

	resp, err := restclient.ListAll[models.VPC](conds...)
	if err != nil {
		return err
	}

	var deletedCount int
	for _, vpc := range resp.Results {
		if err := restclient.Delete(vpc); err != nil {
			s.log.Error("Failed to delete VPC in region", "region", region.RegionID, "id", vpc.GetID(), "error", err)
		} else {
			deletedCount++
		}
	}

	s.log.Debug("Deleted expired VPCs in region", "region", region.RegionID, "count", deletedCount)
	return nil
}

// cleanEIPsByRegion 清理指定区域的过期 EIP
func (s *Syncer) cleanEIPsByRegion(region *models.Region, timestamp int64) error {
	conds := []fields.Field{
		fields.FactoryField(s.cred.Factory.GetID()),
		fields.FactoryAccountField(s.cred.Account.GetID()),
		fields.RegionField(region.GetID()),
		fields.NumberField("updated_before", timestamp),
	}

	resp, err := restclient.ListAll[models.ElasticIP](conds...)
	if err != nil {
		return err
	}

	var deletedCount int
	for _, eip := range resp.Results {
		if err := restclient.Delete(eip); err != nil {
			s.log.Error("Failed to delete EIP in region", "region", region.RegionID, "id", eip.GetID(), "error", err)
		} else {
			deletedCount++
		}
	}

	s.log.Debug("Deleted expired EIPs in region", "region", region.RegionID, "count", deletedCount)
	return nil
}

// cleanSecurityGroupsByRegion 清理指定区域的过期安全组
func (s *Syncer) cleanSecurityGroupsByRegion(region *models.Region, timestamp int64) error {
	conds := []fields.Field{
		fields.FactoryField(s.cred.Factory.GetID()),
		fields.FactoryAccountField(s.cred.Account.GetID()),
		fields.RegionField(region.GetID()),
		fields.NumberField("updated_before", timestamp),
	}

	resp, err := restclient.ListAll[models.Security](conds...)
	if err != nil {
		return err
	}

	var deletedCount int
	for _, sg := range resp.Results {
		if err := restclient.Delete(sg); err != nil {
			s.log.Error("Failed to delete security group in region", "region", region.RegionID, "id", sg.GetID(), "error", err)
		} else {
			deletedCount++
		}
	}

	s.log.Debug("Deleted expired security groups in region", "region", region.RegionID, "count", deletedCount)
	return nil
}
