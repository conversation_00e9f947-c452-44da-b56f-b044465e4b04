package vpc

import (
	"fmt"

	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/profile"
	vpc "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/vpc/v20170312"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
)

const pageSize int64 = 100

func (s *Syncer) SyncInRegion(region *models.Region) error {
	client, err := vpc.NewClient(s.cred.Credential, region.RegionID, profile.NewClientProfile())
	if err != nil {
		return err
	}

	if err := s.SyncVPC(client, region); err != nil {
		return err
	}

	return s.SyncEIP(client, region)
}

func (s *Syncer) SyncVPC(client *vpc.Client, region *models.Region) error {
	request := vpc.NewDescribeVpcsRequest()
	request.Limit = fields.Pointer(fmt.Sprintf("%d", pageSize))

	var lastError error

	for page := int64(0); ; page++ {
		request.Offset = fields.Pointer(fmt.Sprintf("%d", page*pageSize))

		response, err := client.DescribeVpcs(request)
		if err != nil {
			s.log.Error("Failed to describe vpcs", "region", region.RegionID, "error", err)
			return err
		}

		for _, vpc := range response.Response.VpcSet {
			conds := []fields.Field{
				fields.FactoryField(s.cred.Factory.GetID()),
				fields.ExternalUUIDField(*vpc.VpcId),
			}

			attrs := BeautyWith(vpc,
				BeautyVPC,
				fields.FactoryField(s.cred.Factory.GetID()),
				fields.FactoryAccountField(s.cred.Account.GetID()),
				fields.RegionField(region.GetID()),
			)

			if _, perr := restclient.PostOrPatch[models.VPC](conds, attrs); perr != nil {
				lastError = perr
				s.log.Error("Failed to sync vpc", "id", *vpc.VpcId, "region", region.RegionID, "error", perr)
			} else {
				s.log.Debug("Synced vpc", "id", *vpc.VpcId, "region", region.RegionID, "attr", attrs)
			}
		}

		// 不足一页，说明已经全部同步完毕
		if len(response.Response.VpcSet) < int(pageSize) {
			break
		}
	}

	return lastError
}

func (s *Syncer) SyncEIP(client *vpc.Client, region *models.Region) error {
	request := vpc.NewDescribeAddressesRequest()
	request.Limit = fields.Int64(pageSize)

	var lastError error

	for page := int64(0); ; page++ {
		request.Offset = fields.Int64(page * pageSize)

		response, err := client.DescribeAddresses(request)
		if err != nil {
			s.log.Error("Failed to describe eips", "region", region.RegionID, "error", err)
			return err
		}

		for _, addr := range response.Response.AddressSet {
			conds := []fields.Field{
				fields.FactoryField(s.cred.Factory.GetID()),
				fields.ExternalUUIDField(*addr.AddressId),
			}

			attrs := BeautyWith(addr,
				BeautyAddress,
				fields.FactoryField(s.cred.Factory.GetID()),
				fields.FactoryAccountField(s.cred.Account.GetID()),
				fields.RegionField(region.GetID()),
			)

			if _, perr := restclient.PostOrPatch[models.ElasticIP](conds, attrs); perr != nil {
				lastError = perr
				s.log.Error("Failed to sync eip", "id", *addr.AddressId, "region", region.RegionID, "error", perr)
			} else {
				s.log.Debug("Synced eip", "id", *addr.AddressId, "region", region.RegionID, "attr", attrs)
			}
		}

		if len(response.Response.AddressSet) < int(pageSize) {
			break
		}
	}

	return lastError
}
