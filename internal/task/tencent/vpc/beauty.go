package vpc

import (
	vpc "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/vpc/v20170312"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/category"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
)

func BeautyAddress(l *vpc.Address) fields.Fields {
	attrs := fields.NewFields(
		fields.StringPField("version", l.AddressType),

		fields.StringPField("name", l.AddressName),
		fields.StringPField("status", l.AddressStatus),

		fields.ExternalUUIDField(*l.AddressId),
		fields.ExternalStatusField(fields.Value(l.AddressStatus)),

		fields.StringPField("isp", l.InternetServiceProvider),

		fields.StringPField(fields.PublicIPFieldKey, l.AddressIp),
		fields.CreateTimeField(*l.CreatedTime),
	)

	if bindInstanceId := fields.Value(l.InstanceId); bindInstanceId != "" {
		attrs.With(
			fields.StringField("bind_id", bindInstanceId),
			// fields.StringField("bind_type", l.InstanceType),
		)
	}

	if name := fields.Value(l.AddressName); name != "" {
		if productId := category.Category(name, ""); productId != nil {
			attrs.Set(fields.ProductFieldKey, *productId)
		}
	}

	return attrs
}

func BeautyVPC(v *vpc.Vpc) fields.Fields {
	attrs := fields.NewFields(
		fields.StringPField("vpc_id", v.VpcId),
		fields.StringPField("name", v.VpcName),
		fields.StringPField("cidr_block", v.CidrBlock),
	)

	return attrs
}

func BeautyWith[T any](obj T, beauty func(T) fields.Fields, additions ...fields.Field) fields.Fields {
	return beauty(obj).With(additions...)
}
