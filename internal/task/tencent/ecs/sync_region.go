package ecs

import (
	"strings"

	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/profile"
	cvm "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/cvm/v20170312"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
)

// SyncRegions synchronizes all regions and zones.
func (s *Syncer) SyncRegions() error {
	client, err := cvm.NewClient(s.cred.Credential, "", profile.NewClientProfile())
	if err != nil {
		return nil
	}

	resp, err := client.DescribeRegions(cvm.NewDescribeRegionsRequest())
	if err != nil {
		return err
	}

	for _, region := range resp.Response.RegionSet {
		if strings.ToLower(*region.RegionState) != "available" {
			continue
		}

		reg, err := s.writeRegionToDB(region)
		if err != nil {
			continue
		}

		// sync all zones in this region
		regionClient, _ := cvm.NewClient(s.cred.Credential, reg.RegionID, profile.NewClientProfile())
		s.syncZone(regionClient, reg)
	}

	return nil
}

// writeRegionToDB writes region data to the database using POST or PATCH.
func (s *Syncer) writeRegionToDB(region *cvm.RegionInfo) (*models.Region, error) {
	conds := []fields.Field{
		fields.FactoryField(s.cred.Factory.GetID()),
		fields.NamedField("region_id", *region.Region),
	}
	attrs := fields.NewFields(
		fields.FactoryField(s.cred.Factory.GetID()),
		fields.NamedField("region_id", *region.Region),
		fields.NamedField("name", *region.RegionName),
	)

	reg, err := restclient.PostOrPatch[models.Region](conds, attrs)
	if err != nil {
		return nil, err
	}

	return reg, nil
}

func (s *Syncer) syncZone(cli *cvm.Client, reg *models.Region) error {
	req := cvm.NewDescribeZonesRequest()

	resp, err := cli.DescribeZones(req)
	if err != nil {
		return err
	}

	for _, zone := range resp.Response.ZoneSet {
		if strings.ToLower(*zone.ZoneState) != "available" {
			// skip unavailable zones
			continue
		}

		if _, err := s.writeZoneToDB(zone, reg.GetID()); err != nil {
			continue
		}
	}

	return nil
}

func (s *Syncer) writeZoneToDB(zone *cvm.ZoneInfo, regID int) (*models.Zone, error) {
	conds := []fields.Field{
		fields.FactoryField(s.cred.Factory.GetID()),
		fields.NamedField("zone_id", *zone.Zone),
	}
	attrs := fields.NewFields(
		fields.RegionField(regID),
		fields.NamedPField("zone_id", zone.Zone),
		fields.NamedPField("name", zone.ZoneName),
		fields.FactoryField(s.cred.Factory.GetID()),
	)

	z, err := restclient.PostOrPatch[models.Zone](conds, attrs)
	if err != nil {
		return nil, err
	}

	return z, nil
}
