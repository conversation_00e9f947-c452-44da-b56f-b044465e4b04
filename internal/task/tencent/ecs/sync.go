package ecs

import (
	"log/slog"

	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/profile"
	cvm "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/cvm/v20170312"
	"gitlab.lilithgame.com/yunwei/pkg/logger"

	beauty "gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/beauty/tencent"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/category"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/cache"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/task/tencent"
)

type Syncer struct {
	cred *tencent.FactoryCredential
	log  *slog.Logger
}

func NewSyncer(accountKey string) (*Syncer, error) {
	cred, err := tencent.CreateCredentialWithAccount(accountKey)
	if err != nil {
		return nil, err
	}

	log := logger.Slog().With("factory", cred.Factory.Name, "account", cred.Account.Name)

	s := &Syncer{cred: cred, log: log}
	return s, nil
}

func (s *Syncer) Sync() error {
	regions, err := restclient.ListAll[models.Region](fields.NamedField("factory__id", s.cred.Factory.GetID()))
	if err != nil {
		return err
	}

	var lastError error
	for _, region := range regions.Results {
		// fmt.Printf("sync region %s, %s\n", region.RegionID, region.FactoryName)
		if err := s.SyncInRegion(region.RegionID); err != nil {
			lastError = err
		}
	}

	return lastError
}

func (s *Syncer) SyncInRegion(region string) error {
	client, err := cvm.NewClient(s.cred.Credential, region, profile.NewClientProfile())
	if err != nil {
		return err
	}

	var lastError error

	// create a request
	const pageSize int64 = 100

	regionalLog := s.log.With("region", region)

	request := cvm.NewDescribeInstancesRequest()
	request.Limit = fields.Pointer(pageSize)

	for page := int64(0); ; page++ {
		request.Offset = fields.Pointer(page * pageSize)

		resp, err := client.DescribeInstances(request)
		if err != nil {
			regionalLog.Error("Failed to describe instances", "error", err)
			return err
		}

		// regionalLog.Debug("list instances", "page", page, "total", *resp.Response.TotalCount)

		for _, instance := range resp.Response.InstanceSet {
			attrs := beauty.BeautyHost(instance).With(
				fields.FactoryField(s.cred.Factory.GetID()),
				fields.FactoryAccountField(s.cred.Account.GetID()),
			)

			// set zone
			if place := instance.Placement; place != nil {
				zid := fields.Value(place.Zone)
				if zone := cache.GetFactoryZone(s.cred.Factory.GetID(), zid); zone != nil {
					attrs.With(fields.ZoneField(zone.GetID()))
				}
			}

			// product
			if product := category.Category(*instance.InstanceName, ""); product != nil {
				attrs.SetField(fields.ProductField(*product))
			}

			if product := attrs.GetInt(fields.ProductFieldKey); product == nil {
				if product = category.GetMatchedProductID(s.cred.Account.Channel); product != nil {
					attrs.With(fields.ProductField(product))
				}
			}

			conds := []fields.Field{
				fields.FactoryField(s.cred.Factory.GetID()),
				*attrs.GetField(fields.ExternalUUIDFieldKey),
			}

			if _, err := restclient.PostOrPatch[models.Machine](conds, attrs); err != nil {
				lastError = err
				regionalLog.Error("Failed to sync machine", "error", err)
			}
		}

		if len(resp.Response.InstanceSet) < int(pageSize) {
			break
		}
	}

	return lastError
}

func Sync(account string) error {
	s, err := NewSyncer(account)
	if err != nil {
		return err
	}

	if err := s.Sync(); err != nil {
		return err
	}

	return nil
}
