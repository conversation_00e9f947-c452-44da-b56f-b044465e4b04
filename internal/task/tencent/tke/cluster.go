package tke

import (
	"cmp"
	"time"

	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/profile"
	tke "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/tke/v20180525"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
)

func (s *Syncer) SyncInRegion(region string) error {
	client, err := tke.NewClient(s.cred.Credential, region, profile.NewClientProfile())
	if err != nil {
		return err
	}

	request := tke.NewDescribeClustersRequest()
	response, err := client.DescribeClusters(request)
	if err != nil {
		s.log.Error("Failed to describe clusters", "error", err)
		return err
	}

	var lastError error

	for _, cluster := range response.Response.Clusters {
		s.log.Info("Found cluster", "id", *cluster.ClusterId, "name", *cluster.ClusterName, "region", region)

		attrs := Beauty(cluster).With(
			fields.FactoryField(s.cred.Factory.GetID()),
			fields.FactoryAccountField(s.cred.Account.GetID()),
			fields.RegionIDField(region),
		)

		conds := []fields.Field{
			fields.FactoryField(s.cred.Factory.GetID()),
			*attrs.GetField("cluster_id"),
		}

		startAt := time.Now().Unix()
		if k, err := restclient.PostOrPatch[models.K8SCluster](conds, attrs); err != nil {
			s.log.Error("Failed to sync cluster", "id", *cluster.ClusterId, "error", err)
			lastError = err
		} else {
			lastError = cmp.Or(lastError, s.SyncNode(client, *cluster.ClusterId))

			restclient.DeleteSubResource[models.K8SCluster](
				"nodes",
				k.GetID(),
				fields.NumberField("updated_before", startAt),
			)
		}
	}

	return lastError
}

func (s *Syncer) SyncNode(cli *tke.Client, cluster string) error {
	const pageSize int64 = 100

	request := tke.NewDescribeClusterInstancesRequest()
	request.ClusterId = &cluster
	request.Limit = fields.Pointer(pageSize)

	var lastErr error

	for page := int64(0); ; page++ {
		request.Offset = fields.Pointer(page * pageSize)

		response, err := cli.DescribeClusterInstances(request)
		if err != nil {
			return err
		}

		for _, node := range response.Response.InstanceSet {
			conds := []fields.Field{
				fields.NamedPField("instance_id", node.InstanceId),
			}

			attrs := fields.NewFields(
				fields.NamedPField("instance_id", node.InstanceId),
				fields.NamedField("cluster", cluster),
				// fields.NamedPField("pool", node.NodePoolId),
				fields.NamedPField("created_at", node.CreatedTime),
			)

			if _, perr := restclient.PostOrPatch[models.K8SNode](conds, attrs); perr != nil {
				lastErr = perr
			}
		}

		if len(response.Response.InstanceSet) < int(pageSize) {
			break
		}
	}

	return lastErr
}
