package tke

import (
	tke "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/tke/v20180525"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/category"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
)

func Beauty(c *tke.Cluster) fields.Fields {
	attrs := fields.NewFields(
		fields.StringPField("cluster_id", c.ClusterId),
		fields.StringPField("cluster_name", c.ClusterName),
		fields.StringPField("cluster_version", c.ClusterVersion),
		fields.CreateTimeField(*c.CreatedTime),
	)

	if product := category.Category(*c.ClusterName, ""); product != nil {
		attrs.SetField(fields.ProductField(*product))
	}

	return attrs
}
