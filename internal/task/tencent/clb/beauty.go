package clb

import (
	"time"

	clb "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/clb/v20180317"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/category"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
)

const LBType = "CLB"

func Beauty(l *clb.LoadBalancer) fields.Fields {
	fz := fields.NewFields(
		fields.NamedField("lb_type", LBType),
		fields.NamedPField(fields.ExternalUUIDFieldKey, l.LoadBalancerId),
		fields.NamedPField(fields.ExternalNameFieldKey, l.<PERSON>ame),
		fields.NamedPField("ip_version", l.AddressIPVersion),
		fields.NamedPField("network_type", l.LoadBalancerType),
		// fields.NamedPField("address_type", l.AddressType),
		// fields.NamedPField("vswitch_id", l.VSwitchId),
	)

	// FIXME: only the first one is valid
	if len(l.LoadBalancerVips) > 0 {
		fz.Set("address", l.<PERSON>ad<PERSON>alancerVips[0])
	}

	// status renaming
	status := fields.Value(l.Status)
	if status == 0 {
		fz.SetField(fields.ExternalStatusField("creating"))
	} else {
		fz.SetField(fields.ExternalStatusField("running"))
	}

	if createTime := fields.PString(l.CreateTime); createTime != "" {
		if t, terr := time.ParseInLocation(time.DateTime, createTime, time.Local); terr == nil {
			fz.Set(fields.CreateTimeFieldKey, t.Local().Format(time.RFC3339))
		}
	}

	if productID := resolveProductId(l); productID != nil {
		fields.SetValue(fz, fields.ProductFieldKey, productID)
	}

	return fz
}

func BeautyWith(l *clb.LoadBalancer, additions ...fields.Field) fields.Fields {
	return Beauty(l).With(additions...)
}

func resolveProductId(l *clb.LoadBalancer) *int {
	if productID := category.Category(*l.LoadBalancerName, ""); productID != nil {
		return productID
	}

	return nil
}
