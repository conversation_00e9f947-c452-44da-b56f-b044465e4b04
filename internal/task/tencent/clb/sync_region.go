package clb

import (
	clb "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/clb/v20180317"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/profile"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/cache"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
)

func (s *Syncer) SyncInRegion(region string) error {
	const pageSize int64 = 100

	client, err := clb.NewClient(s.cred.Credential, region, profile.NewClientProfile())
	if err != nil {
		return err
	}

	request := clb.NewDescribeLoadBalancersRequest()
	request.Limit = fields.Int64(pageSize)

	var lastError error

	for page := int64(0); ; page++ {
		request.Offset = fields.Int64(page * pageSize)

		response, err := client.DescribeLoadBalancers(request)
		if err != nil {
			s.log.Error("Failed to describe load balancers", "region", region, "error", err)
			return err
		}

		for _, lb := range response.Response.LoadBalancerSet {
			conds := []fields.Field{
				fields.NamedPField(fields.ExternalUUIDFieldKey, lb.LoadBalancerId),
				fields.FactoryField(s.cred.Factory.GetID()),
			}

			attrs := BeautyWith(lb,
				fields.FactoryField(s.cred.Factory.GetID()),
				fields.FactoryAccountField(s.cred.Account.GetID()),
			)

			// zone
			if lb.MasterZone != nil {
				if zone := cache.GetFactoryZone(s.cred.Factory.GetID(), *lb.MasterZone.Zone); zone != nil {
					attrs.With(fields.ZoneField(zone.GetID()))
				} else {
					s.log.Error("Zone not found", "zone", *lb.MasterZone.Zone, "region", region)
				}
			}

			if _, perr := restclient.PostOrPatch[models.SLB](conds, attrs); perr != nil {
				s.log.Error("Failed to sync slb", "id", *lb.LoadBalancerId, "region", region, "error", perr)
				lastError = perr
			}
		}

		if len(response.Response.LoadBalancerSet) < int(pageSize) {
			break
		}
	}

	return lastError
}
