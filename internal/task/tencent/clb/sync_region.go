package clb

import (
	clb "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/clb/v20180317"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/profile"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
)

func (s *Syncer) SyncInRegion(region *models.Region) error {
	const pageSize int64 = 100

	client, err := clb.NewClient(s.cred.Credential, region.RegionID, profile.NewClientProfile())
	if err != nil {
		return err
	}

	request := clb.NewDescribeLoadBalancersRequest()
	request.Limit = fields.Int64(pageSize)

	var lastError error

	for page := int64(0); ; page++ {
		request.Offset = fields.Int64(page * pageSize)

		response, err := client.DescribeLoadBalancers(request)
		if err != nil {
			s.log.Error("Failed to describe load balancers", "region", region.RegionID, "error", err)
			return err
		}

		for _, lb := range response.Response.LoadBalancerSet {
			conds := []fields.Field{
				fields.NamedPField(fields.ExternalUUIDFieldKey, lb.LoadBalancerId),
				fields.FactoryField(s.cred.Factory.GetID()),
			}

			attrs := BeautyWith(lb,
				fields.FactoryField(s.cred.Factory.GetID()),
				fields.FactoryAccountField(s.cred.Account.GetID()),
				fields.RegionField(region.GetID()),
			)

			if _, perr := restclient.PostOrPatch[models.SLB](conds, attrs); perr != nil {
				s.log.Error("Failed to sync slb", "id", *lb.LoadBalancerId, "region", region, "error", perr)
				lastError = perr
			}
		}

		if len(response.Response.LoadBalancerSet) < int(pageSize) {
			break
		}
	}

	return lastError
}
