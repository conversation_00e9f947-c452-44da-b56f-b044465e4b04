package clb

import (
	"log/slog"

	"gitlab.lilithgame.com/yunwei/pkg/logger"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/task/tencent"
)

type Syncer struct {
	cred *tencent.FactoryCredential
	log  *slog.Logger
}

func NewSyncer(accountKey string) (*Syncer, error) {
	cred, err := tencent.CreateCredentialWithAccount(accountKey)
	if err != nil {
		return nil, err
	}

	log := logger.Slog().With("factory", cred.Factory.Name, "account", cred.Account.Name)

	s := &Syncer{cred: cred, log: log}
	return s, nil
}

func (s *Syncer) Sync() error {
	regions, err := restclient.ListAll[models.Region](fields.NamedField("factory__id", s.cred.Factory.GetID()))
	if err != nil {
		return err
	}

	var lastError error
	for _, region := range regions.Results {
		if err := s.SyncInRegion(&region); err != nil {
			lastError = err
		}
	}

	return lastError
}

func Sync(account string) error {
	s, err := NewSyncer(account)
	if err != nil {
		return err
	}

	if err := s.Sync(); err != nil {
		return err
	}

	return nil
}
