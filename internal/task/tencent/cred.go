package tencent

import (
	"fmt"
	"strings"

	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/cache"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
)

type FactoryCredential struct {
	Factory    *models.Factory        `json:"factory"`
	Account    *models.FactoryAccount `json:"account"`
	Credential *common.Credential     `json:"-"`
}

func CreateCredential(secretName string) (*common.Credential, error) {
	var secrets restclient.TencentCredential

	if err := restclient.UnmarshalSecret(secretName, &secrets); err != nil {
		return nil, err
	}

	cred := common.NewCredential(
		secrets.SecretID,
		secrets.SecretKey,
	)

	return cred, nil
}

func CreateCredentialWithAccount(accountKey string) (*FactoryCredential, error) {
	factory := strings.Split(accountKey, "-")
	if len(factory) == 0 {
		return nil, fmt.Errorf("invalid factory account key %s", accountKey)
	}

	factoryKey := factory[0]

	if f := cache.GetFactory(factoryKey); f != nil {
		account, err := restclient.GetAccount(f.GetID(), accountKey)
		if err != nil {
			return nil, err
		}

		cred, err := CreateCredential(account.KMSAccount)
		if err != nil {
			return nil, err
		}

		return &FactoryCredential{
			Factory:    f,
			Account:    account,
			Credential: cred,
		}, nil
	}

	return nil, fmt.Errorf("account %s not found in factory %s", accountKey, factoryKey)
}
