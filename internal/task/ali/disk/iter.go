package disk

import (
	"errors"

	ecsv3 "github.com/alibabacloud-go/ecs-20140526/v3/client"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/util"
)

var (
	ErrNoMoreDisk       = errors.New("no more disks")
	ErrIterationStopped = errors.New("iterator is stopped")
)

type DiskIterator struct {
	cli *ecsv3.Client

	Total       int32
	TotalPages  int32
	CurrentPage int32
	PageSize    int32

	currentPage chan *BlockDisk
}

func NewDiskIterator(cli *ecsv3.Client, pageSize int32) *DiskIterator {
	return &DiskIterator{
		cli:         cli,
		PageSize:    util.NumberRanger(int32(10), 100)(pageSize),
		currentPage: make(chan *BlockDisk, pageSize),
	}
}

func (it *DiskIterator) Close() {
	close(it.currentPage)
}

func (it *DiskIterator) resetCounts() {
	it.Total = 0
	it.TotalPages = 0
	it.CurrentPage = 0
}

func (it *DiskIterator) Next(region string) (*BlockDisk, error) {
	select {
	case d, ok := <-it.currentPage:
		// fmt.Printf("<-it.currentPageChan, d: %+v, ok: %v\n", d, ok)
		if !ok {
			return nil, ErrIterationStopped
		}

		return d, nil

	default:
		nextPage, err := it.NextPage(it.CurrentPage+1, region)
		if err != nil {
			return nil, err
		}

		it.Total = nextPage.Total
		it.CurrentPage = nextPage.CurrentPage
		it.TotalPages = nextPage.TotalPages

		// fmt.Printf("total: %d, page: %d/%d\n", it.Total, it.CurrentPage, it.TotalPages)

		for _, d := range nextPage.Disks {
			it.currentPage <- d
		}

		if len(nextPage.Disks) > 0 {
			return <-it.currentPage, nil
		}

		it.resetCounts()

		return nil, ErrNoMoreDisk
	}
}

type DiskPage struct {
	Total       int32
	TotalPages  int32
	CurrentPage int32
	PageSize    int32
	Disks       []*BlockDisk
}

func (it *DiskIterator) NextPage(page int32, region string) (*DiskPage, error) {
	resp, err := it.cli.DescribeDisks(&ecsv3.DescribeDisksRequest{
		RegionId:   fields.Pointer(region),
		PageSize:   fields.Pointer(it.PageSize),
		PageNumber: fields.Pointer(page),
	})
	if err != nil {
		return nil, ErrNoMoreDisk
	}

	disks := make([]*BlockDisk, 0)
	for _, disk := range resp.Body.Disks.Disk {
		d := diskDetail(disk)
		disks = append(disks, &d)
	}

	total := fields.PNumber(resp.Body.TotalCount)
	pageSize := fields.PNumber(resp.Body.PageSize)

	totalPages := total / pageSize
	if total%pageSize > 0 {
		totalPages++
	}

	return &DiskPage{
		Total:       total,
		TotalPages:  totalPages,
		PageSize:    fields.PNumber(resp.Body.PageSize),
		CurrentPage: fields.PNumber(resp.Body.PageNumber),
		Disks:       disks,
	}, nil
}
