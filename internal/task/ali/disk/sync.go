package disk

import (
	"fmt"
	"strings"
	"time"

	ecsv3client "github.com/alibabacloud-go/ecs-********/v3/client"
	"github.com/fatih/color"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
	alitask "gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/task/ali"
)

type Syncer struct {
	config  *alitask.FactoryCrendential
	regions map[string]models.Region
}

func NewSyncer(accountKey string) (*Syncer, error) {
	cfg, err := alitask.CreateFactoryCredential(accountKey)
	if err != nil {
		return nil, err
	}

	cfg.ClientConfig.SetEndpoint("ecs-cn-hangzhou.aliyuncs.com")

	return &Syncer{
		config: cfg,
		regions: restclient.LoadRegions(
			fields.NamedField("factory__name", "阿里云"),
			fields.Unlimited,
		),
	}, nil
}

func (s *Syncer) AccountID() int {
	return s.config.Account.GetID()
}

func (s *Syncer) Sync() error {
	var err error
	for region := range s.regions {
		if serr := s.SyncWithRegions(region); serr != nil {
			err = serr
			color.Red("sync with region %s failed, %w", region, serr)
		}
	}
	return err
}

func (s *Syncer) SyncWithRegions(regions ...string) error {
	var err error
	for _, region := range regions {
		if serr := s.SyncWithRegion(region); serr != nil {
			err = serr
		}
	}
	return err
}

func (s *Syncer) SyncWithRegion(region string) error {
	cli, err := CreateClient(s.config.ClientConfig)
	if err != nil {
		return err
	}

	it := NewDiskIterator(cli, 100)
	defer it.Close()

	additionsAttrs := []fields.Field{
		fields.FactoryAccountField(s.AccountID()),
	}

	for d, err := it.Next(region); err == nil && d != nil; d, err = it.Next(region) {
		if aerr := mergeToOMA(d, additionsAttrs...); aerr != nil {
			fmt.Printf("failed to merge to OMA: %s\n", aerr)
		}
	}

	return nil
}

type BlockDisk struct {
	ID         string `json:"disk_id"`
	Name       string `json:"disk_name"`
	Size       int32  `json:"size"`
	Type       string `json:"type"`
	InstanceID string
	Region     string
	Zone       string
	Device     string
	Status     string
	Category   string
	Tags       string `json:"tags"` // k1:v1,k2:v2,...,kn:vn
	CreateTime string
}

func diskDetail(d *ecsv3client.DescribeDisksResponseBodyDisksDisk) BlockDisk {
	t, _ := time.Parse(time.RFC3339, *d.CreationTime)
	createTime := t.Local().Format(time.RFC3339)

	// tags
	externalTags := make([]string, 0)
	if tags := d.Tags; tags != nil {
		for _, tag := range tags.Tag {
			externalTags = append(externalTags, fmt.Sprintf("%s:%s", fields.Value(tag.TagKey), fields.Value(tag.TagValue)))
		}
	}

	// rename map
	status := fields.PLowerString(d.Status)
	if status == "in_use" {
		status = "running"
	}

	return BlockDisk{
		ID:         fields.Value(d.DiskId),
		Name:       fields.Value(d.DiskName),
		Size:       fields.Value(d.Size),
		Type:       fields.Value(d.Type),
		InstanceID: fields.Value(d.InstanceId),
		Device:     fields.Value(d.Device),
		Region:     fields.Value(d.RegionId),
		Zone:       fields.Value(d.ZoneId),
		Status:     strings.ToUpper(status),
		Category:   fields.Value(d.Category),
		Tags:       strings.Join(externalTags, ","),
		CreateTime: createTime,
	}
}

func mergeToOMA(d *BlockDisk, additions ...fields.Field) error {
	attrs := fields.NewFields(additions...)
	attrs.With(
		fields.NamedField("disk_id", d.ID),
		fields.NamedField("name", d.Name),
		fields.NamedField("size", d.Size),
		fields.NamedField("disk_type", d.Type),
		fields.NamedField("zone", d.Zone),
		fields.NamedField("region", d.Region),
		fields.NamedField("device", d.Device),
		fields.NamedField("instance_id", d.InstanceID),
		fields.StatusField(d.Status),
		fields.NamedField("category", d.Category),
		fields.NamedField("tags", d.Tags),
		fields.NamedField("create_time", d.CreateTime),
	)

	if d.InstanceID != "" {
		attrs.With(fields.NamedField("last_bind_instance", d.InstanceID))
	}

	conds := []fields.Field{
		fields.NamedField("disk_id", d.ID),
	}

	_, err := restclient.PostOrPatch[models.BlockDisk](conds, attrs)
	return err
}
