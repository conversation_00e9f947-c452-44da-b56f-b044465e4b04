package disk

import (
	"gitlab.lilithgame.com/yunwei/pkg/logger"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
)

func Clean(t int64, conditions ...fields.Field) error {
	fs := append(conditions,
		fields.NamedField("updated_before", t),
		fields.Unlimited,
	)
	resp, err := restclient.List[models.BlockDisk](fs...)
	if err != nil {
		return err
	}

	logger.Printf("shoule delete %d old disks with conditions: %+v\n", len(resp.Results), conditions)
	for _, r := range resp.Results {
		if derr := restclient.Delete(r); derr != nil {
			err = derr
			logger.Errorf("delete disk failed, t = %d, err = %v, record = %+v", t, err, r)
		}
	}

	return err
}
