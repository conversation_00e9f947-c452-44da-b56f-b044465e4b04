package ali

import (
	"strconv"
	"strings"

	openapiv2 "github.com/alibabacloud-go/darabonba-openapi/v2/client"
	ecsv3 "github.com/alibabacloud-go/ecs-20140526/v3/client"
	alivpc "github.com/alibabacloud-go/vpc-20160428/v2/client"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/util"
)

func ImageTask(
	conf *openapiv2.Config,
	ALI_REG_MAP_FROM_CMDB map[string]float64,
	regionList []*alivpc.DescribeRegionsResponseBodyRegionsRegion,
	factoryId int,
) {
	// start := time.Now()
	ALL_IMAGE_LIST := []string{}
	var apiTotal int32
	for _, region := range regionList {
		var allCount, sub int32
		var pageSize int32 = 100
		var pageNumber int32 = 1
		conf.Endpoint = fields.Pointer("ecs." + *region.RegionId + ".aliyuncs.com")
		client, _ := ecsv3.NewClient(conf)
		ALL_IMAGE_LIST, sub, apiTotal, allCount = imageRequest(ALL_IMAGE_LIST, pageSize, pageNumber, region, client, sub, apiTotal, ALI_REG_MAP_FROM_CMDB, factoryId)
		if sub < allCount {
			for pageNumber = 2; pageNumber <= allCount/pageSize+1; pageNumber++ {
				ALL_IMAGE_LIST, sub, apiTotal, allCount = imageRequest(ALL_IMAGE_LIST, pageSize, pageNumber, region, client, sub, apiTotal, ALI_REG_MAP_FROM_CMDB, factoryId)
			}
		}
	}

	// 清理cmdb中无用数据
	{
		resp, err := restclient.ListAll[models.Image](fields.NamedField("factory", factoryId))
		if err == nil {
			for _, i := range resp.Results {
				if !util.IsContain(ALL_IMAGE_LIST, i.ExternalID) {
					// oma.DeleteByID("image", i.ID)
					restclient.Delete(i)
				}
			}
		}
	}
}

func imageRequest(ALL_IMAGE_LIST []string, pageSize, pageNumber int32, region *alivpc.DescribeRegionsResponseBodyRegionsRegion, client *ecsv3.Client, sub, total int32, ALI_REG_MAP_FROM_CMDB map[string]float64, factoryId int) ([]string, int32, int32, int32) {
	request := &ecsv3.DescribeImagesRequest{
		PageSize:     fields.Int32(pageSize),
		PageNumber:   fields.Int32(pageNumber),
		Architecture: fields.Pointer("x86_64"),
		RegionId:     region.RegionId,
	}
	resp, _err := client.DescribeImages(request)
	if _err != nil {
		return ALL_IMAGE_LIST, sub, total, 0
	}
	for _, image := range resp.Body.Images.Image {
		sub++
		if plat := strings.ToLower(*image.Platform); !strings.HasPrefix(plat, "centOS") &&
			!strings.HasPrefix(plat, "ubuntu") &&
			!strings.HasPrefix(plat, "Debian") {
			continue
		}

		total++

		attr := util.ImageAttrBuilder(
			*image.ImageId,
			*image.ImageName,
			*image.Description,
			*image.OSName,
			*image.ImageOwnerAlias,
			"",
			ALI_REG_MAP_FROM_CMDB[*region.RegionId],
			factoryId,
		)

		var selfFlag bool
		cond := []fields.Field{
			fields.NamedField("external_id", *image.ImageId),
		}

		if *image.ImageOwnerAlias == "self" {
			cmdbId := ALI_REG_MAP_FROM_CMDB[*region.RegionId]
			cond = append(cond,
				fields.NamedField("region", strconv.FormatFloat(cmdbId, 'f', 0, 64)),
			)
			selfFlag = true
		}

		if selfFlag {
			var img models.Image
			if has, err := restclient.Find(&img, cond...); err == nil {
				if has {
					restclient.Patch(&img, attr)
				} else {
					restclient.Post[models.Image](attr)
				}
			}
			// restclient.PostOrPatch(&models.Image{}, cond, attr)
		} else {
			if has, err := restclient.Find(&models.Image{}, cond...); err == nil && !has {
				restclient.Post[models.Image](attr)
			}
		}

		// if len(cmdbResultList) == 0 {
		// 	restclient.Post[models.Image](attr)
		// } else if len(cmdbResultList) == 1 && selfFlag {
		// 	// 自定义镜像需要更新
		// 	// fmt.Println("Image Existed but need update: ", *image.ImageId)
		// 	id := int(cmdbResultList[0].(map[string]interface{})["id"].(float64))
		// 	restclient.Patch[models.Image](id, attr)
		// } else if len(cmdbResultList) == 1 && !selfFlag {
		// 	// 系统镜像不更新
		// 	// fmt.Println("Image Existed but system image, not update: ", *image.ImageId)
		// } else {
		// 	fmt.Println("len(cmdbResultList) != 0/1: ", err, *image.ImageId)
		// }
		ALL_IMAGE_LIST = append(ALL_IMAGE_LIST, *image.ImageId)
	}
	return ALL_IMAGE_LIST, sub, total, int32(*resp.Body.TotalCount)
}
