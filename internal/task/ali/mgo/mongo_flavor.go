package mgo

type MongoFlavor struct {
	CPU    int
	Memory int
	Name   string
}

// 单节点实例规格 standalone-instance-types
var MongoStandaloneInstanceTypes = map[string]MongoFlavor{
	"dds.sn2.small.1":    {1, 2, "dds.sn2.small.1"},
	"dds.sn2.standard.1": {2, 4, "dds.sn2.standard.1"},
	"dds.sn2.large.1":    {4, 8, "dds.sn2.large.1"},
	"dds.sn4.xlarge.1":   {4, 16, "dds.sn4.xlarge.1"},
	"dds.sn2.xlarge.1":   {8, 16, "dds.sn2.xlarge.1"},
}

// 副本集实例规格 replica-set-instance-types
var MongoReplicaSetInstanceTypes = map[string]MongoFlavor{
	// 独享型云盘版
	"mdb.shard.4x.large.d":    {2, 8, "mdb.shard.4x.large.d"},
	"mdb.shard.8x.large.d":    {2, 16, "mdb.shard.8x.large.d"},
	"mdb.shard.2x.xlarge.d":   {4, 8, "mdb.shard.2x.xlarge.d"},
	"mdb.shard.4x.xlarge.d":   {4, 16, "mdb.shard.4x.xlarge.d"},
	"mdb.shard.8x.xlarge.d":   {4, 32, "mdb.shard.8x.xlarge.d"},
	"mdb.shard.2x.2xlarge.d":  {8, 16, "mdb.shard.2x.2xlarge.d"},
	"mdb.shard.4x.2xlarge.d":  {8, 32, "mdb.shard.4x.2xlarge.d"},
	"mdb.shard.8x.2xlarge.d":  {8, 64, "mdb.shard.8x.2xlarge.d"},
	"mdb.shard.2x.4xlarge.d":  {16, 32, "mdb.shard.2x.4xlarge.d"},
	"mdb.shard.4x.4xlarge.d":  {16, 64, "mdb.shard.4x.4xlarge.d"},
	"mdb.shard.8x.4xlarge.d":  {16, 128, "mdb.shard.8x.4xlarge.d"},
	"mdb.shard.2x.8xlarge.d":  {32, 64, "mdb.shard.2x.8xlarge.d"},
	"mdb.shard.4x.8xlarge.d":  {32, 128, "mdb.shard.4x.8xlarge.d"},
	"mdb.shard.8x.8xlarge.d":  {32, 256, "mdb.shard.8x.8xlarge.d"},
	"mdb.shard.2x.16xlarge.d": {64, 128, "mdb.shard.2x.16xlarge.d"},
	"mdb.shard.4x.16xlarge.d": {64, 256, "mdb.shard.4x.16xlarge.d"},
	"mdb.shard.8x.16xlarge.d": {64, 512, "mdb.shard.8x.16xlarge.d"},

	// 通用型云盘版
	"mdb.shard.2x.large.c":   {2, 4, "mdb.shard.2x.large.c"},
	"mdb.shard.4x.large.c":   {2, 8, "mdb.shard.4x.large.c"},
	"mdb.shard.2x.xlarge.c":  {4, 8, "mdb.shard.2x.xlarge.c"},
	"mdb.shard.4x.xlarge.c":  {4, 16, "mdb.shard.4x.xlarge.c"},
	"mdb.shard.2x.2xlarge.c": {8, 16, "mdb.shard.2x.2xlarge.c"},
	"mdb.shard.4x.2xlarge.c": {8, 32, "mdb.shard.4x.2xlarge.c"},
	"mdb.shard.2x.4xlarge.c": {16, 32, "mdb.shard.2x.4xlarge.c"},
	"mdb.shard.4x.4xlarge.c": {16, 64, "mdb.shard.4x.4xlarge.c"},
	"mdb.shard.2x.8xlarge.c": {32, 64, "mdb.shard.2x.8xlarge.c"},

	// 本地盘版
	"dds.mongo.mid":          {1, 2, "dds.mongo.mid"},
	"dds.mongo.standard":     {2, 4, "dds.mongo.standard"},
	"dds.mongo.large":        {4, 8, "dds.mongo.large"},
	"dds.mongo.xlarge":       {8, 16, "dds.mongo.xlarge"},
	"dds.mongo.2xlarge":      {8, 32, "dds.mongo.2xlarge"},
	"dds.mongo.4xlarge":      {16, 64, "dds.mongo.4xlarge"},
	"mongo.x8.medium":        {2, 16, "mongo.x8.medium"},
	"mongo.x8.large":         {4, 32, "mongo.x8.large"},
	"mongo.x8.xlarge":        {8, 64, "mongo.x8.xlarge"},
	"mongo.x8.2xlarge":       {16, 128, "mongo.x8.2xlarge"},
	"mongo.x8.4xlarge":       {32, 256, "mongo.x8.4xlarge"},
	"dds.mongo.2xmonopolize": {60, 440, "dds.mongo.2xmonopolize"},
	"dds.mongo.3xmonopolize": {90, 660, "dds.mongo.3xmonopolize"},
}

// 分片集群实例规格
var MongoSharedClusterInstanceTypes = map[string]MongoFlavor{
	// Mongos 独享型云盘版
	"mdb.shard.4x.large.d":   {2, 8, "mdb.shard.4x.large.d"},
	"mdb.shard.8x.large.d":   {2, 16, "mdb.shard.8x.large.d"},
	"mdb.shard.2x.xlarge.d":  {4, 8, "mdb.shard.2x.xlarge.d"},
	"mdb.shard.4x.xlarge.d":  {4, 16, "mdb.shard.4x.xlarge.d"},
	"mdb.shard.8x.xlarge.d":  {4, 32, "mdb.shard.8x.xlarge.d"},
	"mdb.shard.2x.2xlarge.d": {8, 16, "mdb.shard.2x.2xlarge.d"},
	"mdb.shard.4x.2xlarge.d": {8, 32, "mdb.shard.4x.2xlarge.d"},
	"mdb.shard.8x.2xlarge.d": {8, 64, "mdb.shard.8x.2xlarge.d"},
	"mdb.shard.2x.4xlarge.d": {16, 32, "mdb.shard.2x.4xlarge.d"},
	"mdb.shard.4x.4xlarge.d": {16, 64, "mdb.shard.4x.4xlarge.d"},
	"mdb.shard.8x.4xlarge.d": {16, 128, "mdb.shard.8x.4xlarge.d"},
	"mdb.shard.2x.8xlarge.d": {32, 64, "mdb.shard.2x.8xlarge.d"},
	"mdb.shard.4x.8xlarge.d": {32, 128, "mdb.shard.4x.8xlarge.d"},
	"mdb.shard.8x.8xlarge.d": {32, 256, "mdb.shard.8x.8xlarge.d"},

	// Shard 独享型云盘版
	// "mdb.shard.4x.large.d":    {2, 8, "mdb.shard.4x.large.d"},
	// "mdb.shard.8x.large.d":    {2, 16, "mdb.shard.8x.large.d"},
	// "mdb.shard.2x.xlarge.d":   {4, 8, "mdb.shard.2x.xlarge.d"},
	// "mdb.shard.4x.xlarge.d":   {4, 16, "mdb.shard.4x.xlarge.d"},
	// "mdb.shard.8x.xlarge.d":   {4, 32, "mdb.shard.8x.xlarge.d"},
	// "mdb.shard.2x.2xlarge.d":  {8, 16, "mdb.shard.2x.2xlarge.d"},
	// "mdb.shard.4x.2xlarge.d":  {8, 32, "mdb.shard.4x.2xlarge.d"},
	// "mdb.shard.8x.2xlarge.d":  {8, 64, "mdb.shard.8x.2xlarge.d"},
	// "mdb.shard.2x.4xlarge.d":  {16, 32, "mdb.shard.2x.4xlarge.d"},
	// "mdb.shard.4x.4xlarge.d":  {16, 64, "mdb.shard.4x.4xlarge.d"},
	// "mdb.shard.8x.4xlarge.d":  {16, 128, "mdb.shard.8x.4xlarge.d"},
	// "mdb.shard.2x.8xlarge.d":  {32, 64, "mdb.shard.2x.8xlarge.d"},
	// "mdb.shard.4x.8xlarge.d":  {32, 128, "mdb.shard.4x.8xlarge.d"},
	// "mdb.shard.8x.8xlarge.d":  {32, 256, "mdb.shard.8x.8xlarge.d"},
	"mdb.shard.2x.16xlarge.d": {64, 128, "mdb.shard.2x.16xlarge.d"},
	"mdb.shard.4x.16xlarge.d": {64, 256, "mdb.shard.4x.16xlarge.d"},
	"mdb.shard.8x.16xlarge.d": {64, 512, "mdb.shard.8x.16xlarge.d"},

	// ConfigServer 独享型云盘版
	// "mdb.shard.2x.xlarge.d": {4, 8, "mdb.shard.2x.xlarge.d"},

	// 本地盘版

	// 通用型本地盘版(Mongos)
	"dds.mongos.mid":      {1, 2, "dds.mongos.mid"},
	"dds.mongos.standard": {2, 4, "dds.mongos.standard"},
	"dds.mongos.large":    {4, 8, "dds.mongos.large"},
	"dds.mongos.xlarge":   {8, 16, "dds.mongos.xlarge"},
	"dds.mongos.2xlarge":  {8, 32, "dds.mongos.2xlarge"},
	"dds.mongos.4xlarge":  {16, 64, "dds.mongos.4xlarge"},

	// 通用型本地盘版(Shard)
	"dds.shard.mid":            {1, 2, "dds.shard.mid"},
	"dds.shard.standard":       {2, 4, "dds.shard.standard"},
	"dds.shard.large":          {4, 8, "dds.shard.large"},
	"dds.shard.xlarge":         {8, 16, "dds.shard.xlarge"},
	"dds.shard.2xlarge":        {8, 32, "dds.shard.2xlarge"},
	"dds.shard.4xlarge":        {16, 64, "dds.shard.4xlarge"},
	"dds.shard.sn8.xlarge.3":   {2, 16, "dds.shard.sn8.xlarge.3"},
	"dds.shard.sn8.2xlarge.3":  {4, 32, "dds.shard.sn8.2xlarge.3"},
	"dds.shard.sn8.4xlarge.3":  {8, 64, "dds.shard.sn8.4xlarge.3"},
	"dds.shard.sn8.8xlarge.3":  {16, 128, "dds.shard.sn8.8xlarge.3"},
	"dds.shard.sn8.16xlarge.3": {32, 256, "dds.shard.sn8.16xlarge.3"},

	// 独享型本地盘版(Shard)
	"mongo.x8.medium":  {2, 16, "mongo.x8.medium"},
	"mongo.x8.large":   {4, 32, "mongo.x8.large"},
	"mongo.x8.xlarge":  {8, 64, "mongo.x8.xlarge"},
	"mongo.x8.2xlarge": {16, 128, "mongo.x8.2xlarge"},
	"mongo.x8.4xlarge": {32, 256, "mongo.x8.4xlarge"},

	// 通用型本地盘版(ConfigServer)
	"dds.cs.mid": {1, 2, "dds.cs.mid"},
}

var MongoFlavors = map[string]map[string]MongoFlavor{
	"standalone": MongoStandaloneInstanceTypes,
	"replicate":  MongoReplicaSetInstanceTypes,
	"sharding":   MongoSharedClusterInstanceTypes,
}

func GetMongoSpec(flavor string) *MongoFlavor {
	for _, flavors := range MongoFlavors {
		if flavor, ok := flavors[flavor]; ok {
			return &flavor
		}
	}
	return nil
}

func GetMongoSpecByInstanceType(flavor, flavorType string) *MongoFlavor {
	if flavors, ok := MongoFlavors[flavorType]; ok {
		if flavor, ok := flavors[flavor]; ok {
			return &flavor
		}
	}
	return nil
}
