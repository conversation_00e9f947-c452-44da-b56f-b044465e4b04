package mgo

import (
	"slices"

	mgov4 "github.com/alibabacloud-go/dds-20151201/v4/client"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
	task "gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/task/ali"
)

type regionInfo struct {
	*models.Region
	Endpoint string `json:"endpoint"`
}

func completeAvailableRegions(s *Syncer) {
	availableRegions := s.AvailableRegions()

	regions := make(map[string]regionInfo, 0)

	restclient.LoadSomeRegions(
		func(r *models.Region) bool {
			if slices.Contains(task.DeprecatedRegions, r.RegionID) {
				return false
			}

			endpoint, found := availableRegions[r.RegionID]
			if found {
				regions[r.RegionID] = regionInfo{
					Region:   r,
					Endpoint: endpoint,
				}
			}
			return found
		},
		fields.NamedField("factory__name", "阿里云"),
		fields.Unlimited,
	)

	s.regions = regions
}

func (s *Syncer) AvailableRegions() map[string]string {
	cfg := *s.cred.ClientConfig
	cfg.SetRegionId("cn-hangzhou")

	cli, err := CreateClient(&cfg)
	if err != nil {
		return nil
	}

	resp, err := cli.DescribeRegions(&mgov4.DescribeRegionsRequest{})
	if err != nil {
		return nil
	}

	regions := make(map[string]string)
	for _, r := range resp.Body.Regions.DdsRegion {
		if !slices.Contains(task.DeprecatedRegions, *r.RegionId) {
			regions[*r.RegionId] = *r.RegionName
		}
	}

	return regions
}
