package mgo

import (
	"fmt"

	mgov4 "github.com/alibabacloud-go/dds-20151201/v4/client"
	"github.com/fatih/color"
	"gitlab.lilithgame.com/yunwei/pkg/logger"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
)

func (s *Syncer) SyncInRegion(region string) error {
	cfg := *s.cred.ClientConfig
	cfg.SetRegionId(region)
	cfg.SetEndpointType(s.regions[region].Endpoint)

	cli, err := CreateClient(&cfg)
	if err != nil {
		return err
	}

	// 同步分片集
	s.SyncReplicatesInRegion(cli, "sharding", region)

	// 同步副本集
	s.SyncReplicatesInRegion(cli, "replicate", region)

	return nil
}

func (s *Syncer) SyncReplicatesInRegion(cli *mgov4.Client, dbType string, region string) error {
	// cli, err := CreateClient(s.cred.ClientConfig, region)
	// if err != nil {
	// 	return err
	// }
	var err error

	req := &mgov4.DescribeDBInstancesRequest{
		RegionId:       fields.Pointer(region),
		PageSize:       fields.Int32(100),
		DBInstanceType: fields.Pointer(dbType),
	}

	commonAttr := fields.FieldList(
		fields.FactoryField(s.cred.Factory.GetID()),
		fields.FactoryAccountField(s.cred.Account.GetID()),
	)

	for n := int32(1); ; n++ {
		req.SetPageNumber(n)

		resp, derr := cli.DescribeDBInstances(req)
		if derr != nil {
			err = derr
			color.Red("sync mongo failed, %v", derr)
			break
		}

		for _, i := range resp.Body.DBInstances.DBInstance {
			attr := Beauty(i).With(commonAttr...)
			s.completeDetail(cli, *i.DBInstanceId, attr)

			// region
			if region := fields.Value(i.RegionId); region != "" {
				if r, found := s.regions[region]; found && r.RegionID != "" {
					attr.Set(fields.RegionFieldKey, r.GetID())
				}
			}

			conds := fields.FieldList(*attr.GetField(fields.ExternalUUIDFieldKey))

			if _, perr := restclient.PostOrPatch[models.Mongo](conds, attr); perr != nil {
				err = perr
				logger.Error("sync mongo failed", "err", perr)
			} else {
				color.Green("sync %s mongo success, %s %s",
					*i.DBInstanceType,
					attr.GetString(fields.ExternalUUIDFieldKey),
					attr.GetString(fields.ExternalNameFieldKey),
				)

				// 同步白名单
				s.SyncWhitelist(cli, *i.DBInstanceId)

				// 同步安全组
				s.SyncInstanceSecurityGroups(cli, *i.DBInstanceId)
			}
		}

		if len(resp.Body.DBInstances.DBInstance) < int(*resp.Body.PageSize) {
			break
		}
	}

	return err
}

func (s *Syncer) completeDetail(cli *mgov4.Client, instanceID string, attr fields.Fields) error {
	req := &mgov4.DescribeDBInstanceAttributeRequest{
		DBInstanceId: fields.Pointer(instanceID),
	}

	resp, err := cli.DescribeDBInstanceAttribute(req)
	if err != nil {
		return err
	}

	instances := resp.Body.DBInstances
	if len(instances.DBInstance) == 0 {
		return fmt.Errorf("no instance found for %s", instanceID)
	}

	instance := instances.DBInstance[0]
	attr.Update(beautyDetail(instance))

	return nil
}
