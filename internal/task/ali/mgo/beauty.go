package mgo

import (
	"strconv"
	"strings"

	mgov4 "github.com/alibabacloud-go/dds-20151201/v4/client"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/category"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
)

func Beauty(i *mgov4.DescribeDBInstancesResponseBodyDBInstancesDBInstance) fields.Fields {
	attr := fields.NewFields(
		fields.NamedPField(fields.ExternalUUIDFieldKey, i.DBInstanceId),
		fields.NamedPField(fields.ExternalNameFieldKey, i.DBInstanceDescription),
		fields.ExternalStatusField(fields.Value(i.DBInstanceStatus)),
		fields.NamedPField("db_type", i.DBInstanceType),
		fields.NamedPField("version", i.EngineVersion),
		fields.NamedPField(fields.CreateTimeFieldKey, i.CreationTime),
	)

	// external_tags
	if tags := i.Tags; tags != nil && len(tags.Tag) > 0 {
		tagList := make([]map[string]string, 0)
		for _, tag := range tags.Tag {
			tagList = append(tagList, map[string]string{
				"k": *tag.Key,
				"v": fields.Value(tag.Value),
			})
			attr.SetJSON("external_tags", tagList)
		}
	}

	if flavor := fields.Value(i.DBInstanceClass); flavor != "" {
		if spec := GetMongoSpecByInstanceType(flavor, fields.Value(i.DBInstanceType)); spec != nil {
			attr.With(
				fields.NamedField("cpu", spec.CPU),
				fields.NamedField("mem", spec.Memory),
			)
		}
	}

	if name := attr.GetString(fields.ExternalNameFieldKey); name != "" {
		if productId := category.Category(name, ""); productId != nil {
			attr.With(fields.ProductField(*productId))
		}
	}

	return attr
}

func beautyDetail(instance *mgov4.DescribeDBInstanceAttributeResponseBodyDBInstancesDBInstance) fields.Fields {
	attr := fields.NewFields(
		fields.NamedField("replica_set_name", instance.ReplicaSetName),
		fields.NamedPField("disk", instance.DBInstanceStorage),
		fields.NamedPField("iops", instance.MaxIOPS),
		fields.NamedPField("connections", instance.MaxConnections),
		fields.NamedPField("version", instance.EngineVersion),
		fields.NamedPField("flavor_name", instance.DBInstanceClass),
	)

	// replicats
	if replicats := instance.ReplicaSets; replicats != nil {
		var primaryConn, secondaryConn, readonlyConn string
		var primaryPort, secondaryPort, readonlyPort int

		for _, replicaSet := range replicats.ReplicaSet {
			role := fields.PValueApply(replicaSet.ReplicaSetRole, strings.ToLower)
			conn := fields.Value(replicaSet.ConnectionDomain)
			port, _ := strconv.Atoi(fields.Value(replicaSet.ConnectionPort))

			switch role {
			case "primary":
				primaryConn = conn
				primaryPort = port

			case "secondary":
				secondaryConn = conn
				secondaryPort = port

			case "readonly":
				readonlyConn += conn + ";"
				readonlyPort = port
			}
		}

		attr.With(
			fields.NamedField("primary_conn", primaryConn),
			fields.NamedField("primary_port", primaryPort),
			fields.NamedField("secondary_conn", secondaryConn),
			fields.NamedField("secondary_port", secondaryPort),
			fields.NamedField("readonly_conn", readonlyConn),
			fields.NamedField("readonly_port", readonlyPort),
		)
	}

	// shards
	if shards := instance.ShardList; shards != nil {
		shardConn := make([]string, 0)
		for _, shard := range shards.ShardAttribute {
			shardConn = append(shardConn, fields.Value(shard.ConnectString))
		}
		attr.With(fields.NamedField("shard_conn", strings.Join(shardConn, ";")))
	}

	// mongos
	if mongos := instance.MongosList; mongos != nil {
		mongosConn := make([]string, 0)
		for _, mongos := range mongos.MongosAttribute {
			mongosConn = append(mongosConn, fields.Value(mongos.ConnectSting))
		}

		attr.Set("mongos_conn", strings.Join(mongosConn, ";"))

		if mgoAttr := mongos.MongosAttribute; len(mgoAttr) > 0 {
			attr.With(fields.NamedField("mongos_port", fields.Value(mgoAttr[0].Port)))
		}
	}

	return attr
}
