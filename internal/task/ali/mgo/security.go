package mgo

import (
	"strings"
	"time"

	mgov4 "github.com/alibabacloud-go/dds-20151201/v4/client"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
)

// SyncWhitelist sync whitelist and security groups releated with rds instances.
func (s *Syncer) SyncWhitelist(cli *mgov4.Client, dbID string) (err error) {
	req := &mgov4.DescribeSecurityIpsRequest{
		DBInstanceId: fields.Pointer(dbID),
	}

	resp, err := cli.DescribeSecurityIps(req)
	if err != nil {
		return err
	}

	startAt := time.Now().Unix()

	// 未分组白名单
	if ungroupedIPs := resp.Body.SecurityIps; ungroupedIPs != nil {
		conds := fields.FieldList(
			fields.NilField("group_name"),
			fields.StringField("db", dbID),
		)
		restclient.PostOrPatch[models.MongoWhitelist](conds, fields.Fields{
			"group_name": nil,
			"ip_list":    fields.Value(ungroupedIPs),
			"db":         dbID,
		})
	}

	// 已分组白名单
	for _, group := range resp.Body.SecurityIpGroups.SecurityIpGroup {
		if fields.PValueApplyEqual(group.SecurityIpGroupAttribute, strings.ToLower, "hidden") {
			continue
		}

		attrs := fields.Fields{
			"group_name": fields.Value(group.SecurityIpGroupName),
			"ip_list":    fields.Value(group.SecurityIpList),
			"db":         dbID,
		}

		conds := fields.FieldList(
			*attrs.GetField("group_name"),
			*attrs.GetField("db"),
		)

		if _, perr := restclient.PostOrPatch[models.MongoWhitelist](conds, attrs); perr != nil {
			err = perr
		}
	}

	// clean outdated whitelists
	if err == nil {
		restclient.DeleteSubResource[models.Mongo](
			"whitelist",
			dbID,
			fields.NumberField("updated_before", startAt),
		)
	}

	return
}

func (s *Syncer) SyncInstanceSecurityGroups(cli *mgov4.Client, dbID string) error {
	input := &mgov4.DescribeSecurityGroupConfigurationRequest{
		DBInstanceId: fields.Pointer(dbID),
	}
	resp, err := cli.DescribeSecurityGroupConfiguration(input)
	if err != nil {
		return err
	}

	groups := make([]string, 0)
	for _, rel := range resp.Body.Items.RdsEcsSecurityGroupRel {
		groups = append(groups, *rel.SecurityGroupId)
	}

	restclient.PostSubResource[models.Mongo]("security_groups", dbID, fields.Fields{
		"security_groups": groups,
	})

	return nil
}
