package mgo

import (
	openapiv2 "github.com/alibabacloud-go/darabonba-openapi/v2/client"
	mgov4 "github.com/alibabacloud-go/dds-20151201/v4/client"
)

func CreateClient(cfg *openapiv2.Config) (*mgov4.Client, error) {
	cli, err := mgov4.NewClient(cfg)
	if err != nil {
		return nil, err
	}
	return cli, nil
}

// func setConfigEndpoint(c *openapiv2.Config, _ string) {
// 	c.SetEndpoint("mongodb.aliyuncs.com")
// 	// if _, found := endpointsStay[region]; found {
// 	// 	c.SetEndpoint("mongodb.aliyuncs.com")
// 	// } else {
// 	// 	c.SetEndpoint(fmt.Sprintf("mongodb.%s.aliyuncs.com", region))
// 	// }
// }

// var endpointsStay = map[string]bool{
// 	"cn-qingdao":     true,
// 	"cn-beijing":     true,
// 	"cn-wulanchabu":  true,
// 	"cn-shanghai":    true,
// 	"cn-hangzhou":    true,
// 	"cn-shenzhen":    true,
// 	"cn-heyuan":      true,
// 	"cn-guangzhou":   true,
// 	"ap-southeast-1": true,
// 	"cn-hongkong":    true,

// 	"cn-hangzhou-finance":   true,
// 	"cn-shanghai-finance-1": true,
// 	"cn-shenzhen-finance-1": true,
// }
