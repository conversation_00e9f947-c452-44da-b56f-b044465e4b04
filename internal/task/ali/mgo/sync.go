package mgo

import (
	"github.com/fatih/color"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/factory"
	alitask "gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/task/ali"
)

type Syncer struct {
	cred    *alitask.FactoryCrendential
	regions map[string]regionInfo
}

func NewSyncer(factoryKey factory.FactoryKeyType) (*Syncer, error) {
	cred, err := alitask.CreateFactoryCredential(factoryKey.String())
	if err != nil {
		return nil, err
	}

	s := &Syncer{cred: cred}
	completeAvailableRegions(s)

	return s, nil
}

func (s *Syncer) SyncAll() error {
	var err error
	for _, reg := range s.regions {
		color.Blue("  sync in %s", reg.RegionID)
		if serr := s.SyncInRegion(reg.RegionID); serr != nil {
			err = serr
		}
	}
	return err
}
