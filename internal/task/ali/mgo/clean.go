package mgo

import (
	"time"

	"gitlab.lilithgame.com/yunwei/pkg/logger"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
)

func (s *Syncer) Clean(t int64) error {
	var err error

	if serr := s.cleanOudatedInstance(t); serr != nil {
		err = serr
	}

	// 清理12小时还未更新的空账号的机器
	if serr := s.cleanEmptyAccountInstance(t, -time.Hour*2); serr != nil {
		err = serr
	}

	return err
}

func (s *Syncer) cleanOudatedInstance(t int64) error {
	conds := []fields.Field{
		fields.FactoryField(s.cred.Factory.GetID()),
		fields.FactoryAccountField(s.cred.Account.GetID()),
		fields.LessThan("update_at", t),
	}

	resp, err := restclient.ListAll[models.Mongo](conds...)
	if err != nil {
		return err
	}

	logger.Info("find outdated record to delete",
		"count", resp.Count,
		"updated_before", t,
		"factory", s.cred.Factory.Name,
		"account", s.cred.Account.Name,
	)

	for _, m := range resp.Results {
		if perr := restclient.Delete(m); perr != nil {
			err = perr
		}
	}

	return err
}

func (s *Syncer) cleanEmptyAccountInstance(t int64, delta time.Duration) error {
	conds := []fields.Field{
		fields.FactoryField(s.cred.Factory.GetID()),
		fields.IsNull(fields.FactoryAccountFieldKey),
	}

	dayAgo := time.Unix(t, 0).Add(delta).Unix()
	conds = append(conds, fields.LessThan("update_at", dayAgo))

	resp, err := restclient.ListAll[models.Mongo](conds...)
	if err != nil {
		return err
	}

	logger.Info("find empty account machine result to delete",
		"count", resp.Count,
		"updated_before", t,
		"factory", s.cred.Factory.Name,
		"account", s.cred.Account.Name,
	)

	for _, m := range resp.Results {
		if perr := restclient.Patch(&m, fields.NewFields(fields.ExternalStatusDeleted)); perr != nil {
			err = perr
		}
	}

	return err
}
