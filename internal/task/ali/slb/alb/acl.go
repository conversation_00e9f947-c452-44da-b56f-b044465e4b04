package alb

import (
	"time"

	albv2 "github.com/alibabacloud-go/alb-20200616/v2/client"
	"github.com/fatih/color"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
)

func (s *Syncer) SyncACL(cli *albv2.Client, region string) (err error) {
	input := &albv2.ListAclsRequest{
		MaxResults: fields.Int32(100),
	}

	for token, started := "", false; !started || token != ""; started = true {
		if token != "" {
			input.SetNextToken(token)
		}

		resp, lerr := cli.ListAcls(input)
		if lerr != nil {
			return lerr
		}

		if count := len(resp.Body.Acls); count == 0 {
			break
		}

		for _, acl := range resp.Body.Acls {
			attrs := fields.NewFields(
				fields.StringPField("acl_id", acl.AclId),
				fields.StringPField("acl_name", acl.AclName),
				fields.StringPField("ip_version", acl.AddressIPVersion),
				fields.StringPField("create_time", acl.CreateTime),
				fields.StringPField("status", acl.AclStatus),
				fields.StringPField("resource_group_id", acl.ResourceGroupId),
				fields.BoolNumberPField("config_managed_enabled", acl.ConfigManagedEnabled),
				fields.StringField("region", region),

				fields.FactoryField(s.cred.Factory.GetID()),
				fields.FactoryAccountField(s.cred.Account.GetID()),
			)

			conds := []fields.Field{
				fields.FactoryAccountField(s.cred.Account.GetID()),
				fields.StringPField("acl_id", acl.AclId),
			}

			if _, serr := restclient.PostOrPatch[models.ACL](conds, attrs); serr != nil {
				color.Red("sync %s failed %v", *acl.AclId, serr)
			}

			// acl entry sync and clean
			{
				startTime := time.Now().Unix()
				if serr := s.SyncACLEntry(cli, *acl.AclId, region); serr != nil {
					err = serr
				} else {
					restclient.DeleteSubResource[models.ACL]("old_entries", *acl.AclId, fields.NamedField(
						"updated_before", startTime,
					))
				}
			}
		}
	}

	return err
}

func (s *Syncer) SyncACLEntry(cli *albv2.Client, aclID string, region string) error {
	input := &albv2.ListAclEntriesRequest{
		AclId:      fields.Pointer(aclID),
		MaxResults: fields.Int32(100),
	}

	for token, started := "", false; !started || token != ""; started = true {
		if token != "" {
			input.SetNextToken(token)
		}

		resp, err := cli.ListAclEntries(input)
		if err != nil {
			return err
		}

		if count := len(resp.Body.AclEntries); count == 0 {
			break
		}

		for _, entry := range resp.Body.AclEntries {
			attrs := fields.NewFields(
				fields.StringField("acl", aclID),
				fields.StringPField("entry", entry.Entry),
				fields.StringPField("description", entry.Description),
				fields.StringPField("status", entry.Status),
			)

			conds := []fields.Field{
				fields.StringField("acl", aclID),
				fields.StringPField("entry", entry.Entry),
			}

			restclient.PostOrPatch[models.ACLEntry](conds, attrs)
		}
	}

	return nil
}
