package alb

import (
	"fmt"

	albv2 "github.com/alibabacloud-go/alb-20200616/v2/client"
	"github.com/fatih/color"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
)

const LBType = "ALB"

func (s *Syncer) Sync() error {
	for _, region := range s.regions {
		if err := s.SyncWithRegion(region.RegionID); err != nil {
			color.Red("sync region %s error: %v\n", region.RegionID, err)
		}
	}

	return nil
}

func (s *Syncer) SyncWithRegion(region string) error {
	cli, err := CreateALBClient(s.cred.ClientConfig, region)
	if err != nil {
		return err
	}

	s.SyncALBWithRegion(cli, region)
	s.SyncListenerWithRegion(cli, region)
	s.SyncACL(cli, region)

	return nil
}

func (s *Syncer) SyncALBWithRegion(cli *albv2.Client, region string) error {
	var fetchToken string
	var started bool
	vpcer := VpcGetter()

	input := &albv2.ListLoadBalancersRequest{
		MaxResults: fields.Int32(100),
	}

	for ; fetchToken != "" || !started; started = true {
		if fetchToken != "" {
			input.SetNextToken(fetchToken)
		}

		resp, err := cli.ListLoadBalancers(input)
		if err != nil {
			return err
		}

		for _, l := range resp.Body.LoadBalancers {
			attrs := BeautyWith(l,
				fields.FactoryField(s.cred.Factory.GetID()),
				fields.FactoryAccountField(s.cred.Account.GetID()),
				fields.RegionField(s.regions[region].GetID()),
			)

			if vpc, err := vpcer(*l.VpcId); err == nil {
				attrs.Set("vpc", vpc.GetID())
			}

			cond := []fields.Field{
				fields.NamedPField(fields.ExternalUUIDFieldKey, l.LoadBalancerId),
			}
			if _, err := restclient.PostOrPatch[models.SLB](cond, attrs); err != nil {
				color.Red("patch or post error: %v", err)
			} else {
				color.Green("update %s success\n", fields.Value(l.LoadBalancerName))
			}
		}

		fetchToken = fields.Value(resp.Body.NextToken)
	}

	return nil
}

func (s *Syncer) SyncListenerWithRegion(cli *albv2.Client, region string) error {
	input := &albv2.ListListenersRequest{
		MaxResults: fields.Pointer(int32(100)),
	}

	for token, started := "", false; token != "" || !started; started = true {
		if token != "" {
			input.SetNextToken(token)
		}

		resp, err := cli.ListListeners(input)
		if err != nil {
			return err
		}

		for _, l := range resp.Body.Listeners {
			attr := completeListenerAttr(cli, *l.ListenerId).With(
				fields.FactoryField(s.cred.Factory.GetID()),
				fields.FactoryAccountField(s.cred.Account.GetID()),
			)

			cond := []fields.Field{
				*attr.GetField(fields.ExternalNameFieldKey),
				*attr.GetField("lb"),
			}

			fmt.Printf("%+v\n", attr)

			if _, perr := restclient.PostOrPatch[models.SLBListener](cond, attr); perr != nil {
				color.Red("patch or post error: %v", perr)
			} else {
				color.Green("  listener => %s %s success\n", *l.ListenerId, *l.ListenerDescription)
			}
		}

		token = fields.PString(resp.Body.NextToken)
	}

	return nil
}

func completeListenerAttr(cli *albv2.Client, lnID string) fields.Fields {
	resp, err := cli.GetListenerAttribute(&albv2.GetListenerAttributeRequest{
		ListenerId: &lnID,
	})
	if err != nil {
		return nil
	}

	return BeautyListenerAttr(resp.Body)
}
