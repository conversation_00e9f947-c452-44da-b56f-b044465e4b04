package alb

import (
	"strings"

	albv2 "github.com/alibabacloud-go/alb-20200616/v2/client"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/category"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
)

func Beauty(l *albv2.ListLoadBalancersResponseBodyLoadBalancers) fields.Fields {
	fz := fields.NewFields(
		fields.NamedField("lb_type", LBType),
		fields.NamedPField(fields.ExternalUUIDFieldKey, l.LoadBalancerId),
		fields.NamedPField("address", l.DNSName),
		fields.NamedPField(fields.ExternalNameFieldKey, l.Load<PERSON>alancerName),
		fields.NamedPField("edition", l.LoadBalancerEdition),
		fields.NamedPField("ip_version", l.AddressIpVersion),
		fields.NamedPField(fields.CreateTimeFieldKey, l.CreateTime),
	)

	fields.SetAnyValue(fz, "address_type", l.AddressType, l.Ipv6AddressType)

	// status renaming
	status := fields.PLowerString(l.LoadBalancerStatus)
	if status == "active" {
		status = "running"
	}
	fz.SetField(fields.ExternalStatusField(status))

	if productID := resolveProductId(l); productID != nil {
		fields.SetValue(fz, fields.ProductFieldKey, productID)
	}

	return fz
}

func BeautyWith(l *albv2.ListLoadBalancersResponseBodyLoadBalancers, additions ...fields.Field) fields.Fields {
	fz := Beauty(l)
	fz.With(additions...)
	return fz
}

func resolveClusterNameFromTags(tags []*albv2.ListLoadBalancersResponseBodyLoadBalancersTags) string {
	for _, tag := range tags {
		if fields.PValueApply(tag.Key, strings.ToLower) == "ack.aliyun.com" {
			return category.ResolveClusterNameByID(*tag.Value)
		}
	}
	return ""
}

func resolveProductId(l *albv2.ListLoadBalancersResponseBodyLoadBalancers) *int {
	if productID := category.Category(*l.LoadBalancerName, ""); productID != nil {
		return productID
	}

	if clusterName := resolveClusterNameFromTags(l.Tags); clusterName != "" {
		if productID := category.Category(clusterName, ""); productID != nil {
			return productID
		}
	}
	return nil
}

// func BeautyListener(l *albv2.ListListenersResponseBodyListeners) fields.Fields {
// 	attr := fields.NewFields(
// 		fields.StringPField(fields.ExternalUUIDFieldKey, l.ListenerId),
// 		fields.StringPField(fields.ExternalNameFieldKey, l.ListenerDescription),
// 		fields.ExternalStatusField(fields.PString(l.ListenerStatus)),
// 		fields.StringPField("lb", l.LoadBalancerId),
// 		fields.StringPField("proto", l.ListenerProtocol),
// 		fields.NumberPField("port", l.ListenerPort),
// 	)

// 	return attr
// }

func BeautyListenerAttr(l *albv2.GetListenerAttributeResponseBody) fields.Fields {
	attr := fields.NewFields(
		fields.StringPField(fields.ExternalUUIDFieldKey, l.ListenerId),
		fields.StringPField(fields.ExternalNameFieldKey, l.ListenerDescription),
		fields.ExternalStatusField(fields.PString(l.ListenerStatus)),
		fields.StringPField("lb", l.LoadBalancerId),
		fields.StringPField("proto", l.ListenerProtocol),
		fields.NumberPField("port", l.ListenerPort),
	)

	if aclConfig := l.AclConfig; aclConfig != nil {
		attr.SetString("acl_type", aclConfig.AclType)

		aclIds := make([]string, 0)
		for _, ac := range aclConfig.AclRelations {
			if fields.PValueApplyEqual(ac.Status, strings.ToLower, "associated") {
				aclIds = append(aclIds, fields.Value(ac.AclId))
			}
		}
		attr.Set("acl_uuid", strings.Join(aclIds, ","))
		if len(aclIds) > 0 {
			attr.Set("acl_status", "associated")
		}
	}

	return attr
}
