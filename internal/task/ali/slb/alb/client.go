package alb

import (
	"fmt"

	albv2 "github.com/alibabacloud-go/alb-20200616/v2/client"
	openapiv2 "github.com/alibabacloud-go/darabonba-openapi/v2/client"
)

// CreateALBClient creates a new 应用型负载均衡客户端
func CreateALBClient(config *openapiv2.Config, region string) (*albv2.Client, error) {
	cfg := *config
	cfg.SetEndpoint(fmt.Sprintf("alb.%s.aliyuncs.com", region))

	cli, err := albv2.NewClient(&cfg)
	if err != nil {
		return nil, err
	}

	return cli, nil
}
