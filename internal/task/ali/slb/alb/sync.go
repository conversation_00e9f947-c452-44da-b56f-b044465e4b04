package alb

import (
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
	alitask "gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/task/ali"
)

type Syncer struct {
	cred    *alitask.FactoryCrendential
	regions map[string]models.Region
}

func NewSyncer(accountKey string) (*Syncer, error) {
	cred, err := alitask.CreateFactoryCredential(accountKey)
	if err != nil {
		return nil, err
	}

	regions := restclient.LoadRegions(
		fields.NamedField("factory__name", "阿里云"),
		fields.Unlimited,
	)

	return &Syncer{
		cred:    cred,
		regions: regions,
	}, nil
}
