package nlb

import (
	"strings"

	nlbv3 "github.com/alibabacloud-go/nlb-20220430/v3/client"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/category"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
)

func Beauty(l *nlbv3.ListLoadBalancersResponseBodyLoadBalancers) fields.Fields {
	fz := fields.NewFields(
		fields.NamedField("lb_type", LBType),
		fields.NamedPField(fields.ExternalUUIDFieldKey, l.LoadBalancerId),
		fields.NamedPField("address", l.DNSName),
		fields.NamedPField(fields.ExternalNameFieldKey, l.LoadBalancerName),
		fields.NamedPField(fields.CreateTimeFieldKey, l.CreateTime),
		fields.ExternalStatusField(fields.PString(l.LoadBalancerStatus)),
	)

	fields.SetValueOrDefault(fz, "ip_version", l.AddressIpVersion)
	fields.SetAnyValue(fz, "address_type", l.AddressType, l.Ipv6AddressType)

	// status renaming
	status := fields.PLowerString(l.LoadBalancerStatus)
	if status == "active" {
		status = "running"
	}
	fz.SetField(fields.ExternalStatusField(status))

	if productID := resolveProductId(l); productID != nil {
		fields.SetValue(fz, fields.ProductFieldKey, productID)
	}

	return fz
}

func BeautyWith(l *nlbv3.ListLoadBalancersResponseBodyLoadBalancers, additions ...fields.Field) fields.Fields {
	return Beauty(l).With(additions...)
}

func resolveClusterNameFromTags(tags []*nlbv3.ListLoadBalancersResponseBodyLoadBalancersTags) string {
	for _, tag := range tags {
		if fields.PValueApply(tag.Key, strings.ToLower) == "ack.aliyun.com" {
			return category.ResolveClusterNameByID(*tag.Value)
		}
	}
	return ""
}

func resolveProductId(l *nlbv3.ListLoadBalancersResponseBodyLoadBalancers) *int {
	if productID := category.Category(*l.LoadBalancerName, ""); productID != nil {
		return productID
	}

	if clusterName := resolveClusterNameFromTags(l.Tags); clusterName != "" {
		if productID := category.Category(clusterName, ""); productID != nil {
			return productID
		}
	}
	return nil
}
