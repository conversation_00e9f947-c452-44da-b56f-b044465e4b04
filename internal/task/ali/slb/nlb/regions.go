package nlb

import (
	nlbv3 "github.com/alibabacloud-go/nlb-20220430/v3/client"
)

func (s *Syncer) AvailableRegions() map[string]string {
	regions := make(map[string]string)

	cli, err := CreateNLBClient(s.cred.ClientConfig, "cn-hangzhou")
	if err != nil {
		return nil
	}

	resp, err := cli.DescribeRegions(&nlbv3.DescribeRegionsRequest{})
	if err != nil {
		return nil
	}

	for _, r := range resp.Body.Regions {
		regions[*r.RegionId] = *r.LocalName
	}

	return regions
}
