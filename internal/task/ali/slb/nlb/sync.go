package nlb

import (
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
	alitask "gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/task/ali"
)

type Syncer struct {
	cred    *alitask.FactoryCrendential
	regions map[string]models.Region
}

func NewSyncer(accountKey string) (*Syncer, error) {
	cred, err := alitask.CreateFactoryCredential(accountKey)
	if err != nil {
		return nil, err
	}

	s := &Syncer{cred: cred}

	availableRegions := s.AvailableRegions()
	s.regions = restclient.LoadSomeRegions(
		func(r *models.Region) bool {
			_, found := availableRegions[r.RegionID]
			return found
		},
		fields.NamedField("factory__name", "阿里云"),
		fields.Unlimited,
	)

	return s, nil
}
