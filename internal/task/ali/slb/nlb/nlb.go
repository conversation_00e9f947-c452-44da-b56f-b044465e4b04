package nlb

import (
	nlbv3 "github.com/alibabacloud-go/nlb-20220430/v3/client"
	"github.com/fatih/color"
	"gitlab.lilithgame.com/yunwei/pkg/logger"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
)

const LBType = "NLB"

func (s *Syncer) Sync() error {
	for _, region := range s.regions {
		logger.Debug("sync nlb in region", "region", region)
		if err := s.SyncWithRegion(region.RegionID); err != nil {
			color.Red("sync region %s error: %v\n", region.RegionID, err)
		}
	}

	return nil
}

func (s *Syncer) SyncWithRegion(region string) error {
	cli, err := CreateNLBClient(s.cred.ClientConfig, region)
	if err != nil {
		return err
	}

	var fetchToken string
	var started bool
	vpcer := VpcGetter()

	input := &nlbv3.ListLoadBalancersRequest{
		RegionId:   &region,
		MaxResults: fields.Int32(100),
	}

	for ; fetchToken != "" || !started; started = true {
		if fetchToken != "" {
			input.SetNextToken(fetchToken)
		}

		resp, err := cli.ListLoadBalancers(input)
		if err != nil {
			return err
		}

		for _, l := range resp.Body.LoadBalancers {
			attrs := Beauty(l).With(
				fields.FactoryField(s.cred.Factory.GetID()),
				fields.FactoryAccountField(s.cred.Account.GetID()),
				fields.RegionField(s.regions[region].GetID()),
			)

			if vpc, err := vpcer(fields.PString(l.VpcId)); err == nil {
				attrs.Set("vpc", vpc.GetID())
			}

			cond := []fields.Field{
				fields.NamedPField(fields.ExternalUUIDFieldKey, l.LoadBalancerId),
			}

			if _, err := restclient.PostOrPatch[models.SLB](cond, attrs); err != nil {
				color.Red("patch or post error: %v", err)
			} else {
				color.Green("update %s success\n", fields.PString(l.LoadBalancerName))

				//  同步安全组信息
				sgs := make([]string, 0)
				for _, sg := range l.SecurityGroupIds {
					sgs = append(sgs, fields.Value(sg))
				}

				restclient.PostSubResource[models.SLB](
					"security_groups",
					*l.LoadBalancerId,
					fields.Fields{"security_groups": sgs},
				)
			}
		}

		fetchToken = fields.PString(resp.Body.NextToken)
	}

	return nil
}
