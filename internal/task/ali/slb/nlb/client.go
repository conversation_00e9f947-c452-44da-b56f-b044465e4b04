package nlb

import (
	"fmt"

	openapiv2 "github.com/alibabacloud-go/darabonba-openapi/v2/client"
	nlbv3 "github.com/alibabacloud-go/nlb-20220430/v3/client"
)

// CreateNLBClient creates a new 网络型负载均衡客户端
func CreateNLBClient(config *openapiv2.Config, region string) (*nlbv3.Client, error) {
	cfg := *config
	cfg.SetRegionId(region)
	cfg.SetEndpoint(fmt.Sprintf("nlb.%s.aliyuncs.com", region))
	// cfg.SetEndpoint("nlb.cn-hangzhou.aliyuncs.com")

	cli, err := nlbv3.NewClient(&cfg)
	if err != nil {
		return nil, err
	}

	return cli, nil
}
