package slb

import (
	"time"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/factory"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/task/ali/slb/alb"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/task/ali/slb/clb"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/task/ali/slb/nlb"
)

type Syncer interface {
	Sync() error
	Clean(conditions ...fields.Field) error
}

func SyncWithAccount(accountKey string) error {
	syncers := make([]Syncer, 0)

	// alb
	albSyncer, err := alb.NewSyncer(accountKey)
	if err != nil {
		return err
	}
	syncers = append(syncers, albSyncer)

	// nlb
	nlbSyncer, err := nlb.NewSyncer(accountKey)
	if err != nil {
		return err
	}
	syncers = append(syncers, nlbSyncer)

	// clb
	clbSyncer, err := clb.NewSyncer(accountKey)
	if err != nil {
		return err
	}
	syncers = append(syncers, clbSyncer)

	// start syncing
	for _, syncer := range syncers {
		startAt := time.Now().Unix()
		if serr := syncer.Sync(); serr != nil {
			err = serr
		} else {
			syncer.Clean(fields.LessThan("update_at", startAt))
		}
	}

	return err
}

func SyncAll() error {
	var err error
	for _, account := range factory.AliyunFactories {
		if serr := SyncWithAccount(account.String()); serr != nil {
			err = serr
		}
	}

	return err
}
