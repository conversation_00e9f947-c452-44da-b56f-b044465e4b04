package clb

import (
	slbv4 "github.com/alibabacloud-go/slb-20140515/v4/client"
)

func (s *Syncer) AvailableRegions() map[string]string {
	regions := make(map[string]string)

	cli, err := CreateCLBClient(s.cred.ClientConfig, "cn-hangzhou")
	if err != nil {
		return nil
	}

	resp, err := cli.DescribeRegions(&slbv4.DescribeRegionsRequest{})
	if err != nil {
		return nil
	}

	for _, reg := range resp.Body.Regions.Region {
		regions[*reg.RegionId] = *reg.LocalName
	}

	return regions
}
