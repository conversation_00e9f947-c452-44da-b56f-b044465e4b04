package clb

import (
	"time"

	slbv4 "github.com/alibabacloud-go/slb-20140515/v4/client"
	"github.com/fatih/color"
	"gitlab.lilithgame.com/yunwei/pkg/logger"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
)

const LBType = "CLB"

func (s *Syncer) Sync() error {
	var errs error

	for _, region := range s.regions {
		if err := s.SyncWithRegion(region.RegionID); err != nil {
			logger.Error("sync region error", "region", region.RegionID, "error", err.Error())
			errs = err
		}
	}

	return errs
}

func (s *Syncer) SyncWithRegion(region string) error {
	cfg := *s.cred.ClientConfig
	cfg.SetRegionId(region)

	cli, err := CreateCLBClient(&cfg, region)
	if err != nil {
		return err
	}

	color.Yellow("sync region: %s\n", region)

	s.SyncLBRegion(cli, region)
	s.SyncListnerAttrWithRegion(cli, region)
	s.SyncACL(cli, region)

	return nil
}

func (s *Syncer) SyncLBRegion(cli *slbv4.Client, region string) (err error) {
	vpcer := VpcGetter()

	input := &slbv4.DescribeLoadBalancersRequest{
		RegionId: fields.Pointer(region),
		PageSize: fields.Int32(100),
	}

	for p := int32(1); ; p++ {
		input.PageNumber = fields.Int32(p)

		resp, err := cli.DescribeLoadBalancers(input)
		if err != nil {
			break
		}

		for _, l := range resp.Body.LoadBalancers.LoadBalancer {
			attrs := BeautyWith(l,
				fields.FactoryField(s.cred.Factory.GetID()),
				fields.FactoryAccountField(s.cred.Account.GetID()),
				fields.RegionField(s.regions[region].GetID()),
			)

			if vpc, err := vpcer(fields.Value(l.VpcId)); err == nil {
				attrs.SetField(fields.VpcField(vpc.GetID()))
			}

			cond := []fields.Field{
				fields.NamedPField(fields.ExternalUUIDFieldKey, l.LoadBalancerId),
			}

			if _, err := restclient.PostOrPatch[models.SLB](cond, attrs); err != nil {
				color.Red("patch or post error: %v", err)
			} else {
				color.Green("update %s success\n", fields.Value(l.LoadBalancerName))

				// sync default servers
				{
					if perr := s.SyncBackendServer(cli, *l.LoadBalancerId, region); perr != nil {
						color.Red("sync default server failed %v", perr)
					}
				}

				// sync related virtual groups and server list
				{
					startTime := time.Now().Unix()
					if perr := s.SyncLbServerGroups(cli, *l.LoadBalancerId, region); perr == nil {
						if derr := restclient.DeleteSubResource[models.SLB]("old_server_and_group", *l.LoadBalancerId, fields.NumberField(
							"updated_before", startTime,
						)); derr != nil {
							logger.Errorf("delete old server and group error: %v", derr)
						}
					} else {
						color.Red("sync server and group error, %v, lb = %s", perr, *l.LoadBalancerId)
					}
				}
			}
		}

		// fmt.Printf("resp items %d,  pageSize: %d\n", len(resp.Body.LoadBalancers.LoadBalancer), int(*input.PageSize))

		if len(resp.Body.LoadBalancers.LoadBalancer) < int(*input.PageSize) {
			break
		}
	}

	return err
}

func (s *Syncer) SyncListnerAttrWithRegion(cli *slbv4.Client, region string) (err error) {
	input := &slbv4.DescribeLoadBalancerListenersRequest{
		MaxResults: fields.Pointer(int32(1)),
		RegionId:   fields.Pointer(region),
	}

	for token, started := "", false; token != "" || !started; started = true {
		if token != "" {
			input.SetNextToken(token)
		}

		resp, cerr := cli.DescribeLoadBalancerListeners(input)
		if cerr != nil {
			return cerr
		}

		for _, ln := range resp.Body.Listeners {
			lnAttr := BeautyListener(ln)

			conds := []fields.Field{
				*lnAttr.GetField("lb"),
				*lnAttr.GetField(fields.ExternalNameFieldKey),
			}

			if _, perr := restclient.PostOrPatch[models.SLBListener](conds, lnAttr); perr != nil {
				color.Red("patch or post error: %v", perr)
			} else {
				color.Green("  [listener] %s success\n", lnAttr.GetString(fields.ExternalNameFieldKey))
			}
		}

		token = fields.PString(resp.Body.NextToken)
	}

	return
}

func (s *Syncer) SyncBackendServer(cli *slbv4.Client, lb string, region string) (err error) {
	input := &slbv4.DescribeLoadBalancerAttributeRequest{
		LoadBalancerId: fields.Pointer(lb),
		RegionId:       fields.Pointer(region),
	}

	resp, err := cli.DescribeLoadBalancerAttribute(input)
	if err != nil {
		return
	}

	for _, server := range resp.Body.BackendServers.BackendServer {
		attrs := fields.NewFields(
			fields.StringPField("server_id", server.ServerId),
			fields.StringPField("server_type", server.Type),
			fields.IntPField("weight", server.Weight),
			fields.StringPField("desc", server.Description),
			fields.StringField("lb", lb),
		)

		if ip := server.ServerIp; ip != nil {
			attrs.Set("server_ip", *ip)
		}

		conds := []fields.Field{
			fields.IsNull("server_group__group_id"), // 默认服务器组 group_id 为空
			fields.StringField("lb", lb),
			fields.StringPField("server_id", server.ServerId),
		}

		if _, perr := restclient.PostOrPatch[models.LBServer](conds, attrs); perr != nil {
			err = perr
		}
	}

	return
}
