package clb

import (
	"strings"
	"time"

	slbv4 "github.com/alibabacloud-go/slb-20140515/v4/client"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/category"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
)

func Beauty(l *slbv4.DescribeLoadBalancersResponseBodyLoadBalancersLoadBalancer) fields.Fields {
	fz := fields.NewFields(
		fields.NamedField("lb_type", LBType),
		fields.NamedPField(fields.ExternalUUIDFieldKey, l.LoadBalancerId),
		fields.NamedPField("address", l.Address),
		fields.NamedPField(fields.ExternalNameFieldKey, l.LoadBalancerName),
		fields.NamedPField("network_type", l.NetworkType),
		fields.NamedPField("ip_version", l.AddressIPVersion),
		fields.NamedPField("address_type", l.AddressType),
		fields.NamedPField("vswitch_id", l.VSwitchId),
	)

	// status renaming
	status := fields.PLowerString(l.LoadBalancerStatus)
	if status == "active" {
		status = "running"
	}
	fz.SetField(fields.ExternalStatusField(status))

	if createTime := fields.PString(l.CreateTime); createTime != "" {
		if t, terr := time.Parse(time.RFC3339, createTime); terr == nil {
			fz.Set(fields.CreateTimeFieldKey, t.Local().Format(time.RFC3339))
		}
	}

	if productID := resolveProductId(l); productID != nil {
		fields.SetValue(fz, fields.ProductFieldKey, productID)
	}

	return fz
}

func BeautyWith(l *slbv4.DescribeLoadBalancersResponseBodyLoadBalancersLoadBalancer, additions ...fields.Field) fields.Fields {
	return Beauty(l).With(additions...)
}

func resolveClusterNameFromTags(tags []*slbv4.DescribeLoadBalancersResponseBodyLoadBalancersLoadBalancerTagsTag) string {
	for _, tag := range tags {
		if fields.PValueApply(tag.TagKey, strings.ToLower) == "ack.aliyun.com" {
			return category.ResolveClusterNameByID(*tag.TagValue)
		}
	}
	return ""
}

func resolveProductId(l *slbv4.DescribeLoadBalancersResponseBodyLoadBalancersLoadBalancer) *int {
	if productID := category.Category(*l.LoadBalancerName, ""); productID != nil {
		return productID
	}

	if tags := l.Tags; tags != nil {
		if clusterName := resolveClusterNameFromTags(tags.Tag); clusterName != "" {
			if productID := category.Category(clusterName, ""); productID != nil {
				return productID
			}
		}
	}
	return nil
}

func BeautyListener(l *slbv4.DescribeLoadBalancerListenersResponseBodyListeners) fields.Fields {
	attr := fields.NewFields(
		fields.StringPField("acl_uuid", l.AclId), //  可能为空
		fields.StringPField("acl_status", l.AclStatus),
		fields.StringPField("acl_type", l.AclType),
		fields.StringPField(fields.ExternalNameFieldKey, l.Description),
		fields.StringPField(fields.ExternalStatusFieldKey, l.Status),
		fields.StringPField("lb", l.LoadBalancerId),
		fields.StringPField("proto", l.ListenerProtocol),
		fields.NumberPField("port", l.ListenerPort),
		fields.NumberPField("port_forward", l.BackendServerPort),
		fields.StringPField("scheduler", l.Scheduler),
	)

	return attr
}
