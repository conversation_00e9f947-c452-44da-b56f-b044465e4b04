package clb

import (
	"fmt"

	openapiv2 "github.com/alibabacloud-go/darabonba-openapi/v2/client"
	slbv4 "github.com/alibabacloud-go/slb-20140515/v4/client"
)

// CreateCLBClient creates a new 传统型负载均衡客户端
func CreateCLBClient(config *openapiv2.Config, region string) (*slbv4.Client, error) {
	cfg := *config
	cfg.SetRegionId(region)

	setConfigEndpoint(&cfg, region)

	cli, err := slbv4.NewClient(&cfg)
	if err != nil {
		return nil, err
	}

	return cli, nil
}

func setConfigEndpoint(c *openapiv2.Config, region string) {
	// endpoint列表: https://api.aliyun.com/product/Slb
	if _, found := endpointsStay[region]; found {
		c.SetEndpoint("slb.aliyuncs.com")
	} else {
		c.SetEndpoint(fmt.Sprintf("slb.%s.aliyuncs.com", region))
	}
}

var endpointsStay = map[string]bool{
	"cn-qingdao":            true,
	"cn-shanghai":           true,
	"cn-beijing":            true,
	"cn-hangzhou":           true,
	"cn-shenzhen":           true,
	"ap-southeast-1":        true,
	"cn-hongkong":           true,
	"us-east-1":             true,
	"us-west-1":             true,
	"cn-hangzhou-finance":   true,
	"cn-shanghai-finance-1": true,
	"cn-shenzhen-finance-1": true,
}
