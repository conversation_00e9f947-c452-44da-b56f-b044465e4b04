package clb

import (
	slbv4 "github.com/alibabacloud-go/slb-20140515/v4/client"
	"github.com/fatih/color"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
)

func (s *Syncer) SyncLbServerGroups(cli *slbv4.Client, lbId string, region string) (err error) {
	input := &slbv4.DescribeVServerGroupsRequest{
		LoadBalancerId: fields.Pointer(lbId),
		RegionId:       fields.Pointer(region),
	}

	resp, err := cli.DescribeVServerGroups(input)
	if err != nil {
		return err
	}

	for _, sg := range resp.Body.VServerGroups.VServerGroup {
		sgAttr := fields.NewFields(
			fields.StringPField("group_id", sg.VServerGroupId),
			fields.StringPField("group_name", sg.VServerGroupName),
			fields.StringPField("create_time", sg.CreateTime),
			fields.StringField("lb", lbId),
			fields.FactoryField(s.cred.Factory.GetID()),
			fields.FactoryAccountField(s.cred.Account.GetID()),
		)

		conds := []fields.Field{
			*sgAttr.GetField("group_id"),
		}

		if _, perr := restclient.PostOrPatch[models.LBServerGroup](conds, sgAttr); perr != nil {
			err = perr
		} else {
			if pserr := s.SyncLbServers(cli, *sg.VServerGroupId, region); pserr != nil {
				err = pserr
				color.Red("sync server under %s error %v", *sg.VServerGroupId, err)
			}
		}

	}

	return
}

func (s *Syncer) SyncLbServers(cli *slbv4.Client, groupID string, region string) (err error) {
	input := &slbv4.DescribeVServerGroupAttributeRequest{
		VServerGroupId: fields.Pointer(groupID),
		RegionId:       fields.Pointer(region),
	}

	resp, err := cli.DescribeVServerGroupAttribute(input)
	if err != nil {
		return err
	}

	lbID := resp.Body.LoadBalancerId

	for _, srv := range resp.Body.BackendServers.BackendServer {
		attrs := fields.NewFields(
			fields.StringPField("server_id", srv.ServerId),
			fields.StringPField("server_type", srv.Type),
			fields.NumberPField("port", srv.Port),
			fields.NumberPField("weight", srv.Weight),
			fields.StringPField("desc", srv.Description),
			fields.StringPField("lb", lbID),
			fields.StringField("server_group", groupID),
		)

		if ip := srv.ServerIp; ip != nil {
			attrs.SetString("ip", ip)
		}

		conds := []fields.Field{
			fields.NamedField("server_group__group_id", groupID),
			*attrs.GetField("server_id"),
		}

		if _, perr := restclient.PostOrPatch[models.LBServer](conds, attrs); perr != nil {
			err = perr
		}
	}

	return
}

// SyncLbDefaultServer will sync the default servers of a load balancer
// It will not sync the servers that are in server groups
func (s *Syncer) SyncLbDefaultServer(cli *slbv4.Client, lbID string, region string) (err error) {
	input := &slbv4.DescribeHealthStatusRequest{
		LoadBalancerId: fields.Pointer(lbID),
		RegionId:       fields.Pointer(region),
	}

	resp, err := cli.DescribeHealthStatus(input)
	if err != nil {
		return err
	}

	for _, srv := range resp.Body.BackendServers.BackendServer {
		attrs := fields.NewFields(
			fields.StringPField("server_id", srv.ServerId),
			fields.NumberPField("port", srv.Port),
			fields.StringField("lb", lbID),
		)

		if ip := fields.Value(srv.ServerIp); ip != "" {
			attrs.Set("server_ip", ip)
		}

		conds := []fields.Field{
			fields.IsNull("server_group__group_id"), // 确保是在默认服务器组下的服务器
			fields.NamedField("lb", lbID),
			*attrs.GetField("server_id"),
		}

		if _, perr := restclient.PostOrPatch[models.LBServer](conds, attrs); perr != nil {
			err = perr
		}
	}

	return
}
