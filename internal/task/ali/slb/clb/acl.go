package clb

import (
	"time"

	slbv4 "github.com/alibabacloud-go/slb-20140515/v4/client"
	"github.com/fatih/color"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
)

func (s *Syncer) SyncACL(cli *slbv4.Client, region string) error {
	input := &slbv4.DescribeAccessControlListsRequest{
		RegionId: fields.Pointer(region),
		PageSize: fields.Int32(50),
	}

	var err error

	for p := int32(1); ; p++ {
		input.SetPageNumber(p)

		resp, derr := cli.DescribeAccessControlLists(input)
		if derr != nil {
			return derr
		}

		if count := fields.Value(resp.Body.Count); count == 0 {
			break
		}

		for _, acl := range resp.Body.Acls.Acl {
			attrs := fields.NewFields(
				fields.StringPField("acl_id", acl.AclId),
				fields.StringPField("acl_name", acl.AclName),
				fields.StringPField("ip_version", acl.AddressIPVersion),
				fields.StringPField("create_time", acl.CreateTime),
				fields.StringField("region", region),
				fields.StringPField("resource_group_id", acl.ResourceGroupId),
				fields.NilField("config_managed_enabled"),

				fields.FactoryField(s.cred.Factory.GetID()),
				fields.FactoryAccountField(s.cred.Account.GetID()),
			)

			conds := []fields.Field{
				fields.FactoryAccountField(s.cred.Account.GetID()),
				fields.StringPField("acl_id", acl.AclId),
			}

			if _, serr := restclient.PostOrPatch[models.ACL](conds, attrs); serr != nil {
				err = serr
				color.Red("sync clb's acl error %v", serr)

				continue
			}

			color.Green("sync %s %s ok", *acl.AclId, *acl.AclName)

			// sync entry and clean old entries
			{
				startTime := time.Now().Unix()

				if seerr := s.SyncACLEntry(cli, *acl.AclId, region); seerr != nil {
					err = seerr
					color.Red("sync clb's entry error %v", seerr)
				} else {
					restclient.DeleteSubResource[models.ACL]("old_entries", *acl.AclId, fields.NamedField(
						"updated_before", startTime,
					))
				}
			}
		}
	}

	return err
}

func (s *Syncer) SyncACLEntry(cli *slbv4.Client, aclID string, region string) error {
	input := &slbv4.DescribeAccessControlListAttributeRequest{
		AclId:    fields.Pointer(aclID),
		RegionId: fields.Pointer(region),
		PageSize: fields.Int32(100),
	}

	for p := int32(1); ; p++ {
		input.SetPage(p)

		resp, err := cli.DescribeAccessControlListAttribute(input)
		if err != nil {
			return err
		}

		entries := resp.Body.AclEntrys
		if entries == nil {
			break
		}

		for _, entry := range entries.AclEntry {
			attrs := fields.NewFields(
				fields.StringField("acl", aclID),
				fields.StringPField("entry", entry.AclEntryIP),
				fields.StringPField("description", entry.AclEntryComment),
			)

			conds := []fields.Field{
				fields.StringField("acl", aclID),
				fields.StringPField("entry", entry.AclEntryIP),
			}

			restclient.PostOrPatch[models.ACLEntry](conds, attrs)
		}
	}

	return nil
}
