package clb

import (
	"time"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
	alitask "gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/task/ali"
)

type Syncer struct {
	cred    *alitask.FactoryCrendential
	regions map[string]models.Region
}

func NewSyncer(accountKey string) (*Syncer, error) {
	cred, err := alitask.CreateFactoryCredential(accountKey)
	if err != nil {
		return nil, err
	}

	s := &Syncer{cred: cred}
	completeAvailableRegions(s)

	return s, nil
}

func completeAvailableRegions(s *Syncer) {
	availableRegions := s.AvailableRegions()
	regions := restclient.LoadSomeRegions(
		func(r *models.Region) bool {
			_, found := availableRegions[r.RegionID]
			return found
		},
		fields.NamedField("factory__name", "阿里云"),
		fields.Unlimited,
	)
	s.regions = regions
}

func Sync(account string) error {
	s, err := NewSyncer(account)
	if err != nil {
		return err
	}

	t := time.Now().Unix()
	if serr := s.Sync(); serr != nil {
		return serr
	}

	return s.Clean(fields.LessThan("updated_at", t))
}
