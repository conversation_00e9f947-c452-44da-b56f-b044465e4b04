package vpc

import (
	"cmp"
	"fmt"
	"log/slog"
	"time"

	vpcv6 "github.com/alibabacloud-go/vpc-********/v6/client"
	"gitlab.lilithgame.com/yunwei/pkg/logger"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
	task "gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/task/ali"
)

type Syncer struct {
	cred    *task.FactoryCrendential
	regions map[string]regionInfo
	log     *slog.Logger
}

func NewSyncer(accountKey string) (*Syncer, error) {
	cred, err := task.CreateFactoryCredential(accountKey)
	if err != nil {
		return nil, err
	}

	log := logger.Slog().With("factory", cred.Factory.KeyName, "account", cred.Account.KeyName)
	s := &Syncer{cred: cred, log: log}
	completeAvailableRegions(s)

	return s, nil
}

func (s *Syncer) Sync() error {
	var lastErr error

	for _, region := range s.regions {
		s.log.Debug("sync vpc in region", "region_id", region.RegionID, "region_name", region.Name)

		verr := s.SyncWithRegion(region.RegionID)
		swerr := s.SyncVSwitchWithRegion(region.RegionID)

		lastErr = cmp.Or(verr, swerr, lastErr)
	}

	return lastErr
}

func (s *Syncer) SyncWithRegion(region string) error {
	cfg := *s.cred.ClientConfig
	cfg.SetRegionId(region)
	cfg.SetEndpoint(s.regions[region].Endpoint)

	cli, err := CreateClient(&cfg)
	if err != nil {
		return fmt.Errorf("create client failed: %w", err)
	}

	input := &vpcv6.DescribeVpcsRequest{
		RegionId: fields.Pointer(region),
		PageSize: fields.Int32(50),
	}

	regionalLog := s.log.With("region_id", region)

	for n := int32(1); ; n++ {
		input.SetPageNumber(n)
		resp, err := cli.DescribeVpcs(input)
		if err != nil {
			return err
		}

		for _, v := range resp.Body.Vpcs.Vpc {
			attr := Beauty(v).With(
				fields.FactoryField(s.cred.Factory.GetID()),
				fields.FactoryAccountField(s.cred.Account.GetID()),
			)

			if region, found := s.regions[*v.RegionId]; found {
				attr.SetField(fields.RegionField(region.GetID()))
			}

			cond := []fields.Field{
				*attr.GetField("vpc_id"),
				// *attr.GetField(fields.FactoryAccountFieldKey),
			}

			if _, perr := restclient.PostOrPatch[models.VPC](cond, attr); perr != nil {
				regionalLog.Error("sync vpc failed", "error", perr)
			} else {
				regionalLog.Debug("sync vpc success", "vpc_id", *v.VpcId, "vpc_name", *v.VpcName)
			}
		}

		// if len(resp.Body.Vpcs.Vpc) < int(*input.PageSize) {
		if len(resp.Body.Vpcs.Vpc) == 0 {
			break
		}
	}

	return nil
}

func Sync(accout string) error {
	s, err := NewSyncer(accout)
	if err != nil {
		return err
	}

	t := time.Now().Unix()
	s.Sync()
	return s.Clean(t)
}
