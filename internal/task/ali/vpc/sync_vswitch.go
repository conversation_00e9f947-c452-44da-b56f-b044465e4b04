package vpc

import (
	"fmt"

	vpcv6 "github.com/alibabacloud-go/vpc-20160428/v6/client"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
)

func (s *Syncer) SyncVSwitchWithRegion(region string) error {
	regInfo := s.regions[region]

	cfg := *s.cred.ClientConfig
	cfg.SetEndpoint(regInfo.Endpoint)
	cfg.SetRegionId(regInfo.RegionID)

	cli, err := CreateClient(&cfg)
	if err != nil {
		return fmt.Errorf("create client failed: %w", err)
	}

	input := &vpcv6.DescribeVSwitchesRequest{
		RegionId: fields.Pointer(regInfo.RegionID),
		PageSize: fields.Int32(50),
	}

	for n := int32(1); ; n++ {
		input.SetPageNumber(n)
		resp, err := cli.DescribeVSwitches(input)
		if err != nil {
			return err
		}

		for _, v := range resp.Body.VSwitches.VSwitch {
			attr := BeautySwitch(v).With(
				fields.FactoryField(s.cred.Factory.GetID()),
				fields.FactoryAccountField(s.cred.Account.GetID()),
				fields.RegionField(regInfo.GetID()),
			)

			cond := []fields.Field{
				*attr.GetField("vswitch_id"),
			}

			if _, perr := restclient.PostOrPatch[models.VSwitch](cond, attr); perr != nil {
				s.log.Error("sync vswitch failed", "error", perr)
			}
		}

		if len(resp.Body.VSwitches.VSwitch) == 0 {
			break
		}
	}

	return nil
}
