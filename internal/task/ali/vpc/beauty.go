package vpc

import (
	vpcv6 "github.com/alibabacloud-go/vpc-20160428/v6/client"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
)

func Beauty(i *vpcv6.DescribeVpcsResponseBodyVpcsVpc) fields.Fields {
	attrs := fields.NewFields()

	attrs.WithString("vpc_id", i.VpcId).
		WithString("name", i.VpcName).
		WithString("cidr_block", i.CidrBlock).
		WithString("desc", i.Description).
		WithString("status", i.Status).
		WithString(fields.CreateTimeFieldKey, i.CreationTime)

	return attrs
}

func BeautySwitch(i *vpcv6.DescribeVSwitchesResponseBodyVSwitchesVSwitch) fields.Fields {
	attrs := fields.NewFields()

	attrs.WithString("vpc", i.VpcId).
		WithString("vswitch_id", i.VSwitchId).
		WithString("name", i.VSwitchName).
		WithString("cidr_block", i.CidrBlock).
		WithString("network_acl_id", i.NetworkAclId).
		WithString("desc", i.Description).
		WithString("status", i.Status).
		WithString(fields.CreateTimeFieldKey, i.CreationTime)

	attrs.Set("available_ip_address_count", *i.AvailableIpAddressCount)
	attrs.Set("is_default", i.IsDefault)

	return attrs
}
