package vpc

import (
	"time"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
)

func (s *Syncer) Clean(t int64, conds ...fields.Field) error {
	s.cleanOutdatedRecords(t, conds...)

	ago := time.Unix(t, 0).Add(-time.Hour * 4).Unix()
	return s.cleanFactoryOutdatedRecords(ago, conds...)
}

func (s *Syncer) cleanOutdatedRecords(t int64, conds ...fields.Field) error {
	conds = append(conds,
		fields.LessThan("update_at", t),
		fields.FactoryField(s.cred.Factory.GetID()),
		fields.FactoryAccountField(s.cred.Account.GetID()),
	)

	resp, err := restclient.ListAll[models.VPC](conds...)
	if err != nil {
		return err
	}

	for _, r := range resp.Results {
		if oerr := restclient.Delete(r); oerr != nil {
			err = oerr
		}
	}

	return err
}

func (s *Syncer) cleanFactoryOutdatedRecords(t int64, conds ...fields.Field) error {
	conds = append(conds,
		fields.LessThan("update_at", t),
		fields.FactoryField(s.cred.Factory.GetID()),
		// fields.FactoryAccountField(s.cred.Account.GetID()),
	)

	resp, err := restclient.ListAll[models.VPC](conds...)
	if err != nil {
		return err
	}

	for _, r := range resp.Results {
		if oerr := restclient.Delete(r); oerr != nil {
			err = oerr
		}
	}

	return err
}
