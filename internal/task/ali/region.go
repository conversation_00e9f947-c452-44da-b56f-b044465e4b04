package ali

import (
	"fmt"

	openapiv2 "github.com/alibabacloud-go/darabonba-openapi/v2/client"
	vpcv2 "github.com/alibabacloud-go/vpc-20160428/v2/client"
	"gitlab.lilithgame.com/yunwei/pkg/logger"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/util"
)

var DeprecatedRegions = []string{
	"ap-south-1",
	"ap-southeast-2",
}

func LoadRegions(cred FactoryCrendential) map[string]models.Region {
	cfg := *cred.ClientConfig
	cfg.SetRegionId("cn-hangzhou")

	cli, err := vpcv2.NewClient(&cfg)
	if err != nil {
		return nil
	}

	req := &vpcv2.DescribeRegionsRequest{}
	resp, err := cli.DescribeRegions(req)
	if err != nil {
		return nil
	}

	regions := restclient.LoadRegions(
		fields.StringField("factory__name", " 阿里云"),
		fields.Unlimited,
	)

	availableRegions := make(map[string]*vpcv2.DescribeRegionsResponseBodyRegionsRegion, 0)
	for _, r := range resp.Body.Regions.Region {
		availableRegions[*r.RegionId] = r
	}

	// drop regions that are not available
	for _, reg := range regions {
		if _, ok := availableRegions[reg.RegionID]; !ok {
			delete(regions, reg.RegionID)
		}
	}

	return regions
}

func RegionTask(conf *openapiv2.Config, ALI_REG_MAP_FROM_CMDB map[string]int) []*vpcv2.DescribeRegionsResponseBodyRegionsRegion {
	conf.Endpoint = fields.Pointer("vpc.aliyuncs.com")
	client, _ := vpcv2.NewClient(conf)
	describeRegionsRequest := &vpcv2.DescribeRegionsRequest{}
	regions, _err := client.DescribeRegions(describeRegionsRequest)
	if _err != nil {
		logger.Error(_err.Error())
		fmt.Println("client.DescribeRegions Error", _err)
		return []*vpcv2.DescribeRegionsResponseBodyRegionsRegion{}
	}
	regionList := regions.Body.Regions.Region
	regionLenthFromAPI := len(regionList)
	zoneLenthFromAPI := 0
	regionTotal := 0
	zoneTotal := 0
	for _, region := range regionList {
		var regionRecord models.Region
		hasRegionRecord, err := restclient.Find(&regionRecord,
			fields.NamedField("region_id", *region.RegionId),
			fields.NamedField("factory__name", "阿里云"),
		)
		if err != nil {
			logger.Errorf("Region: %s len(cmdbResultList) != 0/1, Check %s !", *region.RegionId, err.Error())
			continue
		}

		regionName := *region.LocalName
		if regionName == "" {
			regionName = *region.RegionId
		}

		attr := util.RegionAttrBuilder(*region.RegionId, regionName, regionRecord.Factory)
		if !hasRegionRecord {
			// 如果CMDB中不存在该region，则添加该region
			if _, err := restclient.Post[models.Region](attr); err != nil {
				logger.Error("record new region failed: ", "error", err, "region", attr)
			}
			regionTotal++

			var newRegion models.Region
			recorded, err := restclient.Find(&newRegion,
				fields.NamedField("region_id", *region.RegionId),
				fields.NamedField("factory__name", "阿里云"),
			)
			if err == nil && recorded {
				ALI_REG_MAP_FROM_CMDB[*region.RegionId] = newRegion.GetID()
			} else {
				logger.Error("record new region failed: ", "error", err, "region", attr, "has", recorded)
			}

			// newRegion, _ := oma.QueryWithKeyArgs("region",
			// 	fields.NamedField("region_id", *region.RegionId),
			// 	fields.NamedField("factory__name", "阿里云"),
			// )
			// cmdbId := newRegion[0].(map[string]interface{})["id"].(float64)
			// ALI_REG_MAP_FROM_CMDB[*region.RegionId] = cmdbId

		} else {
			restclient.PatchByID[models.Region](regionRecord.GetID(), attr)
			regionTotal++
		}

		describeZonesRequest := &vpcv2.DescribeZonesRequest{}
		describeZonesRequest.RegionId = region.RegionId
		zones, _err := client.DescribeZones(describeZonesRequest)
		if _err != nil {
			logger.Error(_err.Error())
			fmt.Println("client.DescribeZones Error", _err)
			return []*vpcv2.DescribeRegionsResponseBodyRegionsRegion{}
		}
		zoneList := zones.Body.Zones.Zone
		zoneLenthFromAPI += len(zoneList)
		for _, zone := range zoneList {
			var zoneRecord models.Zone
			hasRecord, err := restclient.Find(&zoneRecord,
				fields.NamedField("zone_id", *zone.ZoneId),
				fields.NamedField("factory__name", "阿里云"),
			)
			if err != nil {
				logger.Warnf("Zone: %s len(cmdbResultList) != 0/1, Check %s!", *zone.ZoneId, err.Error())
				continue
			}

			attr := util.ZoneAttrBuilder(ALI_REG_MAP_FROM_CMDB[*region.RegionId], *zone.ZoneId, *zone.LocalName, "", "", 1)
			if !hasRecord {
				// 如果CMDB中不存在该zone，则添加该zone
				restclient.Post[models.Zone](attr)
			} else {
				restclient.PatchByID[models.Zone](zoneRecord.GetID(), attr)
			}
			zoneTotal++
		}
	}
	fmt.Println("Aliyun Region total from API: ", regionLenthFromAPI)
	fmt.Println("Aliyun Region total from OMA: ", regionTotal)
	fmt.Println("Aliyun Zone total from API: ", zoneLenthFromAPI)
	fmt.Println("Aliyun Zone total from OMA: ", zoneTotal)
	return regionList
}
