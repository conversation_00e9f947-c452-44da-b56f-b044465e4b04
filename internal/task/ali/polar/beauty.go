package polar

import (
	polarv3 "github.com/alibabacloud-go/polardb-20170801/v3/client"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/category"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
)

func BeautyWith(p *polarv3.DescribeDBClustersResponseBodyItemsDBCluster, fds ...fields.Field) fields.Fields {
	return Beauty(p).With(fds...)
}

func Beauty(p *polarv3.DescribeDBClustersResponseBodyItemsDBCluster) fields.Fields {
	attr := fields.NewFields(
		fields.NamedPField(fields.ExternalUUIDFieldKey, p.DBClusterId),
		fields.StringPField(fields.ExternalNameFieldKey, p.DBClusterDescription),
		fields.ExternalStatusField(fields.PString(p.DBClusterStatus)),
		fields.StringPField(fields.CreateTimeFieldKey, p.CreateTime),
		fields.StringPField("version", p.DBVersion),
		fields.StringPField("engine", p.Engine),
		fields.StringPField("cluster_type", p.Category),
		fields.StringPField("db_type", p.DBType),
		fields.StringPField("desc", p.DBClusterDescription),
	)

	// product
	if productId := category.Category(fields.PString(p.DBClusterDescription), ""); productId != nil {
		attr.Set(fields.ProductFieldKey, *productId)
	}

	return attr
}
