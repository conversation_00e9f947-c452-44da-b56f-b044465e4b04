package polar

import (
	"time"

	polarv3 "github.com/alibabacloud-go/polardb-20170801/v3/client"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
)

func (s *Syncer) SyncWhitelistAndSecurityGroups(cli *polarv3.Client, clusterID string) error {
	request := &polarv3.DescribeDBClusterAccessWhitelistRequest{
		DBClusterId: fields.Pointer(clusterID),
	}

	resp, err := cli.DescribeDBClusterAccessWhitelist(request)
	if err != nil {
		return err
	}

	startAt := time.Now().Unix()
	for _, g := range resp.Body.Items.DBClusterIPArray {
		attrs := fields.Fields{
			"group_name": fields.Value(g.DBClusterIPArrayName),
			"ip_list":    fields.Value(g.SecurityIps),
			"db":         clusterID,
		}

		conds := fields.FieldList(
			*attrs.GetField("db"),
			*attrs.GetField("group_name"),
		)

		if _, perr := restclient.PostOrPatch[models.PolarDBWhitelist](conds, attrs); perr != nil {
			err = perr
		}
	}

	// clean whitelist
	if err == nil {
		restclient.DeleteSubResource[models.Polar](
			"whitelist",
			clusterID,
			fields.NumberField("updated_before", startAt),
		)
	}

	// sync security group
	security_groups := make([]string, 0)
	for _, sg := range resp.Body.DBClusterSecurityGroups.DBClusterSecurityGroup {
		security_groups = append(security_groups, *sg.SecurityGroupId)
	}
	restclient.PostSubResource[models.Polar]("security_groups", clusterID, fields.Fields{
		"security_groups": security_groups,
	})

	return err
}
