package polar

import (
	polarv3 "github.com/alibabacloud-go/polardb-20170801/v3/client"
	"github.com/fatih/color"
	"gitlab.lilithgame.com/yunwei/pkg/logger"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/factory"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
	alitask "gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/task/ali"
)

type Syncer struct {
	cred    *alitask.FactoryCrendential
	regions map[string]models.Region
}

func NewSyncer(factoryKey factory.FactoryKeyType) (*Syncer, error) {
	cred, err := alitask.CreateFactoryCredential(factoryKey.String())
	if err != nil {
		return nil, err
	}

	return &Syncer{
		cred: cred,
		regions: restclient.LoadRegions(
			fields.StringField("factory__name", " 阿里云"),
			fields.Unlimited,
		),
	}, nil
}

func (s *Syncer) SyncAll() error {
	var err error

	availabelRegions, err := s.AvailableRegions()
	if err != nil {
		return err
	}

	for _, region := range availabelRegions {
		if serr := s.SyncInRegion(region); serr != nil {
			err = serr
			logger.Errorf("sync polardb failed, %v", serr)
		}
	}
	return err
}

func (s *Syncer) AvailableRegions() ([]string, error) {
	cfg := *s.cred.ClientConfig
	cfg.SetRegionId("cn-shanghai")

	regions := make([]string, 0)

	cli, err := polarv3.NewClient(&cfg)
	if err != nil {
		return nil, err
	}

	resp, err := cli.DescribeRegions(&polarv3.DescribeRegionsRequest{})
	if err != nil {
		return nil, err
	}

	for _, reg := range resp.Body.Regions.Region {
		regions = append(regions, *reg.RegionId)
	}

	return regions, nil
}

func (s *Syncer) SyncInRegion(region string) error {
	color.Blue("sync in %s", region)
	cli, err := s.newRegionClient(region)
	if err != nil {
		return err
	}

	input := &polarv3.DescribeDBClustersRequest{
		RegionId: &region,
		PageSize: fields.Int32(100),
	}

	for n := int32(1); ; n++ {
		input.SetPageNumber(n)
		resp, derr := cli.DescribeDBClusters(input)
		if derr != nil {
			return derr
		}
		for _, cluster := range resp.Body.Items.DBCluster {
			attr := Beauty(cluster).With(
				fields.FactoryField(s.cred.Factory.GetID()),
				fields.FactoryAccountField(s.cred.Account.GetID()),
			)

			// region
			if region, found := s.regions[fields.PString(cluster.RegionId)]; found {
				attr.SetField(fields.RegionField(region.GetID()))
			}

			conds := fields.FieldList(*attr.GetField(fields.ExternalUUIDFieldKey))

			if _, perr := restclient.PostOrPatch[models.Polar](conds, attr); perr != nil {
				err = perr
				color.Red("sync polardb failed, %v", perr)
			} else {
				color.Green("sync polardb success, %v", *cluster.DBClusterId)

				s.SyncWhitelistAndSecurityGroups(cli, *cluster.DBClusterId)
			}
		}

		if len(resp.Body.Items.DBCluster) < int(*input.PageSize) {
			break
		}
	}

	return err
}

func (s *Syncer) newRegionClient(region string) (*polarv3.Client, error) {
	cfg := *s.cred.ClientConfig
	cfg.SetRegionId(region)
	cfg.SetEndpoint("polardb.aliyuncs.com")

	cli, err := polarv3.NewClient(&cfg)
	if err != nil {
		return nil, err
	}
	return cli, nil
}
