package sg

import (
	ecsv3 "github.com/alibabacloud-go/ecs-20140526/v3/client"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
)

func Beauty(i *ecsv3.DescribeSecurityGroupsResponseBodySecurityGroupsSecurityGroup) fields.Fields {
	attrs := fields.NewFields(
		fields.StringPField("security_id", i.SecurityGroupId),
		fields.StringPField("name", i.SecurityGroupName),
		fields.StringPField(fields.DescFieldKey, i.Description),
		fields.NumberPField("available_amount", i.AvailableInstanceAmount),
		fields.NumberPField("ecs_count", i.EcsCount),

		fields.StringPField("group_type", i.SecurityGroupType),
		//  是否为自定义安全组 或 托管安全组
		fields.NamedPField("service_managed", i.ServiceManaged),

		fields.CreateTimeField(*i.CreationTime),
	)

	return attrs
}

func BeautyRule(i *ecsv3.DescribeSecurityGroupAttributeResponseBodyPermissionsPermission) fields.Fields {
	attrs := fields.NewFields(
		fields.StringPField("rule_id", i.SecurityGroupRuleId),
		fields.StringPField("direction", i.Direction),
		fields.UpperStringField("policy", fields.Value(i.Policy)),
		fields.UpperStringField("protocol", fields.Value(i.IpProtocol)),
		fields.StringPField("priority", i.Priority),

		fields.StringPField("ip", i.SourceCidrIp),
		fields.StringPField("port", i.SourcePortRange),

		fields.StringPField("dest_ip", i.DestCidrIp),
		fields.StringPField("dest_port", i.PortRange),

		fields.StringPField("src_sg", i.SourceGroupId),
		fields.StringPField("dest_sg", i.DestGroupId),

		fields.StringPField("description", i.Description),

		fields.StringPField(fields.CreateTimeFieldKey, i.CreateTime),
	)

	return attrs
}
