package sg

import (
	"errors"
	"fmt"
	"log/slog"
	"time"

	ecsv3 "github.com/alibabacloud-go/ecs-20140526/v3/client"
	"github.com/fatih/color"
	"gitlab.lilithgame.com/yunwei/pkg/logger"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/cache"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
	task "gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/task/ali"
)

type Syncer struct {
	cred    *task.FactoryCrendential
	regions map[string]regionInfo
	log     *slog.Logger
}

func NewSyncer(accountKey string) (*Syncer, error) {
	cred, err := task.CreateFactoryCredential(accountKey)
	if err != nil {
		return nil, err
	}

	log := logger.Slog().With("factory", cred.Factory.KeyName, "account", cred.Account.KeyName)

	s := &Syncer{cred: cred, log: log}
	completeAvailableRegions(s)

	return s, nil
}

func (s *Syncer) Regions() map[string]regionInfo {
	return s.regions
}

func Sync(accout string) error {
	s, err := NewSyncer(accout)
	if err != nil {
		return err
	}

	t := time.Now().Unix()
	if err = s.Sync(); err == nil {
		return s.Clean(t)
	}
	return nil
}

func (s *Syncer) Sync() error {
	if len(s.regions) == 0 {
		return errors.New("no region found")
	}

	var err error
	for region := range s.regions {
		color.Yellow("sync security group with region: %s", region)
		if serr := s.SyncInRegion(region); serr != nil {
			err = serr
		}
	}

	return err
}

// func (s *Syncer) AvailableRegions() map[string]string {
// 	regiosn := make(map[string]string)

// 	cli, err := CreateClient(s.cred.ClientConfig)
// 	if err != nil {
// 		return nil
// 	}

// 	resp, err := cli.DescribeRegions(&vpcv6.DescribeRegionsRequest{})
// 	if err != nil {
// 		return nil
// 	}

// 	for _, r := range resp.Body.Regions.Region {
// 		regiosn[*r.RegionId] = *r.LocalName
// 	}

// 	return regiosn
// }

func (s *Syncer) SyncInRegion(region string) error {
	regionInfo, found := s.regions[region]
	if !found {
		return fmt.Errorf("region %s not found", region)
	}

	cfg := *s.cred.ClientConfig
	cfg.SetEndpoint(regionInfo.Endpoint)
	cfg.SetRegionId(region)

	cli, err := CreateClient(&cfg)
	if err != nil {
		return err
	}

	vpcer := cache.VPCCacher()

	req := &ecsv3.DescribeSecurityGroupsRequest{
		RegionId:   &region,
		MaxResults: fields.Int32(100),
		// IsQueryEcsCount: fields.Pointer(true),
	}

	for token, starts := "", false; token != "" || !starts; starts = true {
		if token != "" {
			req.SetNextToken(token)
		}

		resp, derr := cli.DescribeSecurityGroups(req)
		if derr != nil {
			err = derr
			s.log.Error("describe sucurity group failed", "error", derr)
			break
		}
		token = fields.Value(resp.Body.NextToken)

		for _, g := range resp.Body.SecurityGroups.SecurityGroup {
			attr := Beauty(g).With(
				fields.FactoryField(s.cred.Factory.GetID()),
				fields.FactoryAccountField(s.cred.Account.GetID()),
			)
			if vpc, err := vpcer(fields.PString(g.VpcId)); err == nil {
				attr.SetField(fields.VpcField(vpc.GetID()))
				if regionID := vpc.Region; regionID != nil {
					attr.SetField(fields.RegionField(*regionID))
				}
			} else {
				color.Yellow("no releated vpc %s for security group %s", *g.VpcId, *g.SecurityGroupId)
			}

			cond := []fields.Field{
				*attr.GetField("security_id"),
			}

			if sg, perr := restclient.PostOrPatch[models.Security](cond, attr); perr == nil {
				ruleSyncStart := time.Now().Unix()
				if serr := s.SyncSecurityGroupRuleInRegion(cli, sg.SecurityID, sg.GetID(), region); serr != nil {
					err = serr
				} else {
					restclient.DeleteSubResource[models.Security]("rules", sg.SecurityID, fields.NamedField(
						"updated_before", ruleSyncStart,
					))
				}
			}
		}
	}

	return err
}

func (s *Syncer) SyncSecurityGroupRuleInRegion(cli *ecsv3.Client, sg string, sgID int, region string) error {
	req := &ecsv3.DescribeSecurityGroupAttributeRequest{
		RegionId:        &region,
		SecurityGroupId: fields.Pointer(sg),
	}

	resp, err := cli.DescribeSecurityGroupAttribute(req)
	if err != nil {
		return err
	}

	for _, rule := range resp.Body.Permissions.Permission {
		attr := BeautyRule(rule).With(
			fields.NamedField("security", sgID),
		)
		conds := []fields.Field{
			fields.StringPField("rule_id", rule.SecurityGroupRuleId),
			// fields.NamedField("security", sgID),
		}

		if _, perr := restclient.PostOrPatch[models.Rule](conds, attr); perr != nil {
			err = perr
		}

	}

	return err
}
