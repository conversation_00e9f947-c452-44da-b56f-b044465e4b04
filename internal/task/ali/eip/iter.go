package eip

import (
	"errors"
	"fmt"

	vpcv6 "github.com/alibabacloud-go/vpc-20160428/v6/client"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/util"
)

var (
	ErrNoMore           = errors.New("no more")
	ErrIterationStopped = errors.New("iterator is stopped")
)

type EipIterator struct {
	cli *vpcv6.Client

	Region string

	Total       int32
	TotalPages  int32
	CurrentPage int32
	PageSize    int32

	currentPage chan *vpcv6.DescribeEipAddressesResponseBodyEipAddressesEipAddress
}

func NewEipIterator(cli *vpcv6.Client, region string, pageSize int32) *EipIterator {
	cli.RegionId = fields.Pointer(region)
	return &EipIterator{
		cli:         cli,
		Region:      region,
		PageSize:    util.NumberRanger(int32(10), 100)(pageSize),
		currentPage: make(chan *vpcv6.DescribeEipAddressesResponseBodyEipAddressesEipAddress, pageSize),
	}
}

func (it *EipIterator) Close() {
	close(it.currentPage)
}

func (it *EipIterator) resetCounts() {
	it.Total = 0
	it.TotalPages = 0
	it.CurrentPage = 0
}

func (it *EipIterator) Next() (*vpcv6.DescribeEipAddressesResponseBodyEipAddressesEipAddress, error) {
	select {
	case d, ok := <-it.currentPage:
		if !ok {
			return nil, ErrIterationStopped
		}

		return d, nil

	default:
		nextPage, err := it.NextPage(it.CurrentPage+1, it.Region)
		if err != nil {
			return nil, err
		}

		it.Total = nextPage.Total
		it.CurrentPage = nextPage.CurrentPage
		it.TotalPages = nextPage.TotalPages

		fmt.Printf("%s => total: %d, page: %d/%d\n", it.Region, it.Total, it.CurrentPage, it.TotalPages)

		for _, d := range nextPage.Eips {
			it.currentPage <- d
		}

		if len(nextPage.Eips) > 0 {
			return <-it.currentPage, nil
		}

		it.resetCounts()

		return nil, ErrNoMore
	}
}

type Page struct {
	Total       int32
	TotalPages  int32
	CurrentPage int32
	PageSize    int32
	Eips        []*vpcv6.DescribeEipAddressesResponseBodyEipAddressesEipAddress
}

func (it *EipIterator) NextPage(page int32, region string) (*Page, error) {
	resp, err := it.cli.DescribeEipAddresses(&vpcv6.DescribeEipAddressesRequest{
		RegionId:               fields.Pointer(region),
		PageSize:               fields.Int32(it.PageSize),
		PageNumber:             fields.Int32(page),
		IncludeReservationData: fields.Pointer(true),
	})
	if err != nil {
		return nil, err
	}

	if len(resp.Body.EipAddresses.EipAddress) == 0 {
		return nil, ErrNoMore
	}

	eips := resp.Body.EipAddresses.EipAddress[:]

	total := fields.Value(resp.Body.TotalCount)
	pageSize := fields.Value(resp.Body.PageSize)

	totalPages := total / pageSize
	if total%pageSize > 0 {
		totalPages++
	}

	return &Page{
		Total:       total,
		TotalPages:  totalPages,
		PageSize:    fields.Value(resp.Body.PageSize),
		CurrentPage: fields.Value(resp.Body.PageNumber),
		Eips:        eips,
	}, nil
}
