package eip

import (
	aeipv2 "github.com/alibabacloud-go/eipanycast-20200309/v2/client"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/util"
)

type AEipIterator struct {
	cli *aeipv2.Client

	Total      int32
	TotalPages int32
	PageSize   int32

	currentPage chan *aeipv2.ListAnycastEipAddressesResponseBodyAnycastList
	nextToken   *string
	started     bool
}

func NewAnyCastEIPIterator(cli *aeipv2.Client, pageSize int32) *AEipIterator {
	return &AEipIterator{
		cli:         cli,
		PageSize:    util.NumberRanger(int32(20), 100)(pageSize),
		currentPage: make(chan *aeipv2.ListAnycastEipAddressesResponseBodyAnycastList, pageSize),
	}
}

func (it *AEipIterator) Close() {
	close(it.currentPage)
}

func (it *AEipIterator) resetCounts() {
	it.Total = 0
	it.TotalPages = 0
}

func (it *AEipIterator) Next() (*aeipv2.ListAnycastEipAddressesResponseBodyAnycastList, error) {
	select {
	case d, ok := <-it.currentPage:
		if !ok {
			return nil, ErrIterationStopped
		}

		return d, nil

	default:
		nextPage, err := it.NextPage()
		if err != nil {
			return nil, err
		}

		it.started = true
		it.Total = nextPage.Total
		it.TotalPages = nextPage.TotalPages

		for _, d := range nextPage.AEips {
			it.currentPage <- d
		}

		if len(nextPage.AEips) > 0 {
			return <-it.currentPage, nil
		}

		it.resetCounts()

		return nil, ErrNoMore
	}
}

type AEIPPage struct {
	Total      int32
	TotalPages int32
	PageSize   int32
	AEips      []*aeipv2.ListAnycastEipAddressesResponseBodyAnycastList
}

func (it *AEipIterator) NextPage() (*AEIPPage, error) {
	if it.started && it.nextToken == nil {
		return nil, ErrNoMore
	}

	request := &aeipv2.ListAnycastEipAddressesRequest{
		MaxResults: &it.PageSize,
	}

	if it.nextToken != nil {
		request.NextToken = it.nextToken
	}

	resp, err := it.cli.ListAnycastEipAddresses(request)
	if err != nil {
		return nil, err
	}

	if len(resp.Body.AnycastList) == 0 {
		return nil, ErrNoMore
	}

	if nextToken := resp.Body.NextToken; nextToken != nil {
		it.nextToken = nextToken
	}

	aeips := resp.Body.AnycastList[:]

	total := fields.Value(resp.Body.TotalCount)
	pageSize := it.PageSize
	totalPages := total / pageSize
	if total%pageSize > 0 {
		totalPages++
	}

	return &AEIPPage{
		Total:      total,
		TotalPages: totalPages,
		PageSize:   pageSize,
		AEips:      aeips,
	}, nil
}
