package eip

import (
	openapiv2 "github.com/alibabacloud-go/darabonba-openapi/v2/client"
	aeipv2 "github.com/alibabacloud-go/eipanycast-20200309/v2/client"
	vpcv6 "github.com/alibabacloud-go/vpc-20160428/v6/client"
)

func CreateClient(cfg *openapiv2.Config) (*vpcv6.Client, error) {
	cfg.SetEndpoint("vpc.aliyuncs.com")

	client, err := vpcv6.NewClient(cfg)
	if err != nil {
		return nil, err
	}
	return client, nil
}

func CreateEIPAnyCastClient(cfg *openapiv2.Config) (*aeipv2.Client, error) {
	if cfg.RegionId == nil {
		cfg.SetRegionId("cn-shanghai")
	}

	cfg.SetEndpoint("eipanycast.cn-hangzhou.aliyuncs.com")

	client, err := aeipv2.NewClient(cfg)
	if err != nil {
		return nil, err
	}
	return client, nil
}
