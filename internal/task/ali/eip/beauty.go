package eip

import (
	aeipv2 "github.com/alibabacloud-go/eipanycast-20200309/v2/client"
	vpcv6 "github.com/alibabacloud-go/vpc-20160428/v6/client"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/category"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
)

func BeautyEIPAddress(i *vpcv6.DescribeEipAddressesResponseBodyEipAddressesEipAddress) fields.Fields {
	attrs := fields.NewFields(
		fields.StringField("version", "eip"),

		fields.StringPField("name", i.Name),
		fields.StringPField("state", i.Status),

		fields.ExternalStatusField(fields.Value(i.Status)),
		fields.StringPField(fields.PublicIPFieldKey, i.IpAddress),
		fields.StringPField(fields.ExternalUUIDFieldKey, i.AllocationId),

		fields.StringPField("bind_id", i.InstanceId),
		fields.StringPField("bind_type", i.InstanceType),
		fields.StringPField("isp", i.ISP),

		fields.StringPField(fields.CreateTimeFieldKey, i.AllocationTime),
	)

	if bindId := fields.Value(i.InstanceId); bindId != "" {
		attrs.With(
			fields.StringField("last_bind_id", bindId),
			fields.StringPField("last_bind_type", i.InstanceType),
		)
	}

	if productId := category.Category(*i.Name, ""); productId != nil {
		attrs.Set("product", productId)
	}

	return attrs
}

func BeautyAEip(i *aeipv2.ListAnycastEipAddressesResponseBodyAnycastList) fields.Fields {
	attrs := fields.NewFields(
		fields.StringField("version", "anycast"),

		fields.StringPField("name", i.Name),
		fields.StringPField("state", i.Status),

		fields.ExternalStatusField(fields.Value(i.Status)),
		fields.StringPField(fields.PublicIPFieldKey, i.IpAddress),
		fields.StringPField(fields.ExternalUUIDFieldKey, i.AnycastId),

		fields.StringPField(fields.CreateTimeFieldKey, i.CreateTime),
	)

	for _, bind := range i.AnycastEipBindInfoList {
		attrs.With(
			fields.StringPField("bind_id", bind.BindInstanceId),
			fields.StringPField("bind_type", bind.BindInstanceType),
		)
		break
	}

	if bindId := attrs.GetString("bind_id"); bindId != "" {
		attrs.With(
			fields.StringField("last_bind_id", bindId),
			fields.StringField("last_bind_type", attrs.GetString("bind_type")),
		)
	}

	if name := fields.Value(i.Name); name != "" {
		if productId := category.Category(name, ""); productId != nil {
			attrs.Set("product", productId)
		}
	}

	return attrs
}

func BeautyEIPWith(i *vpcv6.DescribeEipAddressesResponseBodyEipAddressesEipAddress, additions ...fields.Field) fields.Fields {
	return BeautyEIPAddress(i).With(additions...)
}

func BeautyAnyCastEIPWith(i *aeipv2.ListAnycastEipAddressesResponseBodyAnycastList, additions ...fields.Field) fields.Fields {
	return BeautyAEip(i).With(additions...)
}
