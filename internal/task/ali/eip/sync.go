package eip

import (
	"errors"
	"fmt"
	"slices"
	"time"

	"gitlab.lilithgame.com/yunwei/pkg/logger"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/category"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/factory"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
	alitask "gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/task/ali"
)

type Syncer struct {
	cred    *alitask.FactoryCrendential
	regions map[string]models.Region
}

func NewSyncer(accountKey string) (*Syncer, error) {
	cred, err := alitask.CreateFactoryCredential(accountKey)
	if err != nil {
		return nil, err
	}

	return &Syncer{
		cred: cred,
		regions: restclient.LoadRegions(
			fields.NamedField("factory__name", "阿里云"),
			fields.Unlimited,
		),
	}, nil
}

func (s *Syncer) Sync() error {
	var err error

	t := time.Now().Local().Unix()

	logger.Debug("starting sync eip", "factory", s.cred.Factory.KeyName)
	if serr := s.SyncEIP(); serr != nil {
		err = serr
	}

	logger.Debug("starting sync anycast eip", "factory", s.cred.Factory.KeyName)
	if serr := s.SyncAnyCastIP(); serr != nil {
		err = serr
	}

	if err != nil {
		return err
	}

	logger.Info("sync eip done", "factory", s.cred.Factory.KeyName, "cost", time.Now().Local().Unix()-t)

	return s.Clean(t)
}

func (s *Syncer) SyncEIP() error {
	if len(s.regions) == 0 {
		return errors.New("no region found")
	}

	cli, err := CreateClient(s.cred.ClientConfig)
	if err != nil {
		return fmt.Errorf("create client failed: %w", err)
	}

	for region := range s.regions {
		it := NewEipIterator(cli, region, 100)
		defer it.Close()

		for eipAddr, ierr := it.Next(); ierr == nil && eipAddr != nil; eipAddr, ierr = it.Next() {
			attrs := BeautyEIPWith(eipAddr,
				fields.FactoryField(s.cred.Factory.GetID()),
				fields.FactoryAccountField(s.cred.Account.GetID()),
			)

			//  补充产品信息
			if productId := attrs.GetInt(fields.ProductFieldKey); productId == nil {
				productId = category.GetMatchedProductID(s.cred.Account.Channel)
				if productId != nil {
					attrs.Set(fields.ProductFieldKey, *productId)
				} else {
					// 如果是 EcsInstance 类型，就从 EcsInstance 表中获取产品信息
					bindType := attrs.GetString("bind_type")
					if slices.Index([]string{"EcsInstance", "SlbInstance"}, bindType) != -1 {
						// bindId := attrs.GetString("bind_id")

						switch bindId := attrs.GetString("bind_id"); bindType {
						case "EcsInstance":
							bindInstance := &models.Machine{}
							if has, err := restclient.Find(bindInstance, fields.ExternalUUIDField(bindId)); err == nil && has {
								if productId := bindInstance.Product; productId != nil {
									attrs.Set(fields.ProductFieldKey, *productId)
								}
							}

						case "SlbInstance":
							bindInstance := &models.SLB{}
							if has, err := restclient.Find(bindInstance, fields.ExternalUUIDField(bindId)); err == nil && has {
								if productId := bindInstance.Product; productId != nil {
									attrs.Set(fields.ProductFieldKey, *productId)
								}
							}
						}

						// if bindType == "EcsInstance" {
						// 	bindInstance := &models.Machine{}
						// 	if has, err := restclient.Find(bindInstance, fields.ExternalUUIDField(bindId)); err == nil && has {
						// 		if productId := bindInstance.Product; productId != nil {
						// 			attrs.Set(fields.ProductFieldKey, *productId)
						// 		}
						// 	}

						// } else if bindType == "SlbInstance" {
						// 	bindInstance := &models.SLB{}
						// 	if has, err := restclient.Find(bindInstance, fields.ExternalUUIDField(bindId)); err == nil && has {
						// 		if productId := bindInstance.Product; productId != nil {
						// 			attrs.Set(fields.ProductFieldKey, *productId)
						// 		}
						// 	}
						// }
					}
				}
			}

			conds := []fields.Field{
				fields.ExternalUUIDField(attrs.GetString(fields.ExternalUUIDFieldKey)),
			}

			if _, perr := restclient.PostOrPatch[models.ElasticIP](conds, attrs); perr != nil {
				logger.Error("failed to update or create eip record", "error", perr)
				err = perr
			}
		}
	}

	return err
}

func (s *Syncer) SyncAnyCastIP() error {
	cli, err := CreateEIPAnyCastClient(s.cred.ClientConfig)
	if err != nil {
		return fmt.Errorf("create anycast eip client failed: %w", err)
	}

	it := NewAnyCastEIPIterator(cli, 100)
	defer it.Close()

	logger.Debug("sync anycast eip", "factory", s.cred.Factory.KeyName)

	var serr error

	for aeipAddr, err := it.Next(); err == nil && aeipAddr != nil; aeipAddr, err = it.Next() {
		attrs := BeautyAnyCastEIPWith(aeipAddr,
			fields.FactoryField(s.cred.Factory.GetID()),
			fields.FactoryAccountField(s.cred.Account.GetID()),
		)

		//  补充产品信息
		if productId := attrs.GetInt(fields.ProductFieldKey); productId == nil {
			productId = category.GetMatchedProductID(s.cred.Account.Channel)
			if productId != nil {
				attrs.Set(fields.ProductFieldKey, *productId)
			} else {
				// 如果是 EcsInstance 类型，就从 EcsInstance 表中获取产品信息
				bindType := attrs.GetString("bind_type")
				if slices.Index([]string{"SlbInstance"}, bindType) == -1 {
					break
				}

				if bindType == "SlbInstance" {
					bindInstance := &models.SLB{}
					bindId := attrs.GetString("bind_id")
					if has, err := restclient.Find(bindInstance, fields.ExternalUUIDField(bindId)); err == nil && has {
						if productId := bindInstance.Product; productId != nil {
							attrs.Set(fields.ProductFieldKey, *productId)
						}
					}
				}

			}
		}

		conds := []fields.Field{
			fields.ExternalUUIDField(attrs.GetString(fields.ExternalUUIDFieldKey)),
		}

		if _, perr := restclient.PostOrPatch[models.ElasticIP](conds, attrs); perr != nil {
			serr = perr
		}
	}

	return serr
}

func Sync() error {
	factories := make([]string, 0)
	for _, f := range factory.AliyunFactories {
		factories = append(factories, f.String())
	}

	return SyncMany(factories...)
}

func SyncMany(factories ...string) error {
	syncers := make([]*Syncer, 0)

	for _, f := range factories {
		s, err := NewSyncer(f)
		if err != nil {
			logger.Errorf("create syncer failed: %v", err)
			continue
		}

		syncers = append(syncers, s)
	}

	// if len(syncers) == 0 {
	// 	return errors.New("no syncer found")
	// }

	var err error
	for _, s := range syncers {
		if serr := s.Sync(); serr != nil {
			logger.Errorf("sync failed: %v", err)
			err = serr
		}
	}

	return err
}
