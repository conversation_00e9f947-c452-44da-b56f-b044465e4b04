package eip

import (
	"gitlab.lilithgame.com/yunwei/pkg/logger"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
)

func (s *Syncer) Clean(t int64, conds ...fields.Field) error {
	conds = append(conds,
		fields.LessThan("update_at", t),
		fields.FactoryField(s.cred.Factory.GetID()),
		fields.FactoryAccountField(s.cred.Account.GetID()),
	)

	resp, err := restclient.ListAll[models.ElasticIP](conds...)
	if err != nil {
		return err
	}

	for _, r := range resp.Results {
		if oerr := restclient.Delete(r); oerr != nil {
			err = oerr
		}
		logger.Printf("remove %d(%s, version: %s) %v\n", r.GetID(), r.PublicIP, r.Version, err)
	}

	logger.Debugf("remove %d eip records update before %d", resp.Count, t)

	return err
}
