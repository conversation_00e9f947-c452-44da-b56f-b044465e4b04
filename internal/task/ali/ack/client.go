package ack

import (
	"errors"
	"slices"

	csv5 "github.com/alibabacloud-go/cs-20151215/v5/client"
	openapiv2 "github.com/alibabacloud-go/darabonba-openapi/v2/client"
)

var invalidRegions = []string{
	"ap-south-1",
	"ap-southeast-2",
}

func CreateClient(config *openapiv2.Config, region string) (*csv5.Client, error) {
	if slices.Contains(invalidRegions, region) {
		return nil, errors.New("region not supported")
	}

	cfg := *config
	cfg.SetRegionId(region)

	cli, err := csv5.NewClient(&cfg)
	if err != nil {
		return nil, err
	}

	return cli, nil
}
