package ack

import (
	csv5 "github.com/alibabacloud-go/cs-20151215/v5/client"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/category"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
)

func Beauty(i *csv5.DescribeClustersV1ResponseBodyClusters) fields.Fields {
	attrs := fields.NewFields(
		fields.StringPField("cluster_id", i.ClusterId),
		fields.StringPField("cluster_name", i.Name),
		fields.StringPField("cluster_version", i.CurrentVersion),
		fields.CreateTimeField(*i.Created),
		fields.RegionIDField(*i.RegionId),
		// fields.ZoneIDField(*cluster.ZoneId),
	)

	// product
	if product := category.Category(*i.Name, ""); product != nil {
		attrs.Set("product", *product)
	}

	return attrs
}

func BeautyNodePool(i *csv5.DescribeClusterNodePoolsResponseBodyNodepools) fields.Fields {
	attrs := fields.NewFields(
		fields.StringPField("pool_id", i.NodepoolInfo.NodepoolId),
		fields.StringPField("pool_name", i.NodepoolInfo.Name),
		fields.CreateTimeField(*i.NodepoolInfo.Created),
		fields.RegionIDField(*i.NodepoolInfo.RegionId),
	)

	// product
	if product := category.Category(*i.NodepoolInfo.Name, ""); product != nil {
		attrs.Set("product", *product)
	}

	return attrs
}
