package ack

import (
	"fmt"
	"slices"
	"time"

	csv5 "github.com/alibabacloud-go/cs-20151215/v5/client"
	"github.com/fatih/color"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/category"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
)

func (s *Syncer) Sync() error {
	var serr error

	for _, region := range s.regions {
		s.log.Debug("sync region info", "region", region.RegionID)
		if err := s.SyncWithRegion(region.RegionID); err != nil {
			s.log.Warn("sync region error", "region", region.RegionID, "error", err)
			serr = err
		}
	}

	return serr
}

func (s *Syncer) SyncWithRegion(region string) error {
	if slices.Contains(invalidRegions, region) {
		return nil
	}

	cli, err := CreateClient(s.cred.ClientConfig, region)
	if err != nil {
		return err
	}

	factoryAttrs := fields.FieldList(
		fields.FactoryField(s.cred.Factory.GetID()),
		fields.FactoryAccountField(s.cred.Account.GetID()),
	)

	input := &csv5.DescribeClustersV1Request{
		RegionId: fields.Pointer(region),
		PageSize: fields.Int64(50),
	}

	for page := int64(1); ; page++ {
		input.SetPageNumber(page)

		resp, err := cli.DescribeClustersV1(input)
		if err != nil {
			return err
		}

		for _, cluster := range resp.Body.Clusters {
			fmt.Printf("%s %s(kubernetes %s, nodes: %d)\n", *cluster.ClusterId, *cluster.Name, *cluster.CurrentVersion, *cluster.Size)

			attrs := Beauty(cluster).With(factoryAttrs...)

			// 补充产品信息(如果无法通过名字解析)
			if productId := attrs.GetInt(fields.ProductFieldKey); productId == nil {
				if productId = category.GetMatchedProductID(s.cred.Account.Channel); productId != nil {
					attrs.Set(fields.ProductFieldKey, *productId)
				}
			}

			conds := fields.FieldList(
				*attrs.GetField("cluster_id"),
			)

			// sync cluster
			if k, perr := restclient.PostOrPatch[models.K8SCluster](conds, attrs); perr == nil {
				startAt := time.Now().Unix()

				// sync node pool && nodes underneth
				if sserr := s.SyncClusterNodePools(cli, *cluster.ClusterId); sserr != nil {
					s.log.Error("sync cluster node pool failed", "cluster", *cluster.ClusterId, "error", sserr)
				} else {
					//  清理 clusterID 下的过期 node
					restclient.DeleteSubResource[models.K8SCluster](
						"nodes",
						k.GetID(),
						fields.NumberField("updated_before", startAt),
					)
					color.Green("sync cluster %s node pool ok", *cluster.ClusterId)
				}
			}
		}

		if len(resp.Body.Clusters) < int(*input.PageSize) {
			break
		}
	}

	return nil
}

func (s *Syncer) SyncClusterNodePools(cli *csv5.Client, clusterID string) (err error) {
	req := &csv5.DescribeClusterNodePoolsRequest{}

	resp, err := cli.DescribeClusterNodePools(fields.Pointer(clusterID), req)
	if err != nil {
		return err
	}

	for _, pool := range resp.Body.Nodepools {
		s.syncNodePool(pool, fields.StringField("cluster", clusterID))
		s.SyncClusterNodesInPool(cli, clusterID, *pool.NodepoolInfo.NodepoolId)
	}

	return nil
}

func (s *Syncer) SyncClusterNodesInPool(cli *csv5.Client, clusterID string, poolID string) (err error) {
	const PageSize = 100

	req := &csv5.DescribeClusterNodesRequest{
		PageSize:   fields.Pointer(fields.IntString(PageSize)),
		NodepoolId: fields.Pointer(poolID),
	}

	for page := 1; ; page++ {
		req.SetPageNumber(fields.IntString(page))

		nodeResp, err := cli.DescribeClusterNodes(fields.Pointer(clusterID), req)
		if err != nil {
			return err
		}

		for _, node := range nodeResp.Body.Nodes {
			if !fields.Value(node.IsAliyunNode) {
				continue
			}

			s.syncNode(node,
				fields.StringField("cluster", clusterID),
				fields.StringField("pool", poolID),
			)
		}

		if len(nodeResp.Body.Nodes) < PageSize {
			break
		}
	}

	return nil
}

func (s *Syncer) syncNode(node *csv5.DescribeClusterNodesResponseBodyNodes, additions ...fields.Field) error {
	var err error

	conds := fields.FieldList(fields.NamedPField("instance_id", node.InstanceId))

	attrs := fields.NewFields(additions...)
	attrs.With(
		fields.NamedPField("instance_id", node.InstanceId),
		fields.NamedPField("instance_name", node.NodeName),
	)

	_, err = restclient.PostOrPatch[models.K8SNode](conds, attrs)
	if err != nil {
		color.Red(" sync node error %v", err)
	}

	color.Cyan(" node %s (%s) synced", *node.NodeName, *node.InstanceId)
	return err
}

func (s *Syncer) syncNodePool(p *csv5.DescribeClusterNodePoolsResponseBodyNodepools, additions ...fields.Field) error {
	var err error

	conds := fields.FieldList(fields.NamedPField("pool_id", p.NodepoolInfo.NodepoolId))

	attrs := BeautyNodePool(p).With(additions...)

	_, err = restclient.PostOrPatch[models.K8SNodePool](conds, attrs)
	if err != nil {
		color.Red(" sync node pool error %v", err)
	}
	color.Green("node pool %s synced", *p.NodepoolInfo.Name)

	return err
}
