package ack

import (
	"log/slog"
	"time"

	"gitlab.lilithgame.com/yunwei/pkg/logger"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/factory"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/task/ali"
)

type Syncer struct {
	cred    *ali.FactoryCrendential
	regions map[string]models.Region
	log     *slog.Logger
}

func NewSyncer(accountKey string) (*Syncer, error) {
	cred, err := ali.CreateFactoryCredential(accountKey)
	if err != nil {
		return nil, err
	}

	log := logger.Slog().With("factory", cred.Factory.KeyName, "account", cred.Account.KeyName)

	return &Syncer{
		cred: cred,
		regions: restclient.LoadRegions(
			fields.NamedField("factory__name", "阿里云"),
			fields.Unlimited,
		), // TODO: load zones by vendor is better
		log: log,
	}, nil
}

func Sync(accountKey string) error {
	startAt := time.Now().Unix()

	s, err := NewSyncer(accountKey)
	if err != nil {
		return err
	}

	err = s.Sync()
	if err == nil {
		s.Clean(startAt)
	}

	return err
}

func SyncMany(keys ...factory.FactoryKeyType) error {
	var err error
	for _, account := range keys {
		if serr := Sync(account.String()); serr != nil {
			err = serr
		}
	}

	return err
}

func SyncAll() error {
	var err error
	for _, account := range factory.AliyunFactories {
		if serr := Sync(account.String()); serr != nil {
			err = serr
		}
	}

	return err
}
