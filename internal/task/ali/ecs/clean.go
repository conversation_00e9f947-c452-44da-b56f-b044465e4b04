package ecs

import (
	"fmt"
	"time"

	"gitlab.lilithgame.com/yunwei/pkg/logger"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
)

func (s *Syncer) Clean(t int64) error {
	errors := make([]error, 0)

	// Clean "RUNNING"
	if err := s.cleanAccountMachineByStatus(fields.ExternalStatusRunning, t); err != nil {
		errors = append(errors, err)
	}

	if err := s.cleanAccountMachineByStatus(fields.ExternalStatusStopped, t); err != nil {
		errors = append(errors, err)
	}

	// 清理很久还未更新的机器(例如弹性 node)
	if err := s.cleanEmptyAccountMachine(t, -time.Hour*4); err != nil {
		errors = append(errors, err)
		logger.Error("clean empty account machine error", "error", err)
	}

	if len(errors) > 0 {
		return errors[0]
	}

	return nil
}

func (s *Syncer) cleanAccountMachineByStatus(status fields.Field, t int64) error {
	return s.cleanAccountMachine(status, fields.NumberField("updated_before", t))
}

func (s *Syncer) cleanAccountMachine(conds ...fields.Field) error {
	accountConditions := []fields.Field{
		fields.FactoryField(s.cred.Factory.GetID()),
		fields.FactoryAccountField(s.cred.Account.GetID()),
	}

	conds = append(conds, accountConditions...)

	for {
		resp, err := restclient.ListN[models.Machine](100, conds...)
		if err != nil {
			return fmt.Errorf("list all outdated machines error: %v", err)
		}

		logger.Info("some machines will deleted",
			"count", resp.Count,
			"factory", s.cred.Factory.Name,
			"account", s.cred.Account.Name,
			"conditions", conds,
		)

		for _, m := range resp.Results {
			if perr := restclient.Patch(&m, fields.NewFields(fields.ExternalStatusDeleted)); perr != nil {
				logger.Error("failed to patch machine", "error", perr)
			}
		}

		if resp.Count == 0 {
			return err
		}
	}
}

func (s *Syncer) cleanEmptyAccountMachine(t int64, delta time.Duration) error {
	conds := []fields.Field{
		fields.FactoryField(s.cred.Factory.GetID()),
		fields.IsNull(fields.FactoryAccountFieldKey),
		fields.Not(fields.ExternalStatusFieldKey, fields.ExternalStatusDeleted.Value),
	}

	dayAgo := time.Unix(t, 0).Add(delta).Unix()
	conds = append(conds, fields.NamedField("updated_before", dayAgo))

	var err error
	for {
		resp, lerr := restclient.ListN[models.Machine](100, conds...)
		if lerr != nil {
			logger.Error("failed to list empty account machine", "error", lerr)
			return lerr
		}

		logger.Info("find empty account machine result to delete",
			"count", resp.Count,
			"updated_before", dayAgo,
			"factory", s.cred.Factory.Name,
			"conds", conds,
		)

		if resp.Count == 0 {
			break
		}

		for _, m := range resp.Results {
			if perr := restclient.Patch(&m, fields.NewFields(fields.ExternalStatusDeleted)); perr != nil {
				logger.Error("failed to patch machine", "error", perr)
				err = perr
			}
		}
	}

	return err
}
