package ecs

import (
	"fmt"
	"log/slog"

	ecsv3 "github.com/alibabacloud-go/ecs-20140526/v3/client"
	"github.com/fatih/color"
	"gitlab.lilithgame.com/yunwei/pkg/logger"

	beauty "gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/beauty/aliyun"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/category"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/factory"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
	alitask "gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/task/ali"
)

type Syncer struct {
	cred    *alitask.FactoryCrendential
	regions map[string]regionInfo
	log     *slog.Logger
}

func NewSyncer(factoryKey factory.FactoryKeyType) (*Syncer, error) {
	cred, err := alitask.CreateFactoryCredential(factoryKey.String())
	if err != nil {
		return nil, err
	}

	log := logger.Slog().With("factory", cred.Factory.Name, "account", cred.Account.KeyName)

	s := &Syncer{cred: cred, log: log}
	completeAvailableRegions(s)

	return s, nil
}

func (s *Syncer) SyncAll() error {
	var err error
	for _, reg := range s.regions {
		color.Blue("  sync in %s", reg.RegionID)
		if serr := s.SyncInRegion(reg.RegionID); serr != nil {
			err = serr
		}
	}
	return err
}

func (s *Syncer) SyncInRegion(region string) error {
	cfg := *s.cred.ClientConfig
	cfg.SetRegionId(region)
	cfg.SetEndpoint(s.regions[region].Endpoint)

	cli, err := CreateClient(&cfg)
	if err != nil {
		return err
	}

	req := &ecsv3.DescribeInstancesRequest{
		RegionId: &region,
		PageSize: fields.Pointer(int32(50)),
	}

	for token, starts := "", false; token != "" || !starts; starts = true {
		if token != "" {
			req.SetNextToken(token)
		}

		resp, derr := cli.DescribeInstances(req)
		if derr != nil {
			err = derr
			s.log.Error("describe aliyun ecs failed", "err", derr)
			break
		}

		s.log.Debug("found ecs in region", "region", region, "count", *resp.Body.TotalCount)

		for _, i := range resp.Body.Instances.Instance {
			attrs := beauty.BeautyHost(i)
			attrs.With(
				fields.FactoryField(s.cred.Factory.GetID()),
				fields.FactoryAccountField(s.cred.Account.GetID()),
			)

			//  补充产品信息
			if productId := attrs.GetInt(fields.ProductFieldKey); productId == nil {
				if productId = category.GetMatchedProductID(s.cred.Account.Channel); productId != nil {
					attrs.Set(fields.ProductFieldKey, *productId)
				}
			}

			cond := []fields.Field{
				fields.ExternalUUIDField(*i.InstanceId),
			}

			if _, perr := restclient.PostOrPatch[models.Machine](cond, attrs); perr != nil {
				color.Red("sync %s failed, error %v", *i.InstanceName, err)
				err = perr
			} else {
				fmt.Printf(" [%s] %s, %s\n", *i.RegionId, *i.InstanceName, *i.Status)
			}

			// 及时同步EIP
			// if eipAddress := i.EipAddress; eipAddress != nil {
			// 	eipAttrs := fields.NewFields(
			// 		fields.ExternalUUIDField(*eipAddress.AllocationId),
			// 		fields.ExternalNameField(*eipAddress.IpAddress),
			// 		fields.FactoryField(s.cred.Factory.GetID()),
			// 		fields.FactoryAccountField(s.cred.Account.GetID()),
			// 	)

			// 	if _, perr := restclient.PostOrPatch[models.ElasticIP]([]fields.Field{
			// 		fields.ExternalUUIDField(*eipAddress.AllocationId),
			// 	}, eipAttrs); perr != nil {
			// 		color.Red("sync eip %s failed, error %v", *eipAddress.IpAddress, err)
			// 		err = perr
			// 	}
			// }
		}

		token = fields.Value(resp.Body.NextToken)
	}

	return err
}
