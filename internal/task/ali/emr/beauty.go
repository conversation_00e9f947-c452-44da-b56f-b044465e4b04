package emr

import (
	emrv2 "github.com/alibabacloud-go/emr-20210320/v2/client"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/category"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
)

func Beauty(c *emrv2.ClusterSummary) fields.Fields {
	attrs := fields.NewFields(
		fields.StringPField("cluster_id", c.ClusterId),
		fields.StringPField("name", c.ClusterName),
		fields.StringPField("state", c.ClusterState),
		fields.StringPField("type", c.ClusterType),
		fields.StringPField("version", c.ReleaseVersion),
	)

	// create time field from unix timestamp
	attrs.SetApply(fields.CreateTimeFieldKey, *c.CreateTime, fields.ApplyTimestampMilli)

	// figure out product by cluster name
	if product := category.Category(*c.ClusterName, ""); product != nil {
		attrs.Set("product", *product)
	}

	return attrs
}
