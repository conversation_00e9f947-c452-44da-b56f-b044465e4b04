package emr

import (
	emrv2 "github.com/alibabacloud-go/emr-20210320/v2/client"
	"github.com/fatih/color"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/category"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
)

func (s *Syncer) Sync() error {
	var serr error

	for _, region := range s.regions {
		color.Blue("sync in region %s\n", region.RegionID)
		if err := s.SyncWithRegion(region.RegionID); err != nil {
			color.Red("sync region %s error: %v\n", region.RegionID, err)
			serr = err
		}
	}

	return serr
}

func (s *Syncer) SyncWithRegion(region string) error {
	cli, err := CreateClient(s.cred.ClientConfig, region)
	if err != nil {
		return err
	}

	factoryAttrs := fields.FieldList(
		fields.FactoryField(s.cred.Factory.GetID()),
		fields.FactoryAccountField(s.cred.Account.GetID()),
		fields.RegionIDField(region),
	)

	input := &emrv2.ListClustersRequest{
		RegionId:   fields.Pointer(region),
		MaxResults: fields.Int32(20),
	}

	for token, started := "", false; token != "" || !started; started = true {
		if token != "" {
			input.SetNextToken(token)
		}

		resp, err := cli.ListClusters(input)
		if err != nil {
			return err
		}

		for _, cluster := range resp.Body.Clusters {
			attrs := Beauty(cluster).With(factoryAttrs...)

			// 补充产品信息(如果无法通过名字解析)
			if productId := attrs.GetInt(fields.ProductFieldKey); productId == nil {
				if productId = category.GetMatchedProductID(s.cred.Account.Channel); productId != nil {
					attrs.Set(fields.ProductFieldKey, *productId)
				}
			}

			color.Green("%+v", attrs)
			conds := fields.FieldList(
				fields.FactoryField(s.cred.Factory.GetID()),
				fields.StringField("cluster_id", *cluster.ClusterId),
			)
			if _, perr := restclient.PostOrPatch[models.EMRCluster](conds, attrs); perr != nil {
				color.Red("patch or post error", "error", perr)
			}
		}

		token = fields.Value(resp.Body.NextToken)
	}

	return nil
}
