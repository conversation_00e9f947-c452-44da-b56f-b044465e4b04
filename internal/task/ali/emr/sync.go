package emr

import (
	"time"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/factory"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/task/ali"
)

type Syncer struct {
	cred    *ali.FactoryCrendential
	regions map[string]models.Region
}

// NewSyncer 创建并返回一个新的 Syncer 实例，用于执行 EMR（Elastic MapReduce）任务同步操作。
// 它需要一个 `accountKey` 参数，用于在 Aliyun 中进行身份验证和授权访问 EMR API。
func NewSyncer(accountKey string) (*Syncer, error) {
	cred, err := ali.CreateFactoryCredential(accountKey)
	if err != nil {
		return nil, err
	}

	return &Syncer{
		cred: cred,
		regions: restclient.LoadRegions(
			fields.NamedField("factory__name", "阿里云"),
			fields.Unlimited,
		), // TODO: load zones by vendor is better
	}, nil
}

func Sync(accountKey string) error {
	startAt := time.Now().Unix()

	s, err := NewSyncer(accountKey)
	if err != nil {
		return err
	}

	err = s.Sync()
	if err == nil {
		s.Clean(startAt)
	}

	return err
}

func SyncMany(keys ...factory.FactoryKeyType) error {
	var err error
	for _, account := range keys {
		if serr := Sync(account.String()); serr != nil {
			err = serr
		}
	}

	return err
}

func SyncAll() error {
	var err error
	for _, account := range factory.AliyunFactories {
		if serr := Sync(account.String()); serr != nil {
			err = serr
		}
	}

	return err
}
