package slsv2

import (
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
)

func (s *Syncer) Clean(t int64) error {
	return s.cleanSlsProject(t)
}

func (s *Syncer) cleanSlsProject(t int64) error {
	conds := []fields.Field{
		fields.FactoryField(s.cred.Factory.GetID()),
		fields.FactoryAccountField(s.cred.Account.GetID()),
		// fields.LessThan("update_at", t),
	}

	resp, err := restclient.ListAll[models.SLS](conds...)
	if err != nil {
		return err
	}

	for _, m := range resp.Results {
		if cerr := s.cleanLogstore(t, m.GetID()); cerr != nil {
			continue
		}

		if int64(m.UpdateAt) < t {
			if perr := restclient.Delete(m); perr != nil {
				err = perr
			}
		}
	}

	return err
}

func (s *Syncer) cleanLogstore(t int64, projectID int) error {
	conds := []fields.Field{
		fields.NamedField("sls_project", projectID),
		fields.LessThan("update_at", t),
	}

	resp, err := restclient.ListAll[models.LogStore](conds...)
	if err != nil {
		return err
	}

	for _, m := range resp.Results {
		if perr := restclient.Delete(m); perr != nil {
			err = perr
		}
	}

	return err
}
