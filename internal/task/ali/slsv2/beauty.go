package slsv2

import (
	"strconv"
	"strings"
	"time"

	gosls "github.com/alibabacloud-go/sls-20201230/v6/client"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/category"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
)

func Beauty(p *gosls.Project) fields.Fields {
	attr := fields.NewFields(
		fields.ExternalUUIDField(*p.ProjectName),
		fields.ExternalNameField(*p.ProjectName),
		fields.ExternalStatusField(*p.Status),
		fields.NamedField("desc", *p.Description),
	)

	// rename status: normal -> running
	status := strings.ToLower(*p.Status)
	if status == "normal" {
		status = "running"
	}
	attr.SetField(fields.ExternalStatusField(status))

	// create time
	if t, terr := strconv.Atoi(*p.CreateTime); terr == nil {
		attr.Set(fields.CreateTimeFieldKey, time.Unix(int64(t), 0).Format(time.RFC3339))
	}

	if productId := category.Category(*p.ProjectName, ""); productId != nil {
		attr.Set(fields.ProductFieldKey, *productId)
	}

	return attr
}
