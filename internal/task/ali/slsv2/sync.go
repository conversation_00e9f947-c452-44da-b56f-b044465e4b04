package slsv2

import (
	"fmt"
	"log/slog"

	gosls "github.com/alibabacloud-go/sls-20201230/v6/client"
	"gitlab.lilithgame.com/yunwei/pkg/logger"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/factory"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
	alitask "gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/task/ali"
)

type Syncer struct {
	cred    *alitask.FactoryCrendential
	regions map[string]models.Region
	log     *slog.Logger
}

func NewSyncer(factoryKey factory.FactoryKeyType) (*Syncer, error) {
	cred, err := alitask.CreateFactoryCredential(factoryKey.String())
	if err != nil {
		return nil, err
	}

	log := logger.Slog().With("factory", cred.Factory.KeyName, "account", cred.Account.KeyName)

	return &Syncer{
		cred:    cred,
		regions: alitask.LoadRegions(*cred),
		log:     log,
	}, nil
}

func (s *Syncer) SyncAll() error {
	var err error
	for _, reg := range s.regions {
		if serr := s.SyncInRegion(reg.RegionID); serr != nil {
			err = serr
			s.log.Error("sync sls failed", "error", serr)
		}
	}
	return err
}

func (s *Syncer) SyncInRegion(region string) (err error) {
	cfg := *s.cred.ClientConfig
	cfg.SetEndpoint(fmt.Sprintf("%s.log.aliyuncs.com", region))

	cli, err := gosls.NewClient(&cfg)
	if err != nil {
		return err
	}

	const pageSize int32 = 100
	projectRequest := &gosls.ListProjectRequest{Size: fields.Int32(pageSize)}

	for offset := int32(0); ; offset += pageSize {
		projectRequest.Offset = fields.Int32(offset)

		projects, lerr := cli.ListProject(projectRequest)
		if lerr != nil {
			return lerr
		}

		for _, p := range projects.Body.Projects {
			attr := Beauty(p).With(
				fields.FactoryField(s.cred.Factory.GetID()),
				fields.FactoryAccountField(s.cred.Account.GetID()),
			)
			s.completeAttrs(p, attr)

			cond := []fields.Field{
				fields.ExternalUUIDField(*p.ProjectName),
				fields.FactoryAccountField(s.cred.Account.GetID()),
			}

			s.log.Debug("sync sls project", "project_name", *p.ProjectName)

			if slsRecord, perr := restclient.PostOrPatch[models.SLS](cond, attr); perr != nil {
				s.log.Error("sync sls failed", "project_name", *p.ProjectName, "error", perr)
				continue
			} else {
				// sync logstore
				if err = s.completeLogstores(cli, p.ProjectName, slsRecord.GetID(), attr); err != nil {
					s.log.Error("sync logstore failed", "project_name", *p.ProjectName, "error", err)
					continue
				}
			}
		}

		if *projects.Body.Count < int64(pageSize) {
			break
		}
	}

	return err
}

func (s *Syncer) completeLogstores(cli *gosls.Client, p *string, sls_project_id int, attr fields.Fields) error {
	const size int32 = 100

	req := &gosls.ListLogStoresRequest{Size: fields.Int32(size)}
	var lastErr error

	for offset := int32(0); ; offset += size {
		req.Offset = fields.Int32(offset)
		stores, err := cli.ListLogStores(p, req)
		if err != nil {
			return err
		}

		for _, lsName := range stores.Body.Logstores {
			if sAttr, serr := cli.GetLogStore(p, lsName); serr == nil {
				lsattr := fields.NewFields(
					fields.NamedPField("name", lsName),
					fields.NamedField("sls_project", sls_project_id),
					fields.NamedPField("shards", sAttr.Body.ShardCount),
					fields.NamedPField("ttl", sAttr.Body.Ttl),
					fields.NamedPField("hot_ttl", sAttr.Body.HotTtl),
					fields.NamedPField("infrequent_access_ttl", sAttr.Body.InfrequentAccessTTL),
				)

				conds := []fields.Field{
					fields.NamedField("sls_project", sls_project_id),
					fields.NamedPField("name", lsName),
				}

				if _, perr := restclient.PostOrPatch[models.LogStore](conds, lsattr); perr != nil {
					s.log.Error("sync logstore failed", "logstore_name", *lsName, "error", perr)
					lastErr = perr
				} else {
					s.log.Debug("sync logstore success", "logstore_name", *lsName)
				}
			}
		}

		if *stores.Body.Count < size {
			break
		}
	}

	return lastErr
}

func (s *Syncer) completeAttrs(p *gosls.Project, attr fields.Fields) {
	// region
	if region := p.Region; region != nil {
		if r, ok := s.regions[*region]; ok {
			attr.With(fields.NamedField(fields.RegionFieldKey, r.GetID()))
		}
	}
}
