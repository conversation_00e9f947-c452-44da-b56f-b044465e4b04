package ali

import (
	"fmt"

	openapiv2 "github.com/alibabacloud-go/darabonba-openapi/v2/client"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/cache"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
)

type FactoryCrendential struct {
	Factory      *models.Factory        `json:"factory"`
	Account      *models.FactoryAccount `json:"account"`
	ClientConfig *openapiv2.Config      `json:"credential"`
}

func CreateFactoryCredential(accountKey string) (*FactoryCrendential, error) {
	if f := cache.GetFactory("aliyun-lilith"); f != nil {
		account, err := getAccount(f.Get<PERSON>(), accountKey)
		if err != nil {
			return nil, err
		}

		config := restclient.AliConfigWithSecret(account.KMSAccount)
		if config == nil {
			return nil, fmt.Errorf("config aliyun for %s, %s failed", accountKey, f.Name)
		}

		return &FactoryCrendential{
			Factory:      f,
			Account:      account,
			ClientConfig: config,
		}, nil
	}

	return nil, fmt.Errorf("unknown factory %s", accountKey)
}

func getAccount(fid int, accountKey string) (*models.FactoryAccount, error) {
	var account models.FactoryAccount

	has, err := restclient.Find(&account,
		fields.NamedField("key_name", accountKey),
		fields.FactoryIDField(fid),
	)
	if err != nil {
		return nil, err
	}

	if !has {
		return nil, fmt.Errorf("account %s not found", accountKey)
	}

	return &account, nil
}
