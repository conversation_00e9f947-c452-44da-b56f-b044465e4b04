package hbase

import (
	"fmt"
	"os"
	"time"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/factory"
)

type BatchSyncer struct {
	syncers []*Syncer
}

func NewBatchSyncer(s ...*Syncer) *BatchSyncer {
	b := &BatchSyncer{
		syncers: make([]*Syncer, 0),
	}

	b.Append(s...)

	return b
}

func NewBatchSyncerWithFactoryKeys(keys ...factory.FactoryKeyType) *BatchSyncer {
	batcher := NewBatchSyncer()

	for _, key := range keys {
		if s, err := NewSyncer(key); err == nil {
			batcher.Append(s)
		} else {
			fmt.Fprintf(os.Stderr, "initialize syncer for factory %s error: %v\n", key, err)
		}
	}

	return batcher
}

func NewDefaultBatcherSyncer() *BatchSyncer {
	return NewBatchSyncerWithFactoryKeys(factory.AliyunFactories...)
}

func (bs *BatchSyncer) Len() int {
	return len(bs.syncers)
}

func (bs *BatchSyncer) Append(ss ...*Syncer) {
	if len(ss) > 0 {
		bs.syncers = append(bs.syncers, ss...)
	}
}

// func (bs *BatchSyncer) Sync(region string, instanceID string, fs ...fields.Field) error {
func (bs *BatchSyncer) Sync() error {
	var err error

	for _, s := range bs.syncers {
		t := time.Now().Unix()
		if serr := s.SyncAll(); serr != nil {
			err = serr
		} else {
			err = s.Clean(t)
		}
	}

	return err
}

func Sync(accout string) error {
	s := NewBatchSyncerWithFactoryKeys(factory.FactoryKeyType(accout))
	return s.Sync()
}
