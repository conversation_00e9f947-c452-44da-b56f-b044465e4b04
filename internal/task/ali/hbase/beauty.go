package hbase

import (
	hbasev3 "github.com/alibabacloud-go/hbase-20190101/v3/client"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/category"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
)

func BeautyWith(p *hbasev3.DescribeInstancesResponseBodyInstancesInstance, additions ...fields.Field) fields.Fields {
	return Beauty(p).With(additions...)
}

func Beauty(i *hbasev3.DescribeInstancesResponseBodyInstancesInstance) fields.Fields {
	attr := fields.NewFields(
		fields.NamedPField(fields.ExternalUUIDFieldKey, i.InstanceId),
		fields.StringPField(fields.ExternalNameFieldKey, i.InstanceName),
		fields.StringPField("cluster_type", i.ClusterType),
		fields.ExternalStatusField(fields.PString(i.Status)),
		fields.StringPField(fields.CreateTimeFieldKey, i.CreatedTime),
		fields.StringPField("engine", i.Engine),
		fields.StringPField("version", i.MajorVersion),
	)

	// product
	if productId := category.Category(attr.GetString(fields.ExternalNameFieldKey), ""); productId != nil {
		attr.Set(fields.ProductFieldKey, *productId)
	}

	return attr
}
