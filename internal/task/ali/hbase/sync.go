package hbase

import (
	hbsv3 "github.com/alibabacloud-go/hbase-20190101/v3/client"
	"github.com/fatih/color"
	"gitlab.lilithgame.com/yunwei/pkg/logger"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/factory"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
	alitask "gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/task/ali"
)

type Syncer struct {
	cred    *alitask.FactoryCrendential
	regions map[string]models.Region
}

func NewSyncer(factoryKey factory.FactoryKeyType) (*Syncer, error) {
	cred, err := alitask.CreateFactoryCredential(factoryKey.String())
	if err != nil {
		return nil, err
	}

	return &Syncer{
		cred: cred,
		regions: restclient.LoadRegions(
			fields.StringField("factory__name", " 阿里云"),
			fields.Unlimited,
		),
	}, nil
}

func (s *Syncer) SyncAll() error {
	availabelRegions, err := s.AvailableRegions()
	if err != nil {
		return err
	}

	for _, ar := range availabelRegions {
		if serr := s.SyncInRegion(ar); serr != nil {
			err = serr
			logger.Errorf("sync hbase failed, %v", serr)
		}
	}
	return err
}

func (s *Syncer) AvailableRegions() ([]string, error) {
	cfg := *s.cred.ClientConfig
	cfg.SetRegionId("cn-shanghai")

	regions := make([]string, 0)

	cli, err := hbsv3.NewClient(&cfg)
	if err != nil {
		return nil, err
	}

	resp, err := cli.DescribeRegions(&hbsv3.DescribeRegionsRequest{})
	if err != nil {
		return nil, err
	}

	for _, reg := range resp.Body.Regions.Region {
		regions = append(regions, *reg.RegionId)
	}

	return regions, nil
}

func (s *Syncer) SyncInRegion(region string) error {
	cli, err := s.newRegionClient(region)
	if err != nil {
		return err
	}

	input := &hbsv3.DescribeInstancesRequest{
		RegionId: &region,
		PageSize: fields.Int32(100),
	}

	for n := int32(1); ; n++ {
		input.SetPageNumber(n)
		resp, derr := cli.DescribeInstances(input)
		if derr != nil {
			return derr
		}

		if resp.Body.Instances == nil {
			break
		}

		for _, i := range resp.Body.Instances.Instance {
			attr := BeautyWith(i,
				fields.FactoryField(s.cred.Factory.GetID()),
				fields.FactoryAccountField(s.cred.Account.GetID()),
			)

			// region
			if region, found := s.regions[fields.PString(i.RegionId)]; found {
				attr.SetField(fields.RegionField(region.GetID()))
			}

			conds := []fields.Field{
				*attr.GetField(fields.ExternalUUIDFieldKey),
			}

			if _, perr := restclient.PostOrPatch[models.HBase](conds, attr); perr != nil {
				err = perr
				color.Red("sync hbase failed, %v", perr)
			} else {
				color.Green("sync hbase success, %v", *i.InstanceName)

				// 同步白名单
				s.SyncWhitelist(cli, *i.ClusterId)

				// 同步安全组
				s.SyncSecurityGroups(cli, *i.ClusterId)
			}
		}

		if len(resp.Body.Instances.Instance) < int(*input.PageSize) {
			break
		}
	}

	return err
}

func (s *Syncer) newRegionClient(region string) (*hbsv3.Client, error) {
	cfg := *s.cred.ClientConfig
	cfg.SetRegionId(region)
	cfg.SetEndpoint("hbase.aliyuncs.com")

	cli, err := hbsv3.NewClient(&cfg)
	if err != nil {
		return nil, err
	}
	return cli, nil
}
