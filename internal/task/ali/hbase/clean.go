package hbase

import (
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
)

func (s *Syncer) Clean(t int64) error {
	conds := []fields.Field{
		fields.FactoryAccountField(s.cred.Account.GetID()),
		fields.NamedField("resource_type", "hbase"),
		fields.LessThan("update_at", t),
	}

	resp, err := restclient.ListAll[models.Other](conds...)
	if err != nil {
		return err
	}

	for _, r := range resp.Results {
		restclient.Delete(r)
	}

	return err
}
