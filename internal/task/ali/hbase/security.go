package hbase

import (
	"strings"
	"time"

	hbsv3 "github.com/alibabacloud-go/hbase-20190101/v3/client"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
)

func (s *Syncer) SyncWhitelist(cli *hbsv3.Client, clusterID string) error {
	request := &hbsv3.DescribeIpWhitelistRequest{
		ClusterId: fields.Pointer(clusterID),
	}

	resp, err := cli.DescribeIpWhitelist(request)
	if err != nil {
		return err
	}

	startAt := time.Now().Unix()
	for _, group := range resp.Body.Groups.Group {
		attrs := fields.Fields{
			"group_name": fields.Value(group.GroupName),
			"db":         clusterID,
		}

		// ip version
		if ipVersion := fields.Value(group.IpVersion); ipVersion == 4 {
			attrs.Set("ip_type", "IPv4")
		} else if ipVersion == 6 {
			attrs.Set("ip_type", "IPv6")
		}

		// ip list
		ipList := make([]string, 0)
		for _, ip := range group.IpList.Ip {
			ipList = append(ipList, fields.Value(ip))
		}
		attrs.Set("ip_list", strings.Join(ipList, ","))

		conds := fields.FieldList(
			fields.StringField("db", clusterID),
			*attrs.GetField("group_name"),
		)

		if _, perr := restclient.PostOrPatch[models.HBaseWhitelist](conds, attrs); perr != nil {
			err = perr
		}
	}

	// clean whitelist
	if err == nil {
		restclient.DeleteSubResource[models.HBase](
			"whitelist",
			clusterID,
			fields.NumberField("updated_before", startAt),
		)
	}

	return err
}

func (s *Syncer) SyncSecurityGroups(cli *hbsv3.Client, clusterID string) error {
	input := &hbsv3.DescribeSecurityGroupsRequest{
		ClusterId: fields.Pointer(clusterID),
	}

	resp, err := cli.DescribeSecurityGroups(input)
	if err != nil {
		return err
	}

	groups := make([]string, 0)
	for _, sg := range resp.Body.SecurityGroupIds.SecurityGroupId {
		groups = append(groups, *sg)
	}

	restclient.PostSubResource[models.HBase]("security_groups", clusterID, fields.Fields{
		"security_groups": groups,
	})

	return nil
}
