package mysql

import (
	rdsv3 "github.com/alibabacloud-go/rds-20140815/v3/client"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/category"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
)

func BeautyWith(i *rdsv3.DescribeDBInstancesResponseBodyItemsDBInstance, additions ...fields.Field) fields.Fields {
	return Beauty(i).With(additions...)
}

func Beauty(i *rdsv3.DescribeDBInstancesResponseBodyItemsDBInstance) fields.Fields {
	attr := fields.NewFields(
		fields.NamedPField(fields.ExternalUUIDFieldKey, i.DBInstanceId),
		fields.NamedPField(fields.ExternalNameFieldKey, i.DBInstanceDescription),
		fields.ExternalStatusField(fields.Value(i.DBInstanceStatus)),
		fields.NamedPField("db_type", i.DBInstanceType),
		fields.LowerStringPField("engine", i.Engine),
		fields.NamedPField("version", i.EngineVersion),
		fields.NamedPField("conn", i.ConnectionString),
		fields.NamedPField(fields.CreateTimeFieldKey, i.CreateTime),
	)

	if name := fields.Value(i.DBInstanceDescription); name != "" {
		if productId := category.Category(name, ""); productId != nil {
			attr.Set(fields.ProductFieldKey, *productId)
		}
	}

	return attr
}
