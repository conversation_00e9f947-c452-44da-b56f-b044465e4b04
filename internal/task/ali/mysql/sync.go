package mysql

import (
	"github.com/fatih/color"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/factory"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
	alitask "gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/task/ali"
)

type Syncer struct {
	cred    *alitask.FactoryCrendential
	regions map[string]models.Region
}

func NewSyncer(factoryKey factory.FactoryKeyType) (*Syncer, error) {
	cred, err := alitask.CreateFactoryCredential(factoryKey.String())
	if err != nil {
		return nil, err
	}

	return &Syncer{
		cred: cred,
		regions: restclient.LoadRegions(
			fields.StringField("factory__name", " 阿里云"),
			fields.Unlimited,
		),
	}, nil
}

func (s *Syncer) SyncAll() error {
	var err error
	for _, reg := range s.regions {
		color.Blue("  sync in %s", reg.RegionID)
		if serr := s.SyncInRegion(reg.RegionID); serr != nil {
			err = serr
		}
	}
	return err
}
