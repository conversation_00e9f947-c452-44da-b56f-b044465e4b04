package mysql

import (
	rdsv3 "github.com/alibabacloud-go/rds-20140815/v3/client"
	"github.com/fatih/color"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
)

func (s *Syncer) SyncInRegion(region string) error {
	cfg := *s.cred.ClientConfig
	cfg.SetRegionId(region)
	cfg.SetEndpoint("rds.aliyuncs.com")

	cli, err := rdsv3.NewClient(&cfg)
	if err != nil {
		return err
	}

	commonAttr := []fields.Field{
		fields.FactoryField(s.cred.Factory.GetID()),
		fields.FactoryAccountField(s.cred.Account.GetID()),
	}

	req := &rdsv3.DescribeDBInstancesRequest{
		RegionId: fields.Pointer(region),
		PageSize: fields.Int32(100),
	}

	var serr error
	for token, started := "", false; token != "" || !started; started = true {
		if token != "" {
			req.SetNextToken(token)
		}

		resp, derr := cli.DescribeDBInstances(req)
		if derr != nil {
			return derr
		}

		items := resp.Body.Items
		if items == nil {
			break
		}

		for _, i := range items.DBInstance {
			attr := BeautyWith(i, commonAttr...)
			s.completeDetail(cli, i, attr)

			conds := []fields.Field{
				fields.NamedPField(fields.ExternalUUIDFieldKey, i.DBInstanceId),
			}

			if _, perr := restclient.PostOrPatch[models.MySQL](conds, attr); perr != nil {
				serr = perr
			} else {
				color.Green("sync %s %s ok", *i.DBInstanceId)

				//  同步白名单
				s.SyncWhitelist(cli, attr.GetString(fields.ExternalUUIDFieldKey))
				//  同步安全组
				s.SyncInstanceSecurityGroups(cli, attr.GetString(fields.ExternalUUIDFieldKey))
			}
		}

		token = fields.Value(resp.Body.NextToken)
	}

	return serr
}

func (s *Syncer) completeDetail(cli *rdsv3.Client, i *rdsv3.DescribeDBInstancesResponseBodyItemsDBInstance, attr fields.Fields) error {
	// region
	if region := fields.Value(i.RegionId); region != "" {
		if r, ok := s.regions[region]; ok {
			attr.With(fields.NamedField(fields.RegionFieldKey, r.GetID()))
		}
	}

	input := &rdsv3.DescribeDBInstanceAttributeRequest{
		DBInstanceId: i.DBInstanceId,
	}
	attrResp, err := cli.DescribeDBInstanceAttribute(input)
	if err != nil {
		return err
	}

	detail := attrResp.Body.Items.DBInstanceAttribute
	if len(detail) == 0 {
		return nil
	}

	attr.SetAsInt("port", detail[0].Port)
	attr.SetAsInt("cpu", detail[0].DBInstanceCPU)

	// iops
	attr.Set("iops", fields.Value(detail[0].MaxIOPS))
	// mem (MB)
	attr.Set("mem", fields.Value(detail[0].DBInstanceMemory))
	// disk
	attr.Set("disk", fields.Value(detail[0].DBInstanceStorage))
	attr.Set("flavor_name", fields.Value(detail[0].DBInstanceClass))

	return nil
}
