package mysql

import (
	"time"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
)

func (s *Syncer) clean(cond ...fields.Field) error {
	resp, err := restclient.ListAll[models.MySQL](cond...)
	if err != nil {
		return err
	}

	for _, m := range resp.Results {
		if perr := restclient.Delete(m); perr != nil {
			err = perr
		}
	}

	return err
}

func (s *Syncer) Clean(t int64) error {
	if err := s.clean(
		fields.FactoryAccountField(s.cred.Account.GetID()),
		fields.LessThan("update_at", t),
	); err != nil {
		return err
	}

	// cleanup parent factory outdated instances
	detal := time.Unix(t, 0).Add(-time.Hour * 6).Unix()

	return s.clean(
		fields.FactoryField(s.cred.Factory.GetID()),
		fields.<PERSON><PERSON>han("update_at", detal),
	)
}
