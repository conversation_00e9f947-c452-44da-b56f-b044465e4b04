package mysql

import (
	"strings"
	"time"

	rdsv3 "github.com/alibabacloud-go/rds-20140815/v3/client"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
)

// SyncWhitelist sync whitelist and security groups releated with rds instances.
func (s *Syncer) SyncWhitelist(cli *rdsv3.Client, dbID string) (err error) {
	input := &rdsv3.DescribeDBInstanceIPArrayListRequest{
		DBInstanceId: fields.Pointer(dbID),
	}

	resp, err := cli.DescribeDBInstanceIPArrayList(input)
	if err != nil {
		return err
	}

	startAt := time.Now().Unix()
	for _, whiteIP := range resp.Body.Items.DBInstanceIPArray {
		if aattr := whiteIP.DBInstanceIPArrayAttribute; fields.PValueApplyEqual(aattr, strings.ToLower, "hidden") {
			continue
		}

		attrs := fields.NewFields(
			fields.StringPField("group_name", whiteIP.DBInstanceIPArrayName),
			fields.StringPField("ip_type", whiteIP.SecurityIPType),
			fields.StringPField("ip_list", whiteIP.SecurityIPList),
			fields.StringField("db", dbID),
		)

		conds := []fields.Field{
			fields.StringField("db", dbID),
			*attrs.GetField("group_name"),
		}

		if _, perr := restclient.PostOrPatch[models.MySQLWhitelist](conds, attrs); perr != nil {
			err = perr
		}
	}

	// clean outdated whitelists
	if err == nil {
		restclient.DeleteSubResource[models.MySQL](
			"whitelist",
			dbID,
			fields.NumberField("updated_before", startAt),
		)
	}

	return
}

func (s *Syncer) SyncInstanceSecurityGroups(cli *rdsv3.Client, dbID string) error {
	input := &rdsv3.DescribeSecurityGroupConfigurationRequest{
		DBInstanceId: fields.Pointer(dbID),
	}
	resp, err := cli.DescribeSecurityGroupConfiguration(input)
	if err != nil {
		return err
	}

	groups := make([]string, 0)
	for _, rel := range resp.Body.Items.EcsSecurityGroupRelation {
		groups = append(groups, *rel.SecurityGroupId)
	}

	restclient.PostSubResource[models.MySQL]("security_groups", dbID, fields.Fields{
		"security_groups": groups,
	})

	return nil
}
