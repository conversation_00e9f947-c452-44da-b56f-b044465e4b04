package redis

import (
	redisv3 "github.com/alibabacloud-go/r-kvstore-20150101/v3/client"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/category"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
)

func BeautyWith(i *redisv3.DescribeInstancesResponseBodyInstancesKVStoreInstance, additions ...fields.Field) fields.Fields {
	return Beauty(i).With(additions...)
}

func Beauty(i *redisv3.DescribeInstancesResponseBodyInstancesKVStoreInstance, additions ...fields.Field) fields.Fields {
	attr := fields.NewFields(
		fields.NamedPField(fields.ExternalUUIDFieldKey, i.InstanceId),
		fields.NamedPField(fields.ExternalNameFieldKey, i.InstanceName),
		// fields.TitleStringPField("engine", i.InstanceType),
		fields.StringPField("engine", i.InstanceType),
		fields.NamedPField("version", i.EngineVersion),
		fields.NamedField("edition", fields.PLowerString(i.EditionType)),
		fields.NamedPField("conn", i.ConnectionDomain),
		fields.NamedPField("arch", i.ArchitectureType),
		fields.NamedPField("private_ip", i.PrivateIp),
		fields.NamedPField("qps", i.QPS),
		fields.NamedPField(fields.CreateTimeFieldKey, i.CreateTime),
	)

	tagsList := make([]map[string]string, 0)
	if tags := i.Tags; tags != nil {
		for _, tag := range tags.Tag {
			tagsList = append(tagsList, map[string]string{
				"k": *tag.Key,
				"v": fields.Value(tag.Value),
			})
		}
		if len(tagsList) > 0 {
			attr.SetJSON("external_tags", tagsList)
		}
	}

	// attr.SetJSON("external_tags", value any)

	// redis type: 0: cluster, 1: standard
	redisType := 1
	if *i.ArchitectureType == "cluster" {
		redisType = 0
	}
	attr.Set("types", redisType)

	// public_ip
	// capacity
	// available

	// status renaming
	status := fields.PLowerString(i.InstanceStatus)
	if status == "normal" {
		status = "running"
	}
	attr.SetField(fields.ExternalStatusField(status))

	if name := fields.Value(i.InstanceName); name != "" {
		if productId := category.Category(name, ""); productId != nil {
			attr.Set(fields.ProductFieldKey, *productId)
		}
	}

	return attr
}
