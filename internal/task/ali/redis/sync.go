package redis

import (
	"slices"

	"github.com/fatih/color"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/factory"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
	alitask "gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/task/ali"
)

type Syncer struct {
	cred    *alitask.FactoryCrendential
	regions map[string]regionInfo
}

func NewSyncer(factoryKey factory.FactoryKeyType) (*Syncer, error) {
	cred, err := alitask.CreateFactoryCredential(factoryKey.String())
	if err != nil {
		return nil, err
	}

	s := &Syncer{cred: cred}
	completeAvailableRegions(s)

	return s, nil
}

func completeAvailableRegions(s *Syncer) {
	availableRegions := s.AvailableRegions()

	regions := make(map[string]regionInfo, 0)

	restclient.LoadSomeRegions(
		func(r *models.Region) bool {
			if slices.Contains(alitask.DeprecatedRegions, r.RegionID) {
				return false
			}

			endpoint, found := availableRegions[r.RegionID]
			if found {
				regions[r.RegionID] = regionInfo{
					Region:   r,
					Endpoint: endpoint,
				}
			}
			return found
		},
		fields.NamedField("factory__name", "阿里云"),
		fields.Unlimited,
	)

	s.regions = regions
}

func (s *Syncer) SyncAll() error {
	var err error
	for _, reg := range s.regions {
		color.Blue("  sync in %s", reg.RegionID)
		if serr := s.SyncInRegion(reg.RegionID); serr != nil {
			err = serr
			color.Red("sync redis failed, %v", serr)
		}
	}
	return err
}
