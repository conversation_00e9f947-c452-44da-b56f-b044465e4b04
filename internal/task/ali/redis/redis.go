package redis

import (
	redisv3 "github.com/alibabacloud-go/r-kvstore-20150101/v3/client"
	"github.com/fatih/color"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/category"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
)

func (s *Syncer) SyncInRegion(region string) error {
	cfg := *s.cred.ClientConfig

	if r, found := s.regions[region]; found {
		cfg.SetEndpoint(r.Endpoint)
	}

	cli, err := CreateClient(&cfg, region)
	if err != nil {
		return err
	}

	commonAttr := []fields.Field{
		fields.FactoryField(s.cred.Factory.GetID()),
		fields.FactoryAccountField(s.cred.Account.GetID()),
	}

	req := &redisv3.DescribeInstancesRequest{
		RegionId: fields.Pointer(region),
		PageSize: fields.Int32(50),
	}

	var serr error
	for n := int32(1); ; n++ {
		req.SetPageNumber(n)

		resp, derr := cli.DescribeInstances(req)
		if derr != nil {
			return derr
		}

		items := resp.Body.Instances
		if items == nil {
			break
		}

		if len(items.KVStoreInstance) > 0 {
			color.Green("  found %d redis instances in %s", len(items.KVStoreInstance), region)
		}

		for _, i := range items.KVStoreInstance {
			attr := BeautyWith(i, commonAttr...)
			s.completeDetail(cli, i, attr)

			conds := []fields.Field{
				fields.StringPField(fields.ExternalUUIDFieldKey, i.InstanceId),
			}

			if _, perr := restclient.PostOrPatch[models.Redis](conds, attr); perr != nil {
				serr = perr
				color.Red("sync failed, %v", serr)
			} else {
				// 同步白名单
				s.SyncWhitelist(cli, *i.InstanceId)
				//  同步安全组
				s.SyncInstanceSecurityGroups(cli, *i.InstanceId)
			}
		}

		if len(items.KVStoreInstance) < int(fields.Value(req.PageSize)) {
			break
		}
	}

	return serr
}

func (s *Syncer) completeDetail(cli *redisv3.Client, i *redisv3.DescribeInstancesResponseBodyInstancesKVStoreInstance, attr fields.Fields) error {
	// region
	if region := fields.Value(i.RegionId); region != "" {
		if r, ok := s.regions[region]; ok {
			attr.With(fields.NamedField(fields.RegionFieldKey, r.GetID()))
		}
	}

	//  修正产品ID
	if productId := attr.GetInt(fields.ProductFieldKey); productId == nil {
		if productId = category.GetMatchedProductID(s.cred.Account.Channel); productId != nil {
			attr.Set(fields.ProductFieldKey, *productId)
		}
	}

	// more...
	input := &redisv3.DescribeInstanceAttributeRequest{
		InstanceId: i.InstanceId,
	}
	attrResp, err := cli.DescribeInstanceAttribute(input)
	if err != nil {
		return err
	}

	detail := attrResp.Body.Instances.DBInstanceAttribute
	if len(detail) == 0 {
		return nil
	}

	attr.SetOrNil("port", detail[0].Port)
	attr.Set("capacity",
		fields.PValueApply(detail[0].Capacity, func(t int64) int64 { return t / 1024 }),
	)

	return nil
}
