package redis

import (
	redisv3 "github.com/alibabacloud-go/r-kvstore-20150101/v3/client"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
)

type regionInfo struct {
	*models.Region
	Endpoint string `json:"endpoint"`
}

func (s *Syncer) AvailableRegions() map[string]string {
	regions := make(map[string]string)

	cli, err := CreateClient(s.cred.ClientConfig, "cn-hangzhou")
	if err != nil {
		return nil
	}

	resp, err := cli.DescribeRegions(&redisv3.DescribeRegionsRequest{})
	if err != nil {
		return nil
	}

	for _, reg := range resp.Body.RegionIds.KVStoreRegion {
		regions[*reg.RegionId] = *reg.RegionEndpoint
	}

	return regions
}
