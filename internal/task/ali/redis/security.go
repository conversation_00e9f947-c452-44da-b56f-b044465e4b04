package redis

import (
	"strings"
	"time"

	redisv3 "github.com/alibabacloud-go/r-kvstore-20150101/v3/client"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
)

func (s *Syncer) SyncWhitelist(cli *redisv3.Client, dbID string) error {
	request := &redisv3.DescribeSecurityIpsRequest{
		InstanceId: fields.Pointer(dbID),
	}

	resp, err := cli.DescribeSecurityIps(request)
	if err != nil {
		return err
	}

	startAt := time.Now().Unix()
	for _, group := range resp.Body.SecurityIpGroups.SecurityIpGroup {
		if fields.PValueApplyEqual(group.SecurityIpGroupAttribute, strings.ToLower, "hidden") {
			continue
		}

		attrs := fields.Fields{
			"group_name": fields.Value(group.SecurityIpGroupName),
			"ip_list":    fields.Value(group.SecurityIpList),
			"db":         dbID,
		}

		conds := fields.FieldList(
			fields.StringField("db", dbID),
			*attrs.GetField("group_name"),
		)

		if _, perr := restclient.PostOrPatch[models.RedisWhitelist](conds, attrs); perr != nil {
			err = perr
		}
	}

	// clean outdated whitelists
	if err == nil {
		restclient.DeleteSubResource[models.Redis](
			"whitelist",
			dbID,
			fields.NumberField("updated_before", startAt),
		)
	}

	return err
}

func (s *Syncer) SyncInstanceSecurityGroups(cli *redisv3.Client, dbID string) error {
	input := &redisv3.DescribeSecurityGroupConfigurationRequest{
		InstanceId: fields.Pointer(dbID),
	}
	resp, err := cli.DescribeSecurityGroupConfiguration(input)
	if err != nil {
		return err
	}

	groups := make([]string, 0)
	for _, rel := range resp.Body.Items.EcsSecurityGroupRelation {
		groups = append(groups, *rel.SecurityGroupId)
	}

	restclient.PostSubResource[models.Redis]("security_groups", dbID, fields.Fields{
		"security_groups": groups,
	})

	return nil
}
