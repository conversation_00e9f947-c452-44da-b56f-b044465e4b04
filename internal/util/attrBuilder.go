package util

import (
	"strings"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
)

func RegionAttrBuilder(regionID, name string, factory int) fields.Fields {
	attr := map[string]any{
		"region_id": regionID,
		"name":      name,
		"factory":   factory,
	}
	return attr
}

func ZoneAttrBuilder(regionID any, zoneID, name, desc, zoneType string, factory int) fields.Fields {
	attr := map[string]any{
		"region":    regionID,
		"zone_id":   zoneID,
		"name":      name,
		"desc":      desc,
		"zone_type": zoneType,
		"factory":   factory,
	}
	return attr
}

func SubnetAttrBuilder(
	subnetID, subnetName, aliasName, desc, cidrBlock string,
	isDefault bool,
	availableIPCount int64,
	vpcCmdbID, zoneCmdbID any,
) fields.Fields {
	attr := map[string]any{
		"subnet_id":          subnetID,
		"name":               subnetName,
		"alias_name":         aliasName,
		"desc":               desc,
		"cidr_block":         cidrBlock,
		"is_default":         isDefault,
		"available_ip_count": availableIPCount,
		"vpc":                vpcCmdbID,
		"zone":               zoneCmdbID,
	}
	return attr
}

func VpcAttrBuilder(regionID any, vpcID, vpcName, desc, vpcCidrBlock string, factory int, account int) fields.Fields {
	// attr := map[string]interface{}{
	// 	"vpc_id":     vpcID,
	// 	"name":       vpcName,
	// 	"cidr_block": vpcCidrBlock,
	// 	"desc":       desc,
	// 	"region":     regionID,
	// 	"factory":    factory,
	// 	"account":    account,
	// }

	attr := fields.NewFields(
		fields.NamedField("vpc_id", vpcID),
		fields.NamedField("name", vpcName),
		fields.NamedField("cidr_block", vpcCidrBlock),
		fields.NamedField("desc", desc),
		fields.NamedField("region", regionID),
		fields.NamedField("factory", factory),
		fields.NamedField("account", account),
	)

	return attr
}

func ECSAttrBuilder(
	instanceID string,
	instanceName string,
	hostname string,
	instanceStatus string,
	instanceType string,
	cpu, memory int32,
	osName string,
	description string,
	publicIP string,
	privateIP string,
	zone any,
	factory int,
	createTime any,
) fields.Fields {
	// 统一MB换成GB
	memory = memory / 1024
	attr := map[string]any{
		"external_uuid":     instanceID,
		"external_name":     instanceName,
		"external_hostname": hostname,
		"external_status":   strings.ToUpper(instanceStatus), // 统一大写
		"external_flavor":   instanceType,
		"cpu":               cpu,
		"mem":               memory,
		"os_name":           osName,
		"desc":              description,
		"public_ip":         publicIP,
		"private_ip":        privateIP,
		"zone":              zone,
		"factory":           factory,
		"create_time":       createTime,
	}
	return attr
}

func MysqlAttrBuilder(dbInstanceID, name, dbInstanceStatus string,
	connectionString string,
	port, cpu int,
	mem int64,
	disk, iops int32,
	engineVersion, dbInstanceClass, createTime string,
	regionID any,
	factory int,
) fields.Fields {
	// 内存单位为MB
	attr := map[string]any{
		"external_uuid":   dbInstanceID,
		"external_name":   name,
		"external_status": dbInstanceStatus,
		"conn":            connectionString,
		"port":            port,
		"cpu":             cpu,
		"mem":             mem,
		"disk":            disk,
		"iops":            iops,
		"version":         engineVersion,
		"flavor_name":     dbInstanceClass,
		"create_time":     createTime,
		"region":          regionID,
		"factory":         factory,
	}
	return attr
}

func RedisAttrBuilder(
	name, dbInstanceID string,
	external_status int,
	available bool,
	types int,
	connectionString string,
	port int64,
	privateIp, publicIp string,
	capacity, qps int64,
	createTime string,
	regionID any,
	factory int,
) fields.Fields {
	attr := map[string]any{
		"external_name":   name,
		"external_uuid":   dbInstanceID,
		"external_status": external_status,
		"available":       available,
		"types":           types,
		"conn":            connectionString,
		"port":            port,
		"private_ip":      privateIp,
		"public_ip":       publicIp,
		"capacity":        capacity,
		"qps":             qps,
		"create_time":     createTime,
		"region":          regionID,
		"factory":         factory,
	}
	return attr
}

func MongoAttrBuilder(
	name, dbInstanceID, dbInstanceStatus, instanceType, replicaSetName string,
	disk, iops, maxConnections int32,
	engineVersion, instanceClass, primaryConn string,
	primaryPort int,
	secondaryConn string,
	secondaryPort int,
	readOnlyConn string,
	readOnlyPort int,
	shardConn, mongosConn string,
	mongosPort int32,
	regionID any,
	factory int,
) fields.Fields {
	attrs := fields.NewFields(
		fields.ExternalUUIDField(dbInstanceID),
		fields.ExternalNameField(name),
		// fields.ExternalStatusField(0),
		fields.ExternalStatusField(dbInstanceStatus),
		fields.RegionField(regionID),
		fields.FactoryField(factory),
		fields.NamedField("db_type", instanceType),
		fields.NamedField("replica_set_name", replicaSetName),
		fields.NamedField("disk", disk),
		fields.NamedField("iops", iops),
		fields.NamedField("connections", maxConnections),
		fields.NamedField("version", engineVersion),
		fields.NamedField("flavor_name", instanceClass),
		fields.NamedField("primary_conn", primaryConn),
		fields.NamedField("primary_port", primaryPort),
		fields.NamedField("secondary_conn", secondaryConn),
		fields.NamedField("secondary_port", secondaryPort),
		fields.NamedField("readonly_conn", readOnlyConn),
		fields.NamedField("readonly_port", readOnlyPort),
		fields.NamedField("shard_conn", shardConn),
		fields.NamedField("mongos_conn", mongosConn),
		fields.NamedField("mongos_port", mongosPort),
	)
	return attrs
}

func LbAttrBuilder(
	loadBalancerID, address, name, status, lbType string,
	vpcID, regionID any,
	factory int,
	createTime string,
) fields.Fields {
	// if _, ok := vpcID.(float64); ok {
	// 	vpcID = ""
	// }
	attr := map[string]any{
		"slb_id":          loadBalancerID,
		"address":         address,
		"name":            name,
		"external_status": status,
		"network_type":    lbType,
		"vpc":             vpcID,
		"region":          regionID,
		"factory":         factory,
		"create_time":     createTime,
	}
	return attr
}

func EipAttrBuilder(
	instanceID, publicIp, name, version, state string,
	machine any,
	factory int,
) fields.Fields {
	attr := map[string]any{
		"external_uuid":  instanceID,
		"external_state": state,
		"public_ip":      publicIp,
		"name":           name,
		"version":        version,
		"machine":        machine,
		"factory":        factory,
	}
	return attr
}

func ImageAttrBuilder(
	instanceID, name, desc, osName, imageType, localDesc string,
	region any,
	factory int,
) fields.Fields {
	attr := map[string]any{
		"external_id":     instanceID,
		"external_name":   name,
		"external_desc":   desc,
		"external_osname": osName,
		"image_type":      imageType,
		"local_desc":      localDesc,
		"region":          region,
		"factory":         factory,
	}
	return attr
}

func SecurityGroupAttrBuilder(
	instanceID, name, desc string,
	vpc int,
	factory int,
) fields.Fields {
	attr := map[string]any{
		"security_id": instanceID,
		"name":        name,
		"desc":        desc,
		"vpc":         vpc,
		"factory":     factory,
	}
	return attr
}

func SecurityGroupRuleAttrBuilder(
	name, ruleID, policy, direction, protocol string,
	priority int,
	ip, port string,
	securityGroupID int,
) fields.Fields {
	attr := map[string]any{
		"name":      name,
		"rule_id":   ruleID,
		"policy":    policy,
		"direction": direction,
		"protocol":  protocol,
		"priority":  priority,
		"ip":        ip,
		"port":      port,
		"security":  securityGroupID,
	}
	return attr
}

func OtherResourceAttrBuilder(
	instanceID, name, externalStatus string,
	instanceType int,
	region any,
	factory int,
) fields.Fields {
	attr := map[string]any{
		"external_uuid":   instanceID,
		"external_name":   name,
		"external_status": externalStatus,
		"instance_type":   instanceType,
		"local_status":    0,
		"region":          region,
		"factory":         factory,
	}
	return attr
}
