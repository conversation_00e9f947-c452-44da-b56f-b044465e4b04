package util

import (
	"testing"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
)

func TestBuildURL(t *testing.T) {
	type args struct {
		prefix string
		params fields.Fields
	}
	tests := []struct {
		name    string
		args    args
		want    string
		wantErr bool
	}{
		{
			"delete sub resource url",
			args{
				"https://localhost:8080/api/cmdb/acl/acl-1/old_entries/",
				fields.NewFields(
					fields.NamedField("updated_before", 1),
				),
			},
			"https://localhost:8080/api/cmdb/acl/acl-1/old_entries/?updated_before=1",
			false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := BuildURL(tt.args.prefix, tt.args.params)
			if (err != nil) != tt.wantErr {
				t.<PERSON>rf("BuildURL() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.<PERSON>rf("BuildURL() = %v, want %v", got, tt.want)
			}

			t.Logf("got %s", got)
		})
	}
}
