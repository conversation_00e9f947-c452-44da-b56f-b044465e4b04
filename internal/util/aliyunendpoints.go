package util

var AliyunKVRegions = []string{
	"cn-fuzhou",
	"cn-qingdao",
	"cn-beijing",
	"cn-wulanchabu",
	"cn-hangzhou",
	"cn-shanghai",
	"cn-shenzhen",
	"cn-heyuan",
	"ap-southeast-1",
	"us-east-1", // 美国-弗吉尼亚
	"us-west-1",
	"cn-hangzhou-finance",
	"cn-shanghai-finance-1",
	"cn-shenzhen-finance-1",
	"cn-beijing-finance-1",
}

var AliyunHBaseRegions = []string{
	"cn-qingdao",
	"cn-beijing",
	"cn-hangzhou",
	"cn-shanghai",
	"cn-shenzhen",
	"cn-hongkong",
	"ap-southeast-1",
	"us-east-1",
	"us-west-1",
	"cn-hangzhou-finance",
	"cn-shanghai-finance-1",
	"cn-shenzhen-finance-1",
	"cn-beijing-finance-1",
}

func GetRedisEndPoint(region string) string {
	if IsContain(AliyunKVRegions, region) {
		return "r-kvstore.aliyuncs.com"
	} else {
		return "r-kvstore." + region + ".aliyuncs.com"
	}
}

func GetHbaseEndPoint(region string) string {
	if IsContain(AliyunHBaseRegions, region) {
		return "hbase.aliyuncs.com"
	} else {
		return "hbase." + region + ".aliyuncs.com"
	}
}
