package util

import "testing"

func TestExists(t *testing.T) {
	testcases := []struct {
		a      string
		b      string
		val    string
		should bool
	}{
		{"a", "", "a", true},
		{"", "b", "b", true},
		{"", "", "", true},

		{"a", "b", "", true},

		{"a", "", "", false},
		{"", "b", "", false},
		{"", "", "foo", false},
	}

	for _, tc := range testcases {
		if val := PickTheExistedOne(tc.a, tc.b); (tc.val == val) != tc.should {
			t.<PERSON><PERSON>("PickTheExistedOne(\"%v\", \"%v\")=\"%s\" should be %v, but not, got %v", tc.a, tc.b, tc.val, tc.should, val)
		}
	}
}
