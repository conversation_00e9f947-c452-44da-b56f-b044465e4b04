package util

import (
	"fmt"
	"net/url"
)

func BuildURL(domain string, params map[string]any) (string, error) {
	u, err := url.Parse(domain)
	if err != nil {
		return "", err
	}

	values := u.Query()

	for k, v := range params {
		switch v := v.(type) {
		case []string:
			for _, vv := range v {
				values.Add(k, vv)
			}

		default:
			values.Set(k, fmt.Sprintf("%v", v))
		}
	}

	u.RawQuery = values.Encode()

	return u.String(), nil
}

// func BuildURLWithKV(domain string, kwargs ...any) (string, error) {
// 	params, err := BuildArgsWithKArgs(kwargs...)
// 	if err != nil {
// 		return "", err
// 	}

// 	return BuildURL(domain, params)
// }

// func BuildArgsWithKArgs(kwargs ...any) (map[string]any, error) {
// 	if len(kwargs)%2 != 0 {
// 		return nil, fmt.Errorf("kwargs should be even or zero length, kwargs: %v", kwargs)
// 	}

// 	params := make(map[string]any, 0)
// 	for n, m := 0, len(kwargs)/2; n < m; n++ {
// 		idx := n * 2
// 		key := kwargs[idx]
// 		val := kwargs[idx+1]

// 		if sKey, ok := key.(string); ok {
// 			params[sKey] = val
// 		}

// 	}
// 	return params, nil
// }
