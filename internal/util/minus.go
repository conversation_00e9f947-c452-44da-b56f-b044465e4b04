package util

func DnspodMinus(a, b []uint64) []uint64 {
	var inter []uint64
	mp := make(map[uint64]bool)

	for _, s := range a {
		if _, ok := mp[s]; !ok {
			mp[s] = true
		}
	}

	for _, s := range b {
		delete(mp, s)
	}

	for k := range mp {
		inter = append(inter, k)
	}
	return inter
}

func StringMinus(a, b []string) []string {
	var inter []string
	mp := make(map[string]bool)

	for _, s := range a {
		if _, ok := mp[s]; !ok {
			mp[s] = true
		}
	}

	for _, s := range b {
		delete(mp, s)
	}

	for k := range mp {
		inter = append(inter, k)
	}
	return inter
}
