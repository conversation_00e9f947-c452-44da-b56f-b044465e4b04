package category

import (
	"encoding/json"
	"regexp"
	"strings"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
)

var (
	rule = regexp.MustCompile(
		`^(?<Product>[a-z0-9]+?)` +
			`(\.(?<Service>[a-zA-Z0-9]+))?` +
			`(\.(?<Module>[a-zA-Z0-9]+))*` +
			`(-(?<Pub>(global|official|asia|cn|tw|us|eu|h5)?))?` +
			`(-(?<Env>(rc|ptr|pre|prod|test|dev|staging|gray|dev|qa|cbt|obt|yace|audit)?))?` +
			`(-(?<Type>(ecs|machine|k8s|lb|phy|db|pg|mgo|mongo|vpc|eip|sg|redis)))?` +
			`(-(?<Vendor>[tvagquc][a-z]{2,3}))?` +
			`(-(?<Suffix>.*))?$`)
	groupNames = rule.SubexpNames()

	// {"dgameremake-gamesvr-39001-global-rc-vm-gva", "", "dgameremake-global"},
	dgameRemakeRule = regexp.MustCompile(
		`^(?<Product>dgameremake)` +
			`-(?<Service>[a-zA-Z0-9]+)` +
			`(-(?<Module>[0-9]+))?` +
			`-(?<Pub>(global|cn))` +
			`-(?<Env>(rc|ptr|pre|prod|test|dev|staging|gray|dev|qa|cbt|obt|yace|audit)?)` +
			`-(?<Type>(vm|ecs|machine|k8s|lb|phy|db|pg|mgo|mongo|vpc|eip|sg|redis))` +
			`-(?<Vendor>[tg][a-z]{2,3})$`,
	)
)

var adjustRules = []struct {
	key       string
	matchFunc func(*ResourceFields) bool
}{
	{
		"dota",
		func(rf *ResourceFields) bool {
			return rf.Product == "dgame" && rf.Pub == "cn"
		},
	},
	{
		"dgame",
		func(rf *ResourceFields) bool {
			return rf.Product == "dgame" && rf.Pub == "us"
		},
	},
	{
		"dgameremake-tw",
		func(rf *ResourceFields) bool {
			return rf.Product == "dgameremake" && rf.Pub == "tw"
		},
	},

	{
		"dgameremake-global",
		func(rf *ResourceFields) bool {
			return rf.Product == "dgameremake" && rf.Pub == "global"
		},
	},

	{
		"dgameremake-asia",
		func(rf *ResourceFields) bool {
			return rf.Product == "dgameremake" && rf.Pub == "asia"
		},
	},

	{
		"dgame-remake",
		func(rf *ResourceFields) bool {
			return rf.Product == "dgameremake" && rf.Pub == "h5"
		},
	},

	{
		"igame-ptr",
		func(rf *ResourceFields) bool {
			return rf.Product == "igame" && strings.HasPrefix(rf.Suffix, "ptr-")
		},
	},

	{
		"igame-test",
		func(rf *ResourceFields) bool {
			return rf.Product == "igame" && rf.Env == "test"
		},
	},
}

func MatchedProductV2(name string) string {
	resourceName := MatchResourceName(name)
	if resourceName == nil {
		return ""
	}

	for _, rule := range adjustRules {
		if rule.matchFunc(resourceName) {
			return rule.key
		}
	}

	return resourceName.Product
}

type ResourceFields struct {
	Product string
	Service string
	Module  string
	Env     string
	Pub     string
	Type    string
	Vendor  string
	Suffix  string
}

func lastPartMatched(s string, sep string, matchedPattern string) bool {
	parts := strings.Split(s, sep)
	lastPart := parts[len(parts)-1]

	if matched, err := regexp.MatchString(matchedPattern, lastPart); err == nil && matched {
		return true
	}

	return false
}

func MatchResourceName(s string) *ResourceFields {
	if strings.HasPrefix(s, "dgameremake-") && !strings.Contains(s, ".") {
		//  寻找字符串中最后一个被-分割的字符串
		parts := strings.Split(s, "-")
		lastPart := parts[len(parts)-1]

		if isGcp := lastPartMatched(lastPart, ".", `(t|g)va`); isGcp {
			return MatchDgameRemakeGCPResourceName(s)
		}
	}

	matches := rule.FindStringSubmatch(s)
	if matches == nil {
		return nil
	}

	attr := fields.NewFields()
	for _, name := range groupNames {
		if index := rule.SubexpIndex(name); index > -1 {
			attr.Set(name, matches[index])
		}
	}

	resource := &ResourceFields{}
	if bs, err := json.Marshal(attr); err == nil {
		if err = json.Unmarshal(bs, resource); err == nil {
			return resource
		}
	}

	return nil
}

func MatchDgameRemakeGCPResourceName(s string) *ResourceFields {
	matches := dgameRemakeRule.FindStringSubmatch(s)
	if matches == nil {
		return nil
	}

	attr := fields.NewFields()
	for _, name := range groupNames {
		if index := dgameRemakeRule.SubexpIndex(name); index > -1 {
			attr.Set(name, matches[index])
		}
	}

	resource := &ResourceFields{}
	if bs, err := json.Marshal(attr); err == nil {
		if err = json.Unmarshal(bs, resource); err == nil {
			return resource
		}
	}

	return nil
}
