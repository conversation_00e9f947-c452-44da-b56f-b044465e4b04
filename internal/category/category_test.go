package category

import "testing"

func TestCategory<PERSON>ey(t *testing.T) {
	testcases := []struct {
		input      string
		gcpProject string
		expect     string
	}{
		{"sdk-test-001", "steel-spirit-232323", "sdk"},
		{"solarland-eks-aws-dev-eks-node-aws-dev-Node", "", "solarland"},
		{"gke-sdk-test-1-default-pool-02158499-vt7q", "steel-spirit-233703", "sdk"},
		{"gke-gsg-ds-global-gke-000-pool--1-new-7abe5af1-6bjq", "farlight-model", "farlight-model"},
		{"tsh-pub-xgame-officalweb-0001", "", "pub"},

		{"cheap-node-15-1-0-1-asia-107", "solarland-ai", "devops"},

		{"gva-samo-global-gate-0001", "farlight-samo-363102", "samo"},
		{"gke-gva-hgame-tiyan2-default-pool-0d00e9ab-8nkd", "gcp-tiyan", "hgame"},

		{"tva-devops-dev-privatecloud-0001", "", "devops"},

		{"igame-staging-ptr-10.15.8.129", "", "igame-ptr"},
		{"igame-10.15.67.255", "", "igame"},
		{"tsh-igame-prod-official-btcheck-10.74.0.146", "", "igame-official"},

		{"dgameremake-jumper-global-prod-ecs-tva", "lilith-dgame", "dgameremake-global"},

		{"dgameremake-jumper-global-prod-ecs-tva", "", "dgameremake-global"},
		{"dgameremake-global-global-rc-vm-gva", "", "dgameremake-global"},
		{"dgameremake-gamesvr-39001-global-rc-vm-gva", "", "dgameremake-global"},
		{"gke-dgameremake-global-p-default-pool-864c7e2d-wf5f", "", "dgameremake-global"},

		{"dgameremake.jumper-tw-prod-ecs-ctw", "", "dgameremake-tw"},

		{"tsh-dgameremake-game-svr-21821", "", "dgame-remake"},
		{"tsh-dgameremake-official-game-svr-21831", "", "dgame-remake"},

		{"dgame.gamesvr.2003-cn-prod-ecs-ubj", "", "dota"},
		{"tbj-dgame-cn-prod-dir-0001", "", "dota"},
		{"tva-dgame-global-game-18806", "", "dgame"},

		{"tva-xgame-prod2-ack-0001", "", "xgame-global"},
		{"tsh-xgame-prod-k8s-10.81.4.24aabc", "", "xgame-global"},
		{"gty-xgame-global-qa-0001", "xgame-1234", "xgame-global"},
		{"gfk-xgame-gray-haproxy-0002", "xgame-323208", "xgame-global"},

		{"Farlight84-OPS", "", "farlight84"},
		{"ubj_lilith_pre_register_002", "", ""},
		{"ugz_sdk_001", "", "sdk"},
		{"tsh-plat2farlight84-cn-k8s10.136.111.159node", "", "plat"},

		{"tva-ptslg-global-game-0001", "", "ptslg"},

		{"vsh-hdp-cn-gateway-0001", "", "hdp"},

		{"thz-ad-cnk8s-airflow10.96.13.84", "", "ad"},
		{"tsh-satanpit-pvt-mongo-0001", "", "satanpit"},

		{"qsh_hgame_im_proxy_001", "", "hgame"},
		{"qca_sdk_proxy", "", "sdk"},
	}

	for _, tc := range testcases {
		if got := CategoryKey(tc.input, tc.gcpProject); got != tc.expect {
			t.Errorf("CategoryKey(%s) = %s, expect %s", tc.input, got, tc.expect)
		}
	}
}
