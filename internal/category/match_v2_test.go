package category

import (
	"fmt"
	"strings"
	"testing"
)

func TestMatchV2(t *testing.T) {
	testcases := []struct {
		name string
		want string
	}{
		{"dgame.gamesvr.2003-cn-prod-ecs-ubj", "dota"},
		{"dgame.gamesvr-cn-prod-ecs-ubj-6719", "dota"},

		{"dgameremake.log-asia-prod-redis-tsg", "dgameremake-asia"},
		{"dgameremake.jumper-prod-ecs-ctw", "dgameremake"},
		{"dgameremake.jumper-cn-prod-ecs-ctw", "dgameremake"},
		{"dgameremake.jumper-tw-prod-ecs-ctw", "dgameremake-tw"},

		{"dgameremake-global-global-rc-vm-gva", "dgameremake-global"},
		{"dgameremake-gamesvr-39001-global-rc-vm-gva", "dgameremake-global"},
		{"dgameremake-jumper-global-prod-ecs-tva", "dgameremake-global"},

		{"mona-cn-audit-ecs-tsh", "mona"},

		{"igame-staging-ptr-10.15.8.129", "igame-ptr"},
		{"igame-prod-ptr-10.15.0.215", "igame-ptr"},
		{"igame-10.15.67.255", "igame"},
		{"igame-test-ftest-10.15.17.196", "igame-test"},

		{"roc-lostlandsvr-global-prod-ecs", "roc"},
	}

	for _, tc := range testcases {
		got := MatchedProductV2(tc.name)
		if got != tc.want {
			t.Errorf("matchedProduct(%s), %s != %s\n", tc.name, got, tc.want)
		} else {
			t.Logf("matchedProduct(%s) = %s want %s\n", tc.name, got, tc.want)
		}
	}
}

func TestMatchResourceName(t *testing.T) {
	testcases := []struct {
		name     string
		wantFunc func(*ResourceFields) error
	}{
		{
			"dgameremake-jumper-global-prod-ecs-tva",
			func(rf *ResourceFields) error {
				if rf.Product != "dgameremake" {
					return fmt.Errorf("product miss match: %s != dgameremake", rf.Product)
				}

				if rf.Service != "jumper" {
					return fmt.Errorf("pub miss match: %s != global", rf.Pub)
				}

				if rf.Pub != "global" {
					return fmt.Errorf("pub miss match: %s != global", rf.Pub)
				}

				if rf.Env != "prod" {
					return fmt.Errorf("env miss match: %s != prod", rf.Env)
				}

				if rf.Vendor != "tva" {
					return fmt.Errorf("vendor miss match: %s != gva", rf.Vendor)
				}

				return nil
			},
		},
		{
			"dgameremake-global-global-rc-vm-gva",
			func(rf *ResourceFields) error {
				if rf.Product != "dgameremake" {
					return fmt.Errorf("product miss match: %s != dgameremake", rf.Product)
				}

				if rf.Service != "global" {
					return fmt.Errorf("pub miss match: %s != global", rf.Pub)
				}

				if rf.Pub != "global" {
					return fmt.Errorf("pub miss match: %s != global", rf.Pub)
				}

				if rf.Vendor != "gva" {
					return fmt.Errorf("vendor miss match: %s != gva", rf.Vendor)
				}

				return nil
			},
		},

		{
			"dgameremake-gamesvr-39001-global-rc-vm-gva",
			func(rf *ResourceFields) error {
				if rf.Product != "dgameremake" {
					return fmt.Errorf("product miss match: %s != dgameremake", rf.Product)
				}

				if rf.Pub != "global" {
					return fmt.Errorf("pub miss match: %s != global", rf.Pub)
				}

				if rf.Vendor != "gva" {
					return fmt.Errorf("vendor miss match: %s != gva", rf.Vendor)
				}

				return nil
			},
		},

		{
			"dgameremake.log-asia-prod-redis-tsg",
			func(rf *ResourceFields) error {
				if rf.Type != "redis" {
					return fmt.Errorf("type miss match: %s != redis", rf.Type)
				}
				return nil
			},
		},

		{
			"dgame.gamesvr.2003-cn-prod-ecs-ubj",
			func(rf *ResourceFields) error {
				if rf.Product != "dgame" {
					return fmt.Errorf("product: %s", rf.Product)
				}

				if rf.Service != "gamesvr" {
					return fmt.Errorf("service want %s, but got %s", "gamesvr", rf.Service)
				}

				if rf.Module != "2003" {
					return fmt.Errorf("module want %s, but got %s", "2003", rf.Service)
				}

				if rf.Pub != "cn" {
					return fmt.Errorf("pub: %s != cn", rf.Pub)
				}

				if rf.Env != "prod" {
					return fmt.Errorf("env: %s != prod", rf.Env)
				}

				if rf.Type != "ecs" {
					return fmt.Errorf("type: %s != ecs", rf.Type)
				}

				if rf.Vendor != "ubj" {
					return fmt.Errorf("vendor: %s != ubj", rf.Vendor)
				}

				return nil
			},
		},

		{
			"dgameremake-asia-prod-ecs-gsg-proxy-mtvf",
			func(rf *ResourceFields) error {
				if rf.Product != "dgameremake" {
					return fmt.Errorf("product: %s", rf.Product)
				}

				if rf.Pub != "asia" {
					return fmt.Errorf("pub: %s != asia", rf.Pub)
				}

				if rf.Env != "prod" {
					return fmt.Errorf("env: %s != prod", rf.Env)
				}

				if rf.Vendor != "gsg" {
					return fmt.Errorf("vendor: %s != gsg", rf.Vendor)
				}

				if rf.Suffix != "proxy-mtvf" {
					return fmt.Errorf("suffix: %s != proxy-mtvf", rf.Suffix)
				}

				return nil
			},
		},

		{
			"dgame.gamesvr-cn-prod-ecs-ubj-6719",
			func(rf *ResourceFields) error {
				if rf.Product != "dgame" {
					return fmt.Errorf("product: %s", rf.Product)
				}

				if rf.Service != "gamesvr" {
					return fmt.Errorf("service: %s", rf.Service)
				}

				if rf.Pub != "cn" {
					return fmt.Errorf("pub miss match: %s", rf.Pub)
				}

				if rf.Type != "ecs" {
					return fmt.Errorf("type miss match: %s", rf.Type)
				}

				if rf.Suffix != "6719" {
					return fmt.Errorf("suffix miss match: %s", rf.Suffix)
				}

				return nil
			},
		},

		{
			"mona-cn-audit-ecs-tsh",
			func(rf *ResourceFields) error {
				if rf.Product != "mona" {
					return fmt.Errorf("product: %s != mona", rf.Product)
				}

				if rf.Pub != "cn" {
					return fmt.Errorf("pub: %s != cn", rf.Pub)
				}

				if rf.Env != "audit" {
					return fmt.Errorf("env miss match: %+v", rf.Env)
				}

				return nil
			},
		},

		{
			"igame-staging-ptr-10.15.8.129",
			func(rf *ResourceFields) error {
				if rf.Product != "igame" {
					return fmt.Errorf("product: %s", rf.Product)
				}

				if rf.Env != "staging" {
					return fmt.Errorf("env: %s", rf.Env)
				}

				if !strings.HasPrefix(rf.Suffix, "ptr") {
					return fmt.Errorf("suffix not startswith ptr, but %s", rf.Suffix)
				}

				return nil
			},
		},

		{
			"igame-test-ftest-10.15.17.196",
			func(rf *ResourceFields) error {
				if rf.Product != "igame" {
					return fmt.Errorf("missmatched product %s", rf.Product)
				}

				if rf.Env != "test" {
					return fmt.Errorf("missmatched env %s", rf.Env)
				}

				return nil
			},
		},

		{
			"igame-10.15.8.129",
			func(rf *ResourceFields) error {
				if rf.Product != "igame" {
					return fmt.Errorf("missmatched product %s", rf.Product)
				}

				if rf.Suffix != "10.15.8.129" {
					return fmt.Errorf("missmatched suffix %s", rf.Suffix)
				}

				return nil
			},
		},
	}

	for _, tc := range testcases {
		got := MatchResourceName(tc.name)
		if got == nil {
			t.Errorf("%s not matched\n", tc.name)
		} else {
			if err := tc.wantFunc(got); err != nil {
				t.Errorf("matchedProduct(%s) faield, %v, %+v\n", tc.name, err, got)
			} else {
				t.Logf("matchedProduct(%s) = %+v\n", tc.name, got)
			}
		}
	}
}
