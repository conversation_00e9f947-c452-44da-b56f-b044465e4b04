package category

import "testing"

func TestGCPMatch(t *testing.T) {
	testcases := []struct {
		name string
		want string
	}{
		{"farlight-hadoop", "farlight-hadoop"},
		{"farlight-model", "farlight-model"},
		{"solarland-296802", "solarland"},

		{"solarland-ai", "devops"},
		{"rdcenter-ai", "devops"},

		{"lilith-rok", "rok"},
		{"lilith-rokpayprediction-277305", "rok"},

		{"igame-323906", "igame"},
		{"faxing-330502", "faxing"},
		{"xgame-323208", "xgame-global"},
	}

	for _, tc := range testcases {
		got := MatchedGCPProduct(tc.name)
		if got != tc.want {
			t.<PERSON><PERSON><PERSON>("matchedProduct(%s) = %s want %s", tc.name, got, tc.want)
		} else {
			t.Logf("matchedProduct(%s) = %s want %s", tc.name, got, tc.want)
		}
	}
}
