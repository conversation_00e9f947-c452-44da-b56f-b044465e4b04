package category

import (
	"strings"
	"sync"

	"gitlab.lilithgame.com/yunwei/pkg/wrap"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
)

var (
	productsList = make(map[string]int, 0)
	onceLoad     = sync.OnceFunc(loadAllProducts)
)

func loadAllProducts() {
	resp := wrap.Unwrap(restclient.ListAll[models.Product]())

	for _, product := range resp.Results {
		// fmt.Printf("%2d %s %s\n", product.ID, product.Name, product.ExternalID)

		keywords := strings.Split(product.ExternalID, ",")
		for _, keyword := range keywords {
			if keyword != "" {
				productsList[strings.ToLower(keyword)] = product.GetID()
			}
		}
	}
}

func GetMatchedProductID(keyword string) *int {
	onceLoad()

	if id, ok := productsList[strings.ToLower(keyword)]; ok {
		return &id
	}

	return nil
}
