package category

import (
	"fmt"
	"regexp"
	"strings"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
)

func Category(name string, gcpProject string) *int {
	productKey := CategoryKey(name, gcpProject)
	if productID := GetMatchedProductID(productKey); productID != nil {
		return productID
	}

	return nil
}

func CategoryKey(name string, gcpProject string) string {
	var productKey string

	nodeName := ResolveNodeName(name)
	productKey = MatchedProduct(nodeName)

	if productKey == "" && gcpProject != "" {
		productKey = MatchedGCPProduct(gcpProject)
	}

	if productKey == "" {
		productKey = MatchedProductV2(name)
	}

	return productKey
}

func ResolveNodeName(name string) string {
	key := name

	if strings.HasPrefix(key, "k8s-log") {
		if clusterName := ResolveClusterNameFromK8sLog(key); clusterName != "" {
			key = clusterName
		}
	} else if strings.Contains(key, "-k8s-for-cs-") {
		pieces := strings.Split(key, "-")
		if clusterId := pieces[len(pieces)-1]; clusterId != "" {
			if clusterName := ResolveClusterNameByID(clusterId); clusterName != "" {
				key = clusterName
			}
		}
	}

	return key
}

var k8sLogProject = regexp.MustCompile(`^k8s-log(-custom)?(-(?P<clusterId>[0-9a-zA-Z]+))?-?(?P<cluster>[tavugc]{1}[a-z]{2}(-[a-zA-Z0-9]+)+)?$`)

func ResolveClusterNameFromK8sLog(name string) string {
	key := name

	matchs := k8sLogProject.FindStringSubmatch(key)
	if len(matchs) == 0 {
		return key
	}

	var s struct {
		clusterId   string
		clusterName string
	}

	for _, name := range k8sLogProject.SubexpNames() {
		if idx := k8sLogProject.SubexpIndex(name); idx > -1 {
			switch name {
			case "clusterId":
				s.clusterId = matchs[idx]
			case "cluster":
				s.clusterName = matchs[idx]
			}
		}
	}

	if s.clusterName != "" {
		return s.clusterName
	}

	if clusterId := s.clusterId; clusterId != "" {
		if clusterName := ResolveClusterNameByID(clusterId); clusterName != "" {
			key = clusterName
		}
	}
	return key
}

func ResolveClusterNameByID(clusterID string) string {
	var km models.K8SCluster

	has, err := restclient.Find(&km, fields.NamedField("cluster_id", clusterID))
	if err != nil {
		fmt.Println(err)
	}
	if err != nil || !has {
		return ""
	}

	return km.ClusterName
}
