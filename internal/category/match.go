package category

import (
	"fmt"
	"regexp"
)

const VendorPrefixRexp = "qtgavuc"

// NOTE: the order of rules matters, refer https://pkg.go.dev/regexp/syntax#hdr-Syntax
var rules = []struct {
	key string
	exp *regexp.Regexp
}{
	{"dgameremake-global", regexp.MustCompile("gke-dgameremake-global-.*$")},
	{"dgame-remake", regexp.MustCompile(`^(?P<prefix>[taguv][\w]{2})-dgame-?remake(?P<suffix>-[^-]+)+$`)},
	{"dota", regexp.MustCompile(`^dgame_gs_\d+$`)},
	{"dota", regexp.MustCompile(`^dota.*$`)},
	{"dota", regexp.MustCompile(fmt.Sprintf(`^[%s][a-z]{2}-dgame-cn(-[^-]+)*$`, VendorPrefixRexp))},
	{"dgame", regexp.MustCompile(fmt.Sprintf(`^[%s][a-z]{2}-dgame-global(-\w+)*$`, VendorPrefixRexp))},
	// {"gke-dgameremake-global-p-default-pool-864c7e2d-wf5f", "", "dgameremake-global"},

	{"dota_tw", regexp.MustCompile(`^tw_dgame_.*$`)},

	{"devops", regexp.MustCompile(fmt.Sprintf(`^([%s][a-z]{2})-devops(-[\w]+)+$`, VendorPrefixRexp))},

	{"devtest", regexp.MustCompile(`^([taguv][\w]{2})-devtest(-[^-]+)+$`)},
	{"solarland", regexp.MustCompile(`^solarland-eks-.*$`)},

	{"sdk", regexp.MustCompile(`^sdk-test-\d+$`)},
	{"sdk", regexp.MustCompile(`^(gke-)?sdk-.*$`)},
	{"sdk", regexp.MustCompile(`^sdk(_[^_]*)+$`)},

	// ubj_union_sdk_003 ugz_sdk_001 qsh_sdk_busi_proxy_001
	{"sdk", regexp.MustCompile(`^[qu][a-z]{2}(_[a-z]+)*_sdk(_[a-zA-Z0-9]+)+$`)},

	{"forum", regexp.MustCompile(fmt.Sprintf(`^(?P<prefix>[%s]\w{2})[_-]forum(_[^_]+)+$`, VendorPrefixRexp))},

	{"farlight84", regexp.MustCompile(`^sng-ssm-.*$`)},
	// Farlight84-OPS
	{"farlight84", regexp.MustCompile(`^(?i)farlight84-.*$`)},

	// gke-gva-hgame-tiyan2-default-pool-0d00e9ab-8nkd
	{"hgame", regexp.MustCompile(fmt.Sprintf(`^gke-(?P<prefix>[%s]\w{2})-hgame(-[^-]+)+$`, VendorPrefixRexp))},
	{"hgame", regexp.MustCompile(fmt.Sprintf(`^(?P<prefix>[%s]\w{2})-hgame(-[^-]+)+$`, VendorPrefixRexp))},
	{"hdgame", regexp.MustCompile(fmt.Sprintf(`^(?P<prefix>[%s]\w{2})-hdgame(-[^-]+)+$`, VendorPrefixRexp))},
	// qsh_hgame_im_proxy_001
	{"hgame", regexp.MustCompile(fmt.Sprintf(`^(?P<prefix>[%s]\w{2})_hgame(_[^_]+)+$`, VendorPrefixRexp))},

	// ugz_sgame_chat_proxy_2
	{"sgame", regexp.MustCompile(fmt.Sprintf(`^(?P<prefix>[%s][a-z]{2})_sgame(_[^-]+)+$`, VendorPrefixRexp))},
	{"sgame", regexp.MustCompile(fmt.Sprintf(`^(?P<prefix>[%s]\w{2})-sgame(-[^-]+)+$`, VendorPrefixRexp))},

	// Typo here, should be "officialweb"
	{"xgame", regexp.MustCompile(`^(?P<prefix>[taguv][\w]{2})-xgame-official(?P<suffix>-[^-]+)+$`)},
	{"xgame-global", regexp.MustCompile(`^(?P<prefix>[taguv][\w]{2})-xgame(?P<suffix>-[^-]+)+$`)},
	{"pub-qa", regexp.MustCompile(`^([taguv][\w]{2})-pub-qa(-[^-]+)+$`)},
	{"pub", regexp.MustCompile(`^(?P<prefix>[taguv][\w]{2})-pub-(xgame-offici?al)[^-]*(?P<suffix>-[^-]+)+$`)},
	{"pub", regexp.MustCompile(`^(?P<prefix>[taguv][\w]{2})-pub-([a-zA-Z]+)[^-]*(?P<suffix>-[^-]+)+$`)},

	// qbj_roc_proxy_002
	{"roc", regexp.MustCompile(`^(?P<prefix>[qu][a-z]{2,5})_(ro[ck])(?P<suffix>_[^_]+)+$`)},
	{"roc", regexp.MustCompile(`^(?P<prefix>[taguv][\w]{2})-(ro[ck](-cn|-test)?)(?P<suffix>-[^-]+)+$`)},

	{"wgame", regexp.MustCompile(`^(?P<prefix>[taguv][a-z]{2})-(?P<product>wgame)(?P<suffix>-[^-]+)+$`)},
	{"wgame2", regexp.MustCompile(`^(?P<prefix>[taguv][a-z]{2})-(?P<product>wgame2)(?P<suffix>-[^-]+)+$`)},

	// {"", regexp.MustCompile(`^(?P<prefix>[taguv][\w]{2})-(?P<product>\w{1,2}game2?)(?P<suffix>-[^-]+)+$`)},

	{"ad", regexp.MustCompile(`^(?P<prefix>[taguv][\w]{2})-(?P<product>ad)(?P<suffix>-[^-]+)+$`)},
	{"dap", regexp.MustCompile(`^(gke-)?(?P<prefix>[taguv][\w]{2})-dap(?P<suffix>-{1,2}[^-]+)+$`)},

	{"hdp", regexp.MustCompile(`^(gke-)?(?P<prefix>[taguv][\w]{2})-hdp(?P<suffix>-[^-]+)+$`)},
	{"", regexp.MustCompile(`^(gke-)?(?P<prefix>[taguv][\w]{2})-ds(?P<suffix>-{1,2}[^-]+)+$`)},

	// {"igame-test", regexp.MustCompile(`^(gke-)?(?P<prefix>[taguv][\w]{2})-igame-test(?P<suffix>-{1,2}[^-]+)+$`)},
	// {"igame-ptr", regexp.MustCompile(`^(gke-)?(?P<prefix>[taguv][\w]{2})-igame-ptr(?P<suffix>-{1,2}[^-]+)+$`),
	{"igame-official", regexp.MustCompile(`^(?P<prefix>[tagv][\w]{2})-igame-prod-official(?P<suffix>-[^-]+)+$`)},
	{"igame", regexp.MustCompile(`^(gke-)?(?P<prefix>[taguv][\w]{2})-igame(?P<suffix>-{1,2}[^-]+)+$`)},

	{"algorithm", regexp.MustCompile(`^(?P<prefix>[taguv][\w]{2})-algorithm(?P<suffix>-[^-]+)+$`)},

	{"", regexp.MustCompile(`^(?P<prefix>[taguv][\w]{2})-platform(-[^-]*)+$`)},
	{"it", regexp.MustCompile(`^(?P<prefix>[taguv][a-z]{2})-platit-([^-]+)(-[^-]+)*$`)},
	{"plat", regexp.MustCompile(`^(?P<prefix>[taguv][\w]{2})-plat2?[a-zA-Z0-9]*(-[^-]*)+$`)},

	{"samo", regexp.MustCompile(`^(?P<prefix>[taguv][\w]{2})-samo(?P<suffix>-[^-]+)+$`)},

	{"mona", regexp.MustCompile(`^(?P<prefix>[taguv][\w]{2})-mona(?P<suffix>-[^-]+)+$`)},
	{"mona", regexp.MustCompile(`^(?P<prefix>[taguv][\w]{2})-trinity(?P<suffix>-[^-]+)+$`)},

	{"avatar", regexp.MustCompile(`^(?P<prefix>[taguv][\w]{2})-avatar(?P<suffix>-[^-]+)+$`)},

	{"ptslg", regexp.MustCompile(`^(?P<prefix>[taguv][\w]{2})-ptslg(?P<suffix>-[^-]+)+$`)},

	{"satanpit", regexp.MustCompile(`^(?P<prefix>[taguv][\w]{2})-satanpit(?P<suffix>-[^-]+)+$`)},

	// {"tca_rok_proxy_4bj_002", "rok"},
	{"", regexp.MustCompile(`^(?:[tc][a-z]{2})_(?<product>[a-z]+)(_[a-z0-9]+)*$`)},
}

func MatchedProduct(name string) string {
	for _, rule := range rules {
		if matches := rule.exp.FindStringSubmatch(name); matches != nil {
			if rule.key == "" {
				if productIndex := rule.exp.SubexpIndex("product"); productIndex != -1 {
					return matches[productIndex]
				}
			}
			return rule.key
		}
	}

	return ""
}
