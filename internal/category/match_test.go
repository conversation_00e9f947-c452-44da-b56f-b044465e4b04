package category

import "testing"

func TestMatch(t *testing.T) {
	testcases := []struct {
		name string
		want string
	}{
		{"tsh-dgame-remake-game-svr-21745", "dgame-remake"},
		{"tsh-dgameremake-game-svr-21745", "dgame-remake"},

		{"tva-devops-dev-privatecloud-0001", "devops"},

		{"tsh-algorithm-cn-k8s", "algorithm"},

		{"tva-xgame-prod2-k8s-10.145.20.41aabc", "xgame-global"},
		{"tsh-xgame-prod-k8s-10.81.4.24aabc", "xgame-global"},
		{"tfk-xgame-gray-k8s-10.152.42.1aabc", "xgame-global"},
		{"tsh-xgame-official-jumper-0001", "xgame"},
		{"tsh-xgame-official-sdktest-0001", "xgame"},

		{"gsg-hdp-global-tableauserver-0006", "hdp"},
		{"gsg-dap-global-gke-0001-new", "dap"},

		{"gke-gsg-hdp-global-gke-0-trino-pool-1-54fccacf-fj9f", "hdp"},
		{"gke-gsg-ds-global-gke-000-pool--1-new-7abe5af1-mnq9", ""},
		{"gke-sdk-test-1-default-pool-02158499-vt7q", "sdk"},

		{"gtw-plat-sdk-proxy-0002", "plat"},
		{"gtw-plat--logrecv-proxy-01", "plat"},

		{"vsh-plat2public-qa-jumper-0001", "plat"},
		{"vsh-plat2public-qa-k8snode-8h2mps", "plat"},
		{"aor-plat2public-global-k8s-0001", "plat"},
		{"tsh-platit-AGW-001", "it"},
		{"tsh-platform-cn-etcd-003", ""},
		{"tsh-plat2farlight84-cn-k8s10.136.111.159node", "plat"},

		{"tsh-roc-cn-lostlandsvr-536", "roc"},
		{"tsg-roc-test-jumper-0001", "roc"},
		{"tva-rok-k8s-10.145.69.156-lgim", "roc"},
		{"tsh-rok-cn-master-0001", "roc"},

		{"tsg-sgame-global-k8sworker-10.128.107.252-autonode", "sgame"},

		{"tsh-wgame2-dev-jump-0001", "wgame2"},

		{"tsh-hgame-cn-10.137.212.56work8c16g", "hgame"},
		{"tsh-hdgame-cn-10.137.212.3-work8c16g", "hdgame"},
		{"gke-gva-hgame-tiyan2-default-pool-0d00e9ab-8nkd", "hgame"},

		{"tva-samo-global-10.13.78.92-seasongs", "samo"},
		{"tva-samo-k8s-10.13.3.5-platform", "samo"},
		{"gva-samo-global-gate-0001", "samo"},

		{"tsh-pub-xgame-officalweb-0001", "pub"},
		{"tsg-pub-xgame-officialsitetw-0001", "pub"},
		{"tsg-pub-qa-service-0001", "pub-qa"},

		{"gke-gva-igame-test-cbt-k-default-pool-e1dfddcd-vqzx", "igame"},
		{"gke-gva-igame-ptrrc-k8sc-default-pool-83f45e3f-zw89", "igame"},

		{"gke-gsg-dap-global-gke-data-collector-c76c45ac-bs68", "dap"},
		{"gsg-dap-global-bhzookeeper-0002", "dap"},
		{"tsh-ad-cn-starrocks-10.137.104.65-k8s", "ad"},

		{"tsh-trinity-qa-k8s-0001", "mona"},

		{"dota兑换码服务器", "dota"},
		{"tw_dgame_充值服务", "dota_tw"},
		{"dgame_gs_6711", "dota"},

		{"tsh-devtest-dev-hdpetl-0001", "devtest"},

		{"solarland-eks-sng-eks-node-Node", "solarland"},
		{"solarland-eks-aws-dev-eks-node-aws-dev-Node", "solarland"},

		{"sdk-test-001", "sdk"},
		{"sdk_proxy_001", "sdk"},

		{"sng-ssm-002", "farlight84"},
		{"tsh-dgameremake-game-svr-21821", "dgame-remake"},
		{"tva-pub-common-k8s-0001", "pub"},

		{"tsh-avatar-prod-k8scluster-0001", "avatar"},

		{"ugz_sgame_chat_proxy_2", "sgame"},
		{"ulagos_roc_chat_proxy", "roc"},
		{"utb_roc_proxy_001", "roc"},
		{"ubj_union_sdk_003", "sdk"},
		{"ugz_sdk_001", "sdk"},
		{"ubj_lilith_pre_register_001", ""},

		{"cheap-node-15-1-0-1-asia-107", ""},

		{"qbj_roc_proxy_002", "roc"},
		{"qca_sdk_proxy", "sdk"},
		{"qsh_sdk_busi_proxy_001", "sdk"},

		{"qsh_hgame_im_proxy_001", "hgame"},
		{"qca_forum_proxy", "forum"},
	}

	for _, tc := range testcases {
		got := MatchedProduct(tc.name)
		if got != tc.want {
			t.Errorf("matchedProduct(%s) = %s want %s\n", tc.name, got, tc.want)
			// } else {
			// 	t.Logf("matchedProduct(%s) = %s want %s\n", tc.name, got, tc.want)
		}
	}
}

func TestCapital(t *testing.T) {
	testcases := []struct {
		name string
		want string
	}{
		{"csh_hgame_scp_proxy", "hgame"},
		{"cda_roc_proxy_4tw_002", "roc"},
		{"ckr_sdk_proxy", "sdk"},
		{"tca_rok_proxy_4bj_002", "rok"},
		{"csh_ad_proxy_002", "ad"},
	}

	for _, tc := range testcases {
		got := MatchedProduct(tc.name)
		if got != tc.want {
			t.Errorf("Capital(%s) = %s want %s\n", tc.name, got, tc.want)
		} else {
			t.Logf("Capital(%s) = %s want %s", tc.name, got, tc.want)
		}
	}
}
