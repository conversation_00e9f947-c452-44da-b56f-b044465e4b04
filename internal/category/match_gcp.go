package category

import "regexp"

var gcpRules = []struct {
	key string
	exp *regexp.Regexp
}{
	{"", regexp.MustCompile(`^(?<product>farlight-[a-zA-Z]+)`)},
	{"devops", regexp.MustCompile(`^(?<product>[a-z]+)-ai$`)},

	{"solarland", regexp.MustCompile(`solarland-\w+`)},
	{"solarland", regexp.MustCompile(`^lilith-solarland\w*(-\w+)*$`)},

	{"rok", regexp.MustCompile(`^lilith-rok\w*(-\w+)*$`)},
	{"samo", regexp.MustCompile(`^lilith-samo\w*(-\w+)*$`)},

	{"avatar", regexp.MustCompile(`^lilith-avatar\w*(-\w+)*$`)},
	{"avatar", regexp.MustCompile(`^avatar-\w+`)},

	{"sgame", regexp.MustCompile(`^lilith-sgame\w*(-\w+)*$`)},
	{"sgame", regexp.MustCompile(`^sgame-\w+`)},

	{"dgameremake-global", regexp.MustCompile(`^lilith-dgame$`)},

	// {"wgame", regexp.MustCompile(`^lilith-wgame-\w+`)},
	// {"wgame", regexp.MustCompile(`^wgame-\w+`)},
	// {"wgame2", regexp.MustCompile(`^wgame2-\w+`)},
	{"", regexp.MustCompile(`^(:?lilith-)?(?<product>wgame2?)-\w+`)},

	{"igame", regexp.MustCompile(`^igame-\w+`)},
	{"xgame-global", regexp.MustCompile(`^xgame-\w+`)},
	{"faxing", regexp.MustCompile(`^faxing-\w+`)},
}

func MatchedGCPProduct(name string) string {
	for _, rule := range gcpRules {
		if matches := rule.exp.FindStringSubmatch(name); matches != nil {
			if rule.key == "" {
				if productIndex := rule.exp.SubexpIndex("product"); productIndex >= 0 {
					return matches[productIndex]
				}
			}
			return rule.key
		}
	}

	return ""
}
