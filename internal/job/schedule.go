package job

import (
	"fmt"
	"time"

	"github.com/aws/aws-sdk-go/service/ec2"
	"github.com/robfig/cron/v3"
	"gitlab.lilithgame.com/yunwei/pkg/logger"
	"google.golang.org/api/compute/v1"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/factory"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/task/ali/ack"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/task/aws/eks"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/task/gcp/gke"
	uks "gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/task/ucloud/k8s"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/task/volc/vks"
)

func JobStart() {
	// aliRegions, ALI_REG_MAP_FROM_CMDB, ALI_ZONE_MAP_FROM_CMDB := AliInit()
	awsRegions, AWS_REG_MAP_FROM_CMDB, AWS_ZONE_MAP_FROM_CMDB := AWSInit()
	gcpRegions, gcpZones, GCP_REG_MAP_FROM_CMDB, GCP_ZONE_MAP_FROM_CMDB := GCPInit()

	// 第一次同步立即执行(部分项目)
	SyncOnce(
		FilterRules(crontabs, WithStartup(true)),
		// aliRegions,
		awsRegions, gcpRegions,
		gcpZones,
		// ALI_REG_MAP_FROM_CMDB, ALI_ZONE_MAP_FROM_CMDB,
		AWS_REG_MAP_FROM_CMDB, AWS_ZONE_MAP_FROM_CMDB,
		GCP_REG_MAP_FROM_CMDB, GCP_ZONE_MAP_FROM_CMDB,
	)

	c := CronSync(
		crontabs,
		// aliRegions,
		awsRegions,
		gcpRegions,
		gcpZones,
		// ALI_REG_MAP_FROM_CMDB, ALI_ZONE_MAP_FROM_CMDB,
		AWS_REG_MAP_FROM_CMDB, AWS_ZONE_MAP_FROM_CMDB,
		GCP_REG_MAP_FROM_CMDB, GCP_ZONE_MAP_FROM_CMDB,
	)
	if c == nil {
		logger.Error("cron syncer is nil")
		return
	}
	// defer func() {
	// 	<-c.Stop().Done()
	// }()

	// aliyun blockdisk
	c.AddFunc("@every 2h", func() { AliyunBlockDiskSync(VendorFactory("aliyun")...) })

	// k8s cluster
	c.AddFunc("@hourly", func() { uks.SyncAll() })
	c.AddFunc("@hourly", func() { eks.SyncAll() })
	c.AddFunc("@hourly", func() { ack.SyncAll() })
	c.AddFunc("@hourly", func() { vks.SyncAll() })
	c.AddFunc("@hourly", func() { gke.SyncAll() })

	// clean outdated machine record
	const month = time.Hour * 24 * 30
	c.AddFunc("@midnight", func() {
		t := time.Now().Add(month * -1)
		CleanOutdatedMachineRecord(
			fields.ExternalStatusDeleted,
			fields.NumberField("updated_before", t.Unix()),
		)
	})

	c.Start()
}

// SyncOnce 立即同步一次
func SyncOnce(
	rules []Rule,
	// aliRegions []*alivpc.DescribeRegionsResponseBodyRegionsRegion,
	awsRegions []*ec2.Region,
	gcpRegions []*compute.Region,
	gcpZones []*compute.Zone,
	// aliRegionMap map[string]int, aliZoneMap map[string]int,
	awsRegionMap map[string]int, awsZoneMap map[string]int,
	gcpRegionMap map[string]int, gcpZoneMap map[string]int,
) {
	for _, rule := range rules {
		r := rule

		factoryKey := r.FactoryKey.String()

		switch key := r.FactoryKey; key.Vendor() {
		case "aliyun":
			// go AliDailySync(factoryKey, aliRegions, aliRegionMap, aliZoneMap)
			go AliDailySync(factoryKey)
			time.Sleep(time.Minute * 2)

		case "gcp":
			go GcpDailySync(factoryKey, gcpRegions, gcpZones, gcpRegionMap, gcpZoneMap)
			time.Sleep(time.Minute * 2)

		case "aws":
			go AwsDailySync(factoryKey, awsRegions, awsRegionMap, awsZoneMap)
			time.Sleep(time.Minute * 2)

		case "ucloud":
			go TencentSync(key.String())
			time.Sleep(time.Minute * 2)

		case "volc":
			go VolcSync([]factory.FactoryKeyType{key})
			time.Sleep(time.Minute * 2)

		default:
			logger.Warn(fmt.Sprintf("unknown factory '%s' to sync", rule.FactoryKey))
		}
	}
}

func NewCron(location ...string) *cron.Cron {
	opts := []cron.Option{
		cron.WithChain(cron.Recover(cron.DefaultLogger)),
	}

	// set time location
	if len(location) > 0 {
		if loc, err := time.LoadLocation(location[0]); err == nil {
			opts = append(opts, cron.WithLocation(loc))
		}
	}

	return cron.New(opts...)
}

func NewDefaultCron() *cron.Cron {
	return NewCron("Asia/Shanghai")
}

func CronSync(
	rules []Rule,
	// aliRegions []*alivpc.DescribeRegionsResponseBodyRegionsRegion,
	awsRegions []*ec2.Region,
	gcpRegions []*compute.Region,
	gcpZones []*compute.Zone,
	// aliRegionMap map[string]int, aliZoneMap map[string]int,
	awsRegionMap map[string]int, awsZoneMap map[string]int,
	gcpRegionMap map[string]int, gcpZoneMap map[string]int,
) *cron.Cron {
	if len(rules) == 0 {
		return nil
	}

	c := NewDefaultCron()

	var err error
	for _, r := range rules {
		rule := r

		switch vendor := rule.FactoryKey.Vendor(); vendor {
		case "aliyun":
			_, err = c.AddFunc(rule.Spec, func() {
				AliDailySync(rule.FactoryKey.String())
			})

		case "gcp":
			_, err = c.AddFunc(rule.Spec, func() {
				GcpDailySync(
					rule.FactoryKey.String(),
					gcpRegions,
					gcpZones,
					gcpRegionMap,
					gcpZoneMap,
				)
			})

		case "aws":
			_, err = c.AddFunc(rule.Spec, func() {
				AwsDailySync(
					rule.FactoryKey.String(),
					awsRegions,
					awsRegionMap,
					awsZoneMap,
				)
			})

		case "ucloud":
			_, err = c.AddFunc(rule.Spec, func() {
				TencentSync(rule.FactoryKey.String())
			})

		case "volc":
			_, err = c.AddFunc(rule.Spec, func() {
				VolcSync([]factory.FactoryKeyType{
					rule.FactoryKey,
				})
			})

		case "tencent":
			_, err = c.AddFunc(rule.Spec, func() {
				TencentSync(rule.FactoryKey.String())
			})

		default:
			logger.Warn(fmt.Sprintf("unknown cloud provider '%s' to sync", vendor))
		}

		if err != nil {
			logger.Error("cron add func error", "error", err, "rule", rule)
		} else {
			logger.Info("cron add func success", "rule", rule)
		}
	}

	return c
}
