package job

import (
	"sync"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/task/ucloud/host"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/task/ucloud/nets"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/task/ucloud/sg"
)

func UCloudSync(accountKey string) error {
	wg := sync.WaitGroup{}

	// Uhost
	wg.Add(1)
	go func(account string) {
		defer wg.Done()

		host.Sync(account)
	}(accountKey)

	// EIP
	wg.Add(1)
	go func(account string) {
		defer wg.Done()

		nets.Sync(account)
	}(accountKey)

	wg.Add(1)
	go func(account string) {
		defer wg.Done()

		sg.Sync(account)
	}(accountKey)

	wg.Wait()

	return nil
}
