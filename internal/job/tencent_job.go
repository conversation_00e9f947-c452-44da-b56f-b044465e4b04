package job

import (
	"sync"

	qecs "gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/task/tencent/ecs"
	qtke "gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/task/tencent/tke"
)

func TencentSync(accountKey string) error {
	wg := sync.WaitGroup{}

	// CVM
	wg.Add(1)
	go func(account string) {
		defer wg.Done()

		qecs.Sync(account)
	}(accountKey)

	// TKE
	wg.Add(1)
	go func(account string) {
		defer wg.Done()

		qtke.Sync(account)
	}(accountKey)

	wg.Wait()

	return nil
}
