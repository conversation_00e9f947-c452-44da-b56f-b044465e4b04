package job

import (
	"context"
	"fmt"
	"strings"
	"sync"

	"gitlab.lilithgame.com/yunwei/pkg/logger"
	log "gitlab.lilithgame.com/yunwei/pkg/logger"
	"google.golang.org/api/cloudresourcemanager/v1"
	"google.golang.org/api/compute/v1"
	"google.golang.org/api/googleapi"
	"google.golang.org/api/option"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/cache"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/task/gcp"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/task/gcp/gce"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/task/gcp/gke"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/task/gcp/pubips"
)

// GCPConfig return credential bytes for GCP.
func GCPConfig(factoryKey string) []byte {
	if f := cache.GetFactory(factoryKey); f != nil {
		return restclient.GCPConfigWithSecret(f.KMSAccount)
	}
	return nil
}

func ListGCPProjects(factory string) ([]string, map[string]float64) {
	projectList := []string{}
	projectMap := map[string]float64{}

	cred := option.WithCredentialsJSON([]byte(GCPConfig(factory)))

	computeService, err := compute.NewService(context.Background(), cred)
	if err != nil {
		log.Error(fmt.Sprintf("compute.NewService error: %s", err.Error()))
		return nil, projectMap
	}

	resourceService, err := cloudresourcemanager.NewService(context.Background(), cred)
	if err != nil {
		log.Error(fmt.Sprintf("cloudresourcemanager.NewService error: %s", err.Error()))
		return nil, projectMap
	}

	projectLists, err := resourceService.Projects.List().Context(context.Background()).Do()
	if err != nil {
		log.Error(fmt.Sprintf("resourceService.NewService error: %s", err.Error()))
		return nil, projectMap
	}

	log.Info(fmt.Sprintf("Found %d projects in GCP", len(projectLists.Projects)))

	for _, project := range projectLists.Projects {
		if strings.EqualFold(project.Name, "My First Project") || project.LifecycleState != "ACTIVE" {
			continue
		}
		_, err := computeService.GlobalForwardingRules.List(project.ProjectId).Context(context.Background()).Do()
		if err != nil {
			googleError, ok := err.(*googleapi.Error)
			if ok && googleError.Code == 403 {
				continue
			}
		}

		// cmdbResultList, _ := oma.QueryWithKeyArgs("product", "external_id__contains", project.Name)
		// projectCmdbID := float64(-1)
		// if len(cmdbResultList) == 1 {
		// 	projectCmdbID = cmdbResultList[0].(map[string]interface{})["id"].(float64)
		// }
		// projectList = append(projectList, project.ProjectId)
		// projectMap[project.ProjectId] = projectCmdbID

		var product models.Product
		recorded, err := restclient.Find(&product, fields.Contains("external_id", project.Name))
		if err != nil {
			continue
		}

		projectCmdbID := -1
		if recorded {
			projectCmdbID = product.GetID()
		}
		projectList = append(projectList, project.ProjectId)
		projectMap[project.ProjectId] = float64(projectCmdbID)
	}
	return projectList, projectMap
}

// GCPInit 初始化 Region, Zone(通过gcp-farlight账号)
func GCPInit() ([]*compute.Region, []*compute.Zone, map[string]int, map[string]int) {
	GCP_REG_MAP_FROM_CMDB := make(map[string]int)
	GCP_ZONE_MAP_FROM_CMDB := make(map[string]int)

	computeService, _ := compute.NewService(context.Background(), option.WithCredentialsJSON([]byte(GCPConfig("gcp-farlight"))))
	if computeService == nil {
		return nil, nil, GCP_REG_MAP_FROM_CMDB, GCP_ZONE_MAP_FROM_CMDB
	}

	var factory models.Factory
	if has, err := restclient.Find(&factory, fields.NamedField("name", "谷歌云")); err != nil || !has {
		log.Error("factory GoogleCloud not found")
	}

	regionsResp, _ := restclient.List[models.Region](
		fields.NamedField("factory__id", factory.GetID()),
	)
	for _, reg := range regionsResp.Results {
		GCP_REG_MAP_FROM_CMDB[reg.RegionID] = reg.GetID()
	}

	zones := cache.ListZones(cache.ZoneFilterFactoryID(factory.GetID()))
	for _, zone := range zones {
		GCP_ZONE_MAP_FROM_CMDB[zone.ZoneID] = zone.GetID()
	}

	projectList, _ := ListGCPProjects("gcp-farlight")
	if len(projectList) == 0 {
		log.Warn("GCP found no projects or found failed")
		return nil, nil, GCP_REG_MAP_FROM_CMDB, GCP_ZONE_MAP_FROM_CMDB
	}

	// 同步Region
	log.Info("GCP Region Sync Start")
	regionList, zoneList := gcp.RegionTask(computeService, projectList)
	log.Info("GCP Region Sync Finish")
	return regionList, zoneList, GCP_REG_MAP_FROM_CMDB, GCP_ZONE_MAP_FROM_CMDB
}

func GcpDailySync(
	factory string,
	regionList []*compute.Region,
	zoneList []*compute.Zone,
	GCP_REG_MAP_FROM_CMDB, GCP_ZONE_MAP_FROM_CMDB map[string]int,
) {
	flog := logger.Slog().With("factory", factory)

	account, err := GetFactoryAccount(factory)
	if err != nil {
		flog.Error("factory account not found", "error", err)
		return
	}

	projectList, _ := ListGCPProjects(factory)

	// 同步VPC&Subnet
	flog.Info("VPC Sync Start")
	gcp.VPCTask(GCPConfig(factory), projectList, GCP_REG_MAP_FROM_CMDB, regionList, account.Factory, account.GetID())
	flog.Info("VPC Sync Finish")

	wg := sync.WaitGroup{}

	// 同步GCE
	wg.Add(1)
	go func(factory string) {
		defer wg.Done()

		flog.Info("GCE Sync Start")

		if serr := gce.Sync(factory); serr != nil {
			logger.Error("sync gce error", "account", factory, "err", serr)
		}

		flog.Info("GCE Sync Finish")
	}(factory)

	// 同步GKE
	wg.Add(1)
	go func(factory string) {
		defer wg.Done()

		flog.Info("GKE Sync Start")

		if serr := gke.Sync(factory); serr != nil {
			flog.Error("sync gcp k8s cluster error", "err", serr)
		}

		flog.Info("GKE Sync Finish")
	}(factory)

	// 同步mysql
	wg.Add(1)
	go func(factory string) {
		defer wg.Done()
		flog.Info("GCP MySQL Sync Start")
		gcp.MysqlTask(GCPConfig(factory), projectList, GCP_REG_MAP_FROM_CMDB, regionList, account)
		flog.Info("GCP MySQL Sync Finish")
	}(factory)

	// TODO: 同步loadbalencer
	// wg.Add(1)
	// go func(factory string) {
	// 	defer wg.Done()
	// 	log.Info("GCP LoadBalencer Sync Start")
	// 	gcp.LoadBalancerTask(GCPConfig(factory), projectList, GCP_REG_MAP_FROM_CMDB, regionList, account.Factory)
	// 	log.Info("GCP LoadBalencer Sync Finish")
	// }(factory)

	// 同步redis
	wg.Add(1)
	go func(factory string) {
		defer wg.Done()
		flog.Info("GCP Image Sync Start")
		gcp.RedisTask(GCPConfig(factory), projectList, GCP_REG_MAP_FROM_CMDB, regionList, account.Factory, account.GetID())
		flog.Info("GCP Image Sync Finish")
	}(factory)

	// 同步 Public IP
	wg.Add(1)
	go func(factory string) {
		defer wg.Done()

		flog.Info("Public ip Sync Start")

		if serr := pubips.Sync(factory); serr != nil {
			flog.Error("sync gcp public ip error", "err", serr)
		}

		flog.Info("Public ip Sync Finish")
	}(factory)

	wg.Wait()
}
