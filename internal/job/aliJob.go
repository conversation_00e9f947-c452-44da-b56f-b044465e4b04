package job

import (
	"fmt"
	"sync"
	"time"

	openapiv2 "github.com/alibabacloud-go/darabonba-openapi/v2/client"
	alivpc "github.com/alibabacloud-go/vpc-20160428/v2/client"
	"github.com/fatih/color"
	log "gitlab.lilithgame.com/yunwei/pkg/logger"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/factory"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/cache"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/task/ali"
	alidisk "gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/task/ali/disk"
	aliecs "gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/task/ali/ecs"
	alieipsync "gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/task/ali/eip"
	emrtask "gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/task/ali/emr"
	alihbasetask "gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/task/ali/hbase"
	alimgotask "gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/task/ali/mgo"
	alimysqltask "gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/task/ali/mysql"
	alipolartask "gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/task/ali/polar"
	aliredistask "gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/task/ali/redis"
	sgtask "gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/task/ali/sg"
	alislbtask "gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/task/ali/slb"
	alislstask "gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/task/ali/slsv2"
	vpctask "gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/task/ali/vpc"
)

// AliConfig return a `*openapiv2.Config` for accessing Aliyun Service.
func AliConfig(factoryKey string) *openapiv2.Config {
	if f := cache.GetFactory(factoryKey); f != nil {
		return restclient.AliConfigWithSecret(f.KMSAccount)
	}
	return nil
}

func AliInit() ([]*alivpc.DescribeRegionsResponseBodyRegionsRegion, map[string]int, map[string]int) {
	ALI_REG_MAP_FROM_CMDB := make(map[string]int)
	ALI_ZONE_MAP_FROM_CMDB := make(map[string]int)

	regionPage, err := restclient.List[models.Region](
		fields.NamedField("factory__name", "阿里云"),
		fields.Unlimited, // 获取当前CMDB中的所有阿里云的region
	)
	if err != nil {
		return nil, nil, nil
	}

	for _, reg := range regionPage.Results {
		// regId := reg.RegionID
		// cmdbId := reg.ID
		// ALI_REG_MAP_FROM_CMDB[regId] = cmdbId
		ALI_REG_MAP_FROM_CMDB[reg.RegionID] = reg.GetID()
	}

	zoneList := cache.ListZones(cache.ZoneFilterFactory("阿里云"))
	for _, zone := range zoneList {
		ALI_ZONE_MAP_FROM_CMDB[zone.ZoneID] = zone.GetID()
	}

	// 同步Region&Zone
	log.Info("Aliyun Region Sync Start")
	regionList := ali.RegionTask(AliConfig("aliyun-lilith"), ALI_REG_MAP_FROM_CMDB)
	log.Info("Aliyun Region Sync Finish")

	return regionList, ALI_REG_MAP_FROM_CMDB, ALI_ZONE_MAP_FROM_CMDB
}

func AliDailySync(factoryName string) {
	wg := new(sync.WaitGroup)

	// 同步VPC&Subnet
	// log.Info(factory + " VPC Sync Start")
	// ali.VPCTask(AliConfig(factory), AliConfig(factory), ALI_REG_MAP_FROM_CMDB, regionList, ALI_ZONE_MAP_FROM_CMDB, factoryID)
	// log.Info(factory + " VPC Sync Finish")
	wg.Add(1)
	go func(factory string) {
		defer wg.Done()

		log.Info(factory + " VPC Sync Start")
		vpctask.Sync(factory)
		log.Info(factory + " VPC Sync Finish")
	}(factoryName)

	// 同步ECS
	wg.Add(1)
	go func(f string) {
		defer wg.Done()

		// Security Group/Rules
		log.Info("Sync security group and rules start", "factory", f)
		sgtask.Sync(f)

		log.Info("ECS Sync Start", "factory", f)
		s := aliecs.NewBatchSyncerWithFactoryKeys(factory.FactoryKeyType(f))
		if err := s.Sync(); err != nil {
			log.Error("ECS Sync Error", "error", err, "factory", f)
			return
		}

		log.Info("ECS Sync Finish", "factory", f)
	}(factoryName)

	// 同步 SLS 日志
	wg.Add(1)
	go func(f string) {
		defer wg.Done()

		log.Info("SLS Sync Start", "factory", f)

		s := alislstask.NewBatchSyncerWithFactoryKeys(factory.FactoryKeyType(f))
		if err := s.Sync(); err != nil {
			log.Error("SLS Sync Error", "error", err, "factory", f)
			return
		}

		log.Info("SLS Sync Finish", "factory", f)
	}(factoryName)

	// 同步mysql
	wg.Add(1)
	go func(f string) {
		defer wg.Done()

		log.Info(f + " MySQL Sync Start")

		if err := alimysqltask.NewBatchSyncerWithFactoryKeys(factory.FactoryKeyType(f)).Sync(); err != nil {
			log.Error("Aliyun MySQL Sync Error", "error", err)
		}

		log.Info(f + " MySQL Sync Finish")
	}(factoryName)

	// 同步redis
	wg.Add(1)
	go func(f string) {
		defer wg.Done()

		log.Info(f + " Redis Sync Start")
		fmt.Println(f + " Redis Sync Start")

		s := aliredistask.NewBatchSyncerWithFactoryKeys(factory.FactoryKeyType(f))
		s.Sync()

		fmt.Println(f + " Redis Sync Finish")
		log.Info(f + " Redis Sync Finish")
	}(factoryName)

	// 同步mongo
	wg.Add(1)
	go func(f string) {
		defer wg.Done()

		log.Info(f + " Mongodb Sync Start")

		s := alimgotask.NewBatchSyncerWithFactoryKeys(factory.FactoryKeyType(f))
		s.Sync()

		log.Info(f + " Mongodb Sync Finish")
	}(factoryName)

	// 同步loadbalencer
	wg.Add(1)
	go func(factory string) {
		defer wg.Done()

		log.Info(factory + " LoadBalencer Sync Start")
		fmt.Println(factory + " LoadBalencer Sync Start")

		alislbtask.SyncWithAccount(factory)

		fmt.Println(factory + " LoadBalencer Sync Finish")
		log.Info(factory + " LoadBalencer Sync Finish")
	}(factoryName)

	// 同步EIP
	wg.Add(1)
	go func(factory string) {
		defer wg.Done()

		log.Info(factory + " EIP Sync Start")
		fmt.Println(factory + " EIP Sync Start")

		if err := alieipsync.SyncMany(factory); err != nil {
			log.Error("Aliyun EIP Sync Error", "error", err)
		}

		fmt.Println(factory + " EIP Sync Finish")
		log.Info(factory + " EIP Sync Finish")
	}(factoryName)

	// 同步hbase
	wg.Add(1)
	go func(factory string) {
		defer wg.Done()

		log.Info(factory + " Hbase Sync Start")
		alihbasetask.Sync(factory)
		log.Info(factory + " Hbase Sync Finish")
	}(factoryName)

	// 同步polarDB
	wg.Add(1)
	go func(factory string) {
		defer wg.Done()

		log.Info(factory + " PolarDB Sync Start")
		alipolartask.Sync(factory)
		log.Info(factory + " PolarDB Sync Finish")
	}(factoryName)

	// 同步 EMR
	wg.Add(1)
	go func(factory string) {
		defer wg.Done()

		log.Info(factory + " EMR Sync Start")
		emrtask.Sync(factory)
		log.Info(factory + " EMR Sync Finish")
	}(factoryName)

	wg.Wait()
}

func AliyunBlockDiskSync(keys ...factory.FactoryKeyType) {
	log.Info("Aliyun BlockDisk Sync Start")

	for _, key := range keys {
		start := time.Now()

		if s, err := alidisk.NewSyncer(key.String()); err == nil {
			if serr := s.Sync(); serr == nil {
				alidisk.Clean(start.Unix(), fields.FactoryAccountField(s.AccountID()))
			} else {
				log.Error("Aliyun BlockDisk Sync Error", "error", serr, "factory", key)
				color.Red("Aliyun blockdisk sync error %v", err)

				continue
			}
		}

		cost := time.Since(start).Seconds()

		log.Debug("Aliyun BlockDisk Sync factory finish", "factory", key, "cost", cost)
	}

	log.Info("Aliyun BlockDisk Sync Finish")
}
