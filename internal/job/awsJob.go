package job

import (
	"sync"

	"github.com/aws/aws-sdk-go/service/ec2"
	log "gitlab.lilithgame.com/yunwei/pkg/logger"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/cache"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/task/aws"
	ec2task "gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/task/aws/ec2"
	awseiptask "gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/task/aws/eip"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/task/aws/mgo"
	awsmysqltask "gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/task/aws/mysql"
	redistask "gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/task/aws/redis"
	awsslbtask "gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/task/aws/slb"
	vpctask "gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/task/aws/vpc"
)

// AWSConfig return `ak`, `sk` for AWS EC2 authentication.
func AWSConfig(factory string) (string, string) {
	f := cache.GetFactory(factory)
	return restclient.AWSConfigWithSecretName(f.KMSAccount)
}

func AWSInit() ([]*ec2.Region, map[string]int, map[string]int) {
	log.Info("AWS Sync Job Start")

	AWSRegionMap := make(map[string]int)
	AWSZoneMap := make(map[string]int)

	// 获取当前CMDB中的所有region
	regions, err := restclient.List[models.Region](
		fields.NamedField("factory__name", "亚马逊"),
		fields.Unlimited,
	)
	if err != nil {
		log.Error("load all regions for aws failed", "error", err)
		return nil, nil, nil
	}

	for _, reg := range regions.Results {
		regId := reg.RegionID
		cmdbId := reg.GetID()

		// log.Debug("found region", "region", regId, "name", reg.Name)

		if _, ok := AWSRegionMap[regId]; !ok {
			AWSRegionMap[regId] = cmdbId
		} else {
			log.Warn("region has multiple record", "id", cmdbId, "region", reg.Name)
		}
	}

	// 获取当前CMDB中的所有zone
	zones := cache.ListZones(cache.ZoneFilterFactory("亚马逊"))
	for _, zone := range zones {
		AWSZoneMap[zone.Name] = zone.GetID()
	}

	ak, sk := AWSConfig("aws-farlight")

	// 同步Region&Zone
	log.Info("AWS Region Sync Start")
	regionList := aws.RegionTask(ak, sk, AWSRegionMap)
	log.Info("AWS Region Sync Finish")
	return regionList, AWSRegionMap, AWSZoneMap
}

func AwsDailySync(
	factory string,
	regList []*ec2.Region,
	AWS_REG_MAP_FROM_CMDB map[string]int,
	AWS_ZONE_MAP_FROM_CMDB map[string]int,
) {
	flog := log.Slog().With("factory", factory)

	//  同步 VPC
	vpctask.NewBatchSyncerWithFactoryKeys(factory).Sync()

	// TODO: 同步Subnet
	// ak, sk := AWSConfig(factory)
	// log.Info(factory + " VPC Sync Start")

	// aws.VPCTask(ak, sk, AWS_REG_MAP_FROM_CMDB, regList, AWS_ZONE_MAP_FROM_CMDB, f.ID, account.ID)
	// log.Info(factory + " VPC Sync Finish")

	wg := sync.WaitGroup{}

	// 同步ec2
	wg.Add(1)
	go func(factory string) {
		defer wg.Done()

		flog.Info("AWS EC2 Sync Start")

		bs := ec2task.NewBatchSyncerWithFactoryKeys(factory)
		bs.Sync()

		flog.Info("AWS EC2 Sync Finish")
	}(factory)

	// 同步MongoDB(docdb)
	wg.Add(1)
	go func(factory string) {
		defer wg.Done()

		flog.Info("AWS Mongo Sync Start")

		mgo.Sync(factory)

		flog.Info("AWS Mongo Sync Finish")
	}(factory)

	// 同步mysql
	wg.Add(1)
	go func(factory string) {
		defer wg.Done()

		flog.Info("AWS MySQL Sync Start")

		awsmysqltask.Sync(factory)

		flog.Info("AWS MySQL Sync Finish")
	}(factory)

	wg.Add(1)
	go func(factory string) {
		defer wg.Done()

		flog.Info("AWS Redis Sync Start")
		defer flog.Info("AWS Redis Sync Finish")

		redistask.Sync(factory)
	}(factory)

	// 同步loadbalencer
	wg.Add(1)
	go func(factory string) {
		defer wg.Done()

		flog.Info("AWS LoadBalencer sync start")
		defer flog.Info("AWS LoadBalencer sync finish")

		awsslbtask.SyncWithAccount(factory)
	}(factory)

	// 同步eip
	wg.Add(1)
	go func(factory string) {
		defer wg.Done()

		flog.Info("AWS EIP Sync Start")
		defer flog.Info("AWS EIP Sync Finish")

		awseiptask.Sync(factory)
	}(factory)

	wg.Wait()
}
