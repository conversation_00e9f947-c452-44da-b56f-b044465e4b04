package job

import (
	"fmt"

	"gitlab.lilithgame.com/yunwei/pkg/logger"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
)

func CleanOutdatedMachineRecord(conds ...fields.Field) error {
	if len(conds) == 0 {
		return fmt.Errorf("at least one condition is required")
	}

	for total := 1; total > 0; {
		resp, err := restclient.ListN[models.Machine](100, conds...)
		if err != nil {
			return err
		}

		total = resp.Count

		logger.Infof("find %d outdate deleted machine", resp.Count)

		for _, m := range resp.Results {
			if derr := restclient.Delete(m); derr != nil {
				return derr
			}
		}
	}

	return nil
}
