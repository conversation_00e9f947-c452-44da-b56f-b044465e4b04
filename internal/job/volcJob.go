package job

import (
	"sync"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/factory"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/task/volc/vdisk"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/task/volc/vecs"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/task/volc/vmysql"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/task/volc/vpc"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/task/volc/vredis"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/task/volc/vslb"
)

func VolcSync(accountKeys []factory.FactoryKeyType) error {
	wg := sync.WaitGroup{}

	// EIP
	wg.Add(1)
	go func() {
		defer wg.Done()
		vpc.Sync(accountKeys[0])
	}()

	// ECS
	wg.Add(1)
	go func() {
		defer wg.Done()
		vecs.SyncAll(accountKeys...)
	}()

	// SLB
	wg.Add(1)
	go func() {
		defer wg.Done()
		vslb.SyncMany(accountKeys...)
	}()

	// Disk
	wg.Add(1)
	go func() {
		defer wg.Done()
		vdisk.SyncAll(accountKeys...)
	}()

	wg.Wait()

	// Redis
	wg.Add(1)
	go func() {
		defer wg.Done()
		vredis.SyncAll(accountKeys...)
	}()

	// MySQL
	wg.Add(1)
	go func() {
		defer wg.Done()
		vmysql.SyncAll(accountKeys...)
	}()

	wg.Wait()

	return nil
}
