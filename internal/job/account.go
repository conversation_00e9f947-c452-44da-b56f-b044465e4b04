package job

import (
	"fmt"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
)

// GetFactoryAccount get factory account by key
func GetFactoryAccount(key string) (*models.FactoryAccount, error) {
	var account models.FactoryAccount
	hasAccount, err := restclient.Find(&account,
		fields.StringField("key_name", key),
	)
	if err != nil {
		return nil, err
	}

	if !hasAccount {
		return nil, fmt.Errorf("factory account %s not found", key)
	}

	return &account, nil
}
