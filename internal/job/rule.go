package job

import (
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/factory"
)

type Rule struct {
	FactoryKey factory.FactoryKeyType `mapstructure:"factory_key"`
	Startup    bool                   `mapstructure:"startup"`
	Spec       string                 `mapstructure:"spec"`
}

var crontabs = []Rule{
	// VolcEngine(火山云)
	{factory.VolcQA, true, "@every 2h"},
	{factory.VolcPlat, true, "@every 2h"},
	{factory.VolcPlat2, true, "@every 2h"},
	{factory.VolcMona, true, "@every 2h"},
	{factory.VolcDgame, true, "@every 2h"},
	{factory.VolcWgame, true, "@every 2h"},
	{factory.VolcFarlightCN, true, "@every 2h"},

	// 腾讯云
	{factory.TencentLilith, true, "@every 2h"},
	{factory.TencentMona, true, "@every 2h"},

	// Aliyun
	{factory.AliyunLilith, true, "@every 2h"},
	{factory.AliyunPlatGlobal, true, "@every 2h"},
	{factory.AliyunRok, true, "@every 2h"},
	{factory.AliyunWgame, true, "@every 2h"},
	{factory.AliyunW3, true, "@every 2h"},
	{factory.<PERSON>yunDgame, true, "@every 2h"},
	{factory.AliyunPGame, true, "@every 2h"},
	{factory.AliyunFarlightCN, true, "@every 2h"},
	{factory.AliyunFarlightGlobal, true, "@every 2h"},
	{factory.AliyunFarlightPlat, true, "@every 2h"},
	{factory.AliyunXGame, false, "@every 2h"},
	{factory.AliyunSatanpit, false, "@every 2h"},
	{factory.AliyunSamo, false, "@every 2h"},
	{factory.AliyunSamoCN, false, "@every 2h"},
	{factory.AliyunMona, false, "@every 2h"},
	{factory.AliyunIGame, false, "@every 2h"},
	{factory.AliyunAvatar, false, "@every 2h"},
	{factory.AliyunDevops, false, "@every 2h"},
	{factory.AliyunSecurity, true, "@every 2h"},
	{factory.AliyunWaibao, true, "@every 2h"},
	{factory.AliyunPtslg, true, "@every 2h"},
	{factory.AliyunIT, true, "@every 2h"},

	// GCP
	{factory.GCPFarlight, true, "@every 2h"},
	{factory.GCPLilith, true, "@every 2h"},

	// AWS
	{factory.AWSFarlight, true, "@every 2h"},
	{factory.AWSSamo, true, "@every 2h"},
	{factory.AWSPlat, true, "@every 2h"},
	{factory.AWSPlat2, true, "@every 2h"},
	{factory.AWSAd, true, "@every 2h"},
	{factory.AWSMona, true, "@every 2h"},

	// UCloud
	{factory.UcloudLilith, false, "@every 2h"},
	{factory.UcloudPub, false, "@every 2h"},
	{factory.UcloudDGame, false, "@every 2h"},
}

type RuleFilter func(*Rule) bool

func WithVendor(vendor string) RuleFilter {
	return func(r *Rule) bool {
		return r.FactoryKey.Vendor() == vendor
	}
}

func WithStartup(startup bool) RuleFilter {
	return func(r *Rule) bool {
		return r.Startup == startup
	}
}

func FilterRules(rules []Rule, filter RuleFilter) []Rule {
	var filtered []Rule
	for _, rule := range rules {
		if filter(&rule) {
			filtered = append(filtered, rule)
		}
	}
	return filtered
}

func VendorRules(vendor string) []Rule {
	return FilterRules(crontabs, WithVendor(vendor))
}

func VendorFactory(vendor string) []factory.FactoryKeyType {
	keys := make([]factory.FactoryKeyType, 0)
	for _, rule := range VendorRules(vendor) {
		keys = append(keys, rule.FactoryKey)
	}

	return keys
}
