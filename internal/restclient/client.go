package restclient

import (
	"crypto/tls"
	"net"
	"net/http"
	"net/url"
	"time"

	"gitlab.lilithgame.com/yunwei/pkg/wrap"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/util"
)

const Name = "AssetSyncer"

type RESTClient struct {
	*http.Client

	BaseURL string
	Headers http.Header // 使用标准的 http.Header 类型

	apiVersion string
}

func New(baseURL string, opts ...ClientOption) *RESTClient {
	cli := &RESTClient{
		Client:  createHTTPClient(),
		BaseURL: wrap.Unwrap(url.JoinPath(baseURL, "cmdb")),
		Headers: make(http.Header),
	}

	for _, opt := range opts {
		opt(cli)
	}

	return cli
}

func createHTTPClient() *http.Client {
	transport := &http.Transport{
		DialContext: (&net.Dialer{
			Timeout:   30 * time.Second,
			KeepAlive: 30 * time.Second,
		}).DialContext,

		ForceAttemptHTTP2:     false,            // 强制使用HTTP/2
		MaxIdleConns:          100,              // 最大空闲连接数
		IdleConnTimeout:       90 * time.Second, // 空闲连接超时时间
		ExpectContinueTimeout: 1 * time.Second,  // 1秒的期望继续超时

		// 每个主机的连接数限制
		MaxIdleConnsPerHost: 20,
		MaxConnsPerHost:     0,

		TLSClientConfig: &tls.Config{
			InsecureSkipVerify: true, // 忽略证书验证
		},
	}

	return &http.Client{
		Transport: transport,
		Timeout:   60 * time.Second,
	}
}

func (c *RESTClient) fullURL(resource string, fs ...fields.Field) (string, error) {
	base, err := url.JoinPath(c.BaseURL, resource)
	if err != nil {
		return "", err
	}

	return util.BuildURL(base, fields.NewFields(fs...))
}
