package restclient

import (
	"fmt"
	"io"
	"net/http"
	"net/url"
	"slices"

	"gitlab.lilithgame.com/yunwei/pkg/wrap"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
)

var okStatusCodes = []int{
	http.StatusOK,
	http.StatusCreated,
	http.StatusAccepted,
	http.StatusNonAuthoritativeInfo,
	http.StatusNoContent,
	http.StatusResetContent,
	http.StatusPartialContent,
	http.StatusMultiStatus,
	http.StatusAlreadyReported,
	http.StatusIMUsed,
}

func (c *RESTClient) Request(req *http.Request) ([]byte, error) {
	// 合并请求头，避免覆盖已有的头部信息
	for key, values := range c.Headers.Clone() {
		if _, ok := req.Header[key]; !ok {
			for _, value := range values {
				req.Header.Add(key, value)
			}
		}
	}

	resp, err := c.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	if !slices.Contains(okStatusCodes, resp.StatusCode) {
		return nil, fmt.Errorf("http status: %s, body: %s", resp.Status, string(body))
	}

	return body, nil
}

func (c *RESTClient) JSON(req *http.Request) ([]byte, error) {
	req.Header.Set("Content-Type", "application/json")
	return c.Request(req)
}

func (c *RESTClient) Query(resource string, fs ...fields.Field) ([]byte, error) {
	fullurl, err := c.fullURL(resource, fs...)
	// logger.Info("Querying URL", "full_url", fullurl, "error", err)
	if err != nil {
		return nil, err
	}

	r, err := http.NewRequest(http.MethodGet, fullurl, nil)
	if err != nil {
		return nil, err
	}

	return c.Request(r)
}

func (c *RESTClient) Get(resource string, id string) ([]byte, error) {
	return c.Query(wrap.Unwrap(url.JoinPath(resource, id)))
}
