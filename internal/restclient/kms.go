package restclient

import (
	"encoding/json"
	"fmt"

	openapiv2 "github.com/alibabacloud-go/darabonba-openapi/v2/client"
	kmsv3 "github.com/alibabacloud-go/kms-20160120/v3/client"
	"github.com/alibabacloud-go/tea/tea"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/config"
)

func NewKMSClient(region ...string) (*kmsv3.Client, error) {
	kmsConfig := config.Config().KMS

	ak := kmsConfig.AccessKeyID
	sk := kmsConfig.SecretAccessKey
	endpoint := kmsConfig.Endpoint

	config := &openapiv2.Config{
		AccessKeyId:     &ak,
		AccessKeySecret: &sk,
		Endpoint:        &endpoint,
	}

	if len(region) > 0 {
		endpoint = fmt.Sprintf("kms.%s.aliyuncs.com", region[0])
		config.SetEndpoint(endpoint).SetRegionId(region[0])
	}

	return kmsv3.NewClient(config)
}

func GetCredentialBytes(secretName string, region ...string) ([]byte, error) {
	client, err := NewKMSClient(region...)
	if err != nil {
		return nil, err
	}

	req := &kmsv3.GetSecretValueRequest{
		SecretName: tea.String(secretName),
	}

	resp, err := client.GetSecretValue(req)
	if err != nil {
		return nil, err
	}

	data := tea.StringValue(resp.Body.SecretData)

	return []byte(data), err
}

func UnmarshalSecret(secretName string, v any) error {
	regions := []string{
		"cn-shanghai",
		"cn-hangzhou",
	}

	for _, region := range regions {
		bs, err := GetCredentialBytes(secretName, region)
		if err == nil {
			err = json.Unmarshal(bs, v)
			if err == nil {
				return nil
			}
		}
	}

	return fmt.Errorf("get credential error for %s", secretName)
}

// func GetCredentialBySecretName(secretName string) (map[string]string, error) {
// 	var credential = make(map[string]string)
// 	err := GetUnmarshaledCredential(secretName, &credential)
// 	return credential, err
// }

// func GetCredential(factoryKey string) (map[string]string, error) {
// 	var cred = make(map[string]string)
// 	err := GetUnmarshaledCredential(FactorySecret(factoryKey), &cred)
// 	return cred, err
// }

// MustCredential return crential map, panic if encounter any error
// func MustCredential(supplier string) map[string]string {
// 	cred, err := GetCredential(supplier)
// 	if err != nil {
// 		panic(err)
// 	}
// 	return cred
// }
