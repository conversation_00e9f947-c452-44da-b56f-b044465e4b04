package restclient

import (
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"net/url"
	"strings"

	log "gitlab.lilithgame.com/yunwei/pkg/logger"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/config"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/util"
)

func BaseURLForOMAResource(resource string, paths ...string) string {
	subPaths := []string{"cmdb", strings.ToLower(resource)}
	subPaths = append(subPaths, paths...)

	u, _ := url.JoinPath(config.Config().OMA.BaseURL, subPaths...)
	return u
}

// Do do something with oma RESTful resource
func DoAction(method, url string, body any) ([]byte, error) {
	bs, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	return doRequest(defaultClient, method, url, bs)
}

func doRequest(cli *RESTClient, method, resourceURL string, body []byte) ([]byte, error) {
	req, err := http.NewRequest(method, resourceURL, bytes.NewReader(body))
	if err != nil {
		log.Errorf("failed to create request: %s", err.Error())
		return nil, err
	}

	bs, err := cli.JSON(req)
	if err != nil {
		log.Error("request oma failed", "method", method, "url", resourceURL, "error", err.Error())
		return nil, err
	}

	return bs, nil
}

// Post posts new RESTful resource with attrs
func Post[T models.Resourcer](body fields.Fields) (*T, error) {
	var r T

	url := BaseURLForOMAResource(string(r.Type()), "/")
	bs, err := DoAction(http.MethodPost, url, body)
	if err != nil {
		return nil, err
	}

	if err = json.Unmarshal(bs, &r); err != nil {
		return nil, err
	}

	return &r, nil
}

func PostSubResource[T models.Resourcer](sub string, pk any, body fields.Fields) error {
	var r T

	url := BaseURLForOMAResource(string(r.Type()), fmt.Sprintf("%v/%s", pk, sub), "/")

	bs, err := DoAction(http.MethodPost, url, body)
	if err != nil {
		return err
	}

	if err = json.Unmarshal(bs, &r); err != nil {
		return err
	}
	return nil
}

func DeleteSubResource[T models.Resourcer](name string, pk any, attrs ...fields.Field) error {
	var r T

	base := BaseURLForOMAResource(string(r.Type()), fmt.Sprintf("%v/%s", pk, name), "/")
	url, _ := util.BuildURL(base, fields.NewFields(attrs...))

	_, err := DoAction(http.MethodDelete, url, nil)

	return err
}

func Delete[T models.Resourcer](r T) error {
	if id := r.GetID(); id != 0 {
		url := BaseURLForOMAResource(string(r.Type()), fmt.Sprintf("%d/", r.GetID()), "/")
		_, err := DoAction(http.MethodDelete, url, nil)
		return err
	}
	return fmt.Errorf("%s is not initialized", r.Type())
}

func PatchByID[T models.Resourcer](id int, attrs fields.Fields) (*T, error) {
	var r T

	resourceURL := BaseURLForOMAResource(string(r.Type()), fmt.Sprintf("%d/", id))

	// in case of override id field
	attrs.Pop(fields.IDFieldKey)

	bs, err := DoAction(http.MethodPatch, resourceURL, attrs)
	if err != nil {
		return nil, err
	}

	if err = json.Unmarshal(bs, &r); err != nil {
		return nil, err
	}

	return &r, nil
}

func PatchAttrs[T models.Resourcer](id int, attrs ...fields.Field) (*T, error) {
	return PatchByID[T](id, fields.NewFields(attrs...))
}

func Patch[T models.Resourcer](r T, attrs fields.Fields) error {
	resourceURL := BaseURLForOMAResource(string(r.Type()), fmt.Sprintf("%d/", r.GetID()))

	// in case of override id field
	attrs.Pop(fields.IDFieldKey)

	bs, err := DoAction(http.MethodPatch, resourceURL, attrs)
	if err != nil {
		return err
	}

	if err = json.Unmarshal(bs, &r); err != nil {
		return err
	}

	return nil
}

func PatchResource[R models.Resourcer](id int, attrs fields.Fields) (*R, error) {
	var r R
	resourceURL := BaseURLForOMAResource(string(r.Type()), fmt.Sprintf("%d/", id))

	// in case of override id field
	attrs.Pop(fields.IDFieldKey)

	bs, err := DoAction(http.MethodPatch, resourceURL, attrs)
	if err != nil {
		return nil, err
	}

	if err = json.Unmarshal(bs, &r); err != nil {
		return nil, err
	}

	return &r, nil
}

// PostOrPatch is a generic function that either creates a new resource or updates an existing resource.
// It takes a condition and attributes as input parameters and returns the created/updated resource and an error (if any).
// If the resource already exists, it will be updated using the PatchResource function.
// If the resource does not exist, it will be created using the Post function.
// The function returns the created/updated resource and any error that occurred during the operation.
func PostOrPatch[R models.Resourcer](conds []fields.Field, attrs fields.Fields) (*R, error) {
	r, err := FindResource[R](conds...)
	if err != nil && !errors.Is(err, ErrResourceNotFound) {
		return nil, err
	}

	// fmt.Printf("find result(%T) %+v\n", r, r)

	if r != nil {
		return PatchResource[R]((*r).GetID(), attrs)
	}

	return Post[R](attrs)
}
