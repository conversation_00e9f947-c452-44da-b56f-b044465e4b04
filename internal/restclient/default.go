package restclient

import (
	"encoding/json"
	"errors"
	"fmt"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/config"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
)

var ErrResourceNotFound = errors.New("resource not found")

var defaultClient = NewDefaultRestClient()

func NewDefaultRestClient(opts ...ClientOption) *RESTClient {
	opts = append(opts,
		WithUserAgent("asset-syncer/1.1"),
		WithAuthorize(config.Config().OMA.Token, "Token"),
		WithRetry(DefaultRetryConfig()),
	)
	cli := New(config.Config().OMA.BaseURL, opts...)

	return cli
}

func Query(resource string, fs ...fields.Field) ([]byte, error) {
	return defaultClient.Query(resource, fs...)
}

// Get get one resource by id
func Get[T models.Resourcer](v T) (has bool, err error) {
	bs, err := defaultClient.Get(string(v.Type()), fmt.Sprintf("%d", v.GetID()))
	if err != nil {
		return false, err
	}

	if err := json.Unmarshal(bs, v); err != nil {
		return false, err
	}

	return true, nil
}

// Find find one resource by fields
func Find[T models.Resourcer](v T, conditions ...fields.Field) (has bool, err error) {
	conditions = append(conditions, fields.LimitField(1))

	bs, err := defaultClient.Query(string(v.Type()), conditions...)
	if err != nil {
		return false, err
	}

	resp := ResourcePage[T]{
		Results: []T{v},
	}

	if err := json.Unmarshal(bs, &resp); err != nil {
		return false, err
	}

	if resp.Count > 0 {
		return true, nil
	}

	return false, nil
}

func FindResource[T models.Resourcer](conditions ...fields.Field) (v *T, err error) {
	var m T

	conditions = append(conditions, fields.LimitField(1))

	bs, err := defaultClient.Query(string(m.Type()), conditions...)
	if err != nil {
		return nil, err
	}

	resp := ResourcePage[T]{
		// Results: []T{m},
		Results: make([]T, 0),
	}

	if err := json.Unmarshal(bs, &resp); err != nil {
		return nil, err
	}

	if resp.Count > 0 {
		v = &resp.Results[0]
		return
	}

	return nil, ErrResourceNotFound
}

// List list all resources by fields in pages
func List[T models.Resourcer](conditions ...fields.Field) (*ResourcePage[T], error) {
	var t T
	bs, err := defaultClient.Query(string(t.Type()), conditions...)
	if err != nil {
		return nil, err
	}

	var resp ResourcePage[T]
	if err := json.Unmarshal(bs, &resp); err != nil {
		return nil, err
	}

	return &resp, nil
}

func ListN[T models.Resourcer](n int, conditions ...fields.Field) (*ResourcePage[T], error) {
	conditions = append(conditions, fields.LimitField(n))
	return List[T](conditions...)
}

func ListAll[T models.Resourcer](conditions ...fields.Field) (*ResourcePage[T], error) {
	conditions = append(conditions, fields.Unlimited)
	return List[T](conditions...)
}
