package restclient

import (
	"fmt"
	"os"

	openapiv2 "github.com/alibabacloud-go/darabonba-openapi/v2/client"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
)

type AliyunCredential struct {
	AccessKeyID     string `json:"access_key_id"`
	AccessKeySecret string `json:"access_key_secret"`
}

type AWSCredential struct {
	AccessKeyID     string `json:"access_key_id"`
	AccessKeySecret string `json:"access_key_secret"`
}

type UcloudCredential struct {
	PublicKey  string `json:"public_key"`
	PrivateKey string `json:"private_key"`
}

type VolcEngineCredential struct {
	AccessKeyID     string `json:"access_key_id"`
	AccessKeySecret string `json:"access_key_secret"`
}

type TencentCredential struct {
	SecretID  string `json:"secret_id"`
	SecretKey string `json:"secret_key"`
}

func AliConfigWithSecret(secret string) *openapiv2.Config {
	var cred AliyunCredential
	if err := UnmarshalSecret(secret, &cred); err != nil {
		return nil
	}

	return &openapiv2.Config{
		AccessKeyId:     fields.Pointer(cred.AccessKeyID),
		AccessKeySecret: fields.Pointer(cred.AccessKeySecret),
	}
}

func AWSConfigWithSecretName(secret string) (string, string) {
	var cred AWSCredential
	err := UnmarshalSecret(secret, &cred)
	if err != nil {
		fmt.Fprintf(os.Stderr, "Failed to get AWS credential for %s: %v", secret, err)
		return "", ""
	}

	return cred.AccessKeyID, cred.AccessKeySecret
}

func GCPConfigWithSecret(secret string) []byte {
	cred, err := GetCredentialBytes(secret)
	if err != nil {
		return nil
	}
	return cred
}
