package restclient

import (
	"fmt"
	"math/rand"
	"net"
	"net/http"
	"strings"
	"time"
)

type ClientOption func(*RESTClient)

func WithApiVersion(ver string) ClientOption {
	return func(c *RESTClient) {
		c.apiVersion = ver
	}
}

func WithHeader(name string, value string) ClientOption {
	return func(c *RESTClient) {
		c.Headers.Set(name, value)
	}
}

func WithAuthorize(token string, prefix ...string) ClientOption {
	headerValue := token
	if len(prefix) > 0 {
		headerValue = fmt.Sprintf("%s %s", prefix[0], token)
	}

	return WithHeader("Authorization", headerValue)
}

func WithBear(token string) ClientOption {
	return WithAuthorize(token, "Bearer")
}

func WithUserAgent(userAgent string) ClientOption {
	return func(c *RESTClient) {
		c.Headers.Set("User-Agent", userAgent)
	}
}

// RetryConfig 重试配置
type RetryConfig struct {
	MaxAttempts int           // 最大重试次数
	BaseDelay   time.Duration // 基础延迟
	MaxDelay    time.Duration // 最大延迟
}

func DefaultRetryConfig() RetryConfig {
	return RetryConfig{
		MaxAttempts: 3,
		BaseDelay:   100 * time.Millisecond,
		MaxDelay:    5 * time.Second,
	}
}

func isRetryableError(err error) bool {
	if err == nil {
		return false
	}

	if netErr, ok := err.(net.Error); ok {
		return netErr.Timeout()
	}

	// 检查是否为HTTP2连接强制关闭错误
	if strings.Contains(err.Error(), "client connection force closed") {
		return true
	}

	return false
}

// WithRetry 为客户端添加重试机制
func WithRetry(config RetryConfig) ClientOption {
	return func(c *RESTClient) {
		originalTransport := c.Transport
		if originalTransport == nil {
			originalTransport = http.DefaultTransport
		}

		c.Transport = &retryTransport{
			transport: originalTransport,
			config:    config,
		}
	}
}

type retryTransport struct {
	transport http.RoundTripper
	config    RetryConfig
}

func (rt *retryTransport) RoundTrip(req *http.Request) (*http.Response, error) {
	var lastErr error
	var lastResp *http.Response

	for attempt := 0; attempt < rt.config.MaxAttempts; attempt++ {
		cloneReq := req.Clone(req.Context())

		resp, err := rt.transport.RoundTrip(cloneReq)
		// 如果没有网络错误，直接返回给调用者处理
		if err == nil {
			return resp, nil
		}

		// 保存最后的响应和错误
		lastResp = resp
		lastErr = err

		// 检查是否应该重试
		if !isRetryableError(err) {
			return resp, err
		}

		// 如果这不是最后一次尝试，等待一段时间
		if attempt < rt.config.MaxAttempts-1 {
			delay := rt.calculateDelay(attempt)
			time.Sleep(delay)

			// 只有在需要重试时才关闭响应体，因为我们不会返回这个响应给调用者
			if resp != nil && resp.Body != nil {
				resp.Body.Close()
			}
		}
	}

	// 所有重试都失败了，返回最后的响应和错误
	// 确保不会同时返回 nil 响应和 nil 错误
	if lastErr == nil && lastResp == nil {
		lastErr = fmt.Errorf("all retry attempts failed with no response")
	}

	return lastResp, lastErr
}

// calculateDelay 计算重试延迟时间 (指数级退避算法)
func (rt *retryTransport) calculateDelay(attempt int) time.Duration {
	delay := rt.config.BaseDelay * time.Duration(1<<attempt)

	jitter := time.Duration(rand.Int63n(int64(delay / 10)))
	delay += jitter

	// 确保延迟不超过最大延迟
	delay = min(delay, rt.config.MaxDelay)

	return delay
}
