package fields

import (
	"encoding/json"
	"fmt"
	"io"
	"strings"

	"golang.org/x/text/cases"
	"golang.org/x/text/language"
)

type Field struct {
	Key   string
	Value any
}

func FieldList(fds ...Field) []Field {
	return fds
}

type Fields map[string]any

func (fs Fields) Keys() []string {
	keys := make([]string, 0)
	for key := range fs {
		keys = append(keys, key)
	}
	return keys
}

// Update updates fields if exists
func (fs Fields) Update(f Fields) {
	for _, key := range f.Keys() {
		fs[key] = f[key]
	}
}

func (fs Fields) SetField(f Field) {
	fs[f.Key] = f.Value
}

func (fs Fields) Set(key string, value any) {
	fs[key] = value
}

func (fs Fields) SetOrNil(key string, value any) {
	if value == nil {
		fs[key] = nil
	} else {
		fs[key] = value
	}
}

func (fs Fields) SetString(key string, value *string) {
	SetValueOrDefault(fs, key, value)
}

func (fs Fields) SetStringOrNil(key string, value *string) {
	SetValueOrNil(fs, key, value)
}

func (fs Fields) SetAsInt(key string, v *string) {
	if n := ToInt(Value(v)); n != nil {
		fs[key] = *n
	}
}

// SetJSON sets the value of a string in the Fields map by marshaling the value to JSON.
func (fs Fields) SetJSON(key string, value any) error {
	bs, err := json.Marshal(value)
	if err != nil {
		return err
	}

	fs.Set(key, string(bs))
	return nil
}

// SetPString sets the value of a string pointer in the Fields map.
// If the key is not present in the map, it will be added with the specified value.
// If the key is already present in the map, the value will be updated.
// Parameters:
//   - key: A pointer to the key string.
//   - value: A pointer to the value string.
func (fs Fields) SetPString(key *string, value *string) {
	SetValueOrDefault(fs, PString(key), value)
}

func (fs Fields) Remove(key string) bool {
	if _, exists := fs[key]; exists {
		delete(fs, key)
		return true
	}
	return false
}

func (fs Fields) WithDefault(f Field) {
	if _, exists := fs[f.Key]; !exists {
		fs.SetField(f)
	}
}

func (fs Fields) With(f ...Field) Fields {
	for _, field := range f {
		fs.SetField(field)
	}
	return fs
}

func (fs Fields) WithString(k string, v *string) Fields {
	if v != nil {
		fs.Set(k, *v)
	}
	return fs
}

func (fs Fields) Get(key string) any {
	return fs[key]
}

func (fs Fields) GetInt(key string) *int {
	if value, exists := fs[key]; exists {
		if v, ok := value.(int); ok {
			return &v
		}
	}
	return nil
}

// GetString returns the value of a string in the Fields map.
// if the key does not exist, an empty string is returned.
// if the value is not a string or nil, an empty string is returned.
func (fs Fields) GetString(key string) string {
	if value, exists := fs[key]; exists {
		switch val := value.(type) {
		case string:
			return val
		}
	}
	return ""
}

func (fs Fields) GetField(key string) *Field {
	if value, exists := fs[key]; exists {
		return &Field{key, value}
	}
	return nil
}

func (fs Fields) Pop(key string) any {
	if value, exists := fs[key]; exists {
		delete(fs, key)
		return value
	}
	return nil
}

func (fs Fields) PopString(key string) string {
	if v := fs.Pop(key); v != nil {
		return v.(string)
	}

	return ""
}

func (fs Fields) PopInt(key string) *int {
	if v := fs.Pop(key); v != nil {
		if vv, ok := v.(int); ok {
			return &vv
		}
	}

	return nil
}

func (fs Fields) Beautify(out io.Writer) {
	for name, value := range fs {
		fmt.Fprintf(out, " %s: %v\n", name, value)
	}
}

func NewFields(f ...Field) Fields {
	fs := make(Fields)
	for _, field := range f {
		fs.SetField(field)
	}
	return fs
}

func NamedField(key string, v any) Field {
	return Field{key, v}
}

func NilField(key string) Field {
	return Field{key, nil}
}

func NumberField[T Number](key string, v T) Field {
	return Field{key, v}
}

func NumberPField[T Number](key string, v *T) Field {
	return NamedPField(key, v)
}

func PositiveIntegerPField[T PositiveInteger](key string, v *T) Field {
	return NamedPField(key, v)
}

func IntPField[T Integer](key string, v *T) Field {
	return NamedPField(key, v)
}

func BoolNumberPField(key string, b *bool) Field {
	v := 0
	if b != nil {
		if *b {
			v = 1
		}
	}

	return NamedField(key, v)
}

func StringField(key string, v string) Field {
	return Field{key, v}
}

var (
	ToTitle = cases.Title(language.English)
	ToLower = cases.Lower(language.English)
	ToUpper = cases.Upper(language.English)
)

func TitleStringField(key string, v string) Field {
	return Field{key, ToTitle.String(v)}
}

func LowerStringField(key string, v string) Field {
	return Field{key, ToLower.String(v)}
}

func UpperStringField(key string, v string) Field {
	return Field{key, ToUpper.String(v)}
}

func StringPField(key string, v *string) Field {
	return NamedPField(key, v)
}

// TitleStringPField is a function that creates a field with a title-cased string pointer value.
func TitleStringPField(key string, v *string) Field {
	if v == nil {
		return Field{key, ""}
	}
	return Field{key, ToTitle.String(*v)}
}

// LowerStringPField is a function that creates a field with a lower-cased string pointer value.
func LowerStringPField(key string, v *string) Field {
	if v == nil {
		return Field{key, ""}
	}
	return Field{key, strings.ToLower(*v)}
}

func StringPPField[T String](key *string, v *T) Field {
	return NamedPField(PString(key), v)
}

// NamedPField is a function that creates a named field with a pointer value.
// It takes a key string and a pointer to a value of type Number or string.
// If the pointer is nil, it creates a field with a zero value of the specified type.
// Otherwise, it creates a field with the dereferenced value of the pointer.
// The function returns a Field struct with the specified key and value.
func NamedPField[T ValueType](key string, v *T) Field {
	return Field{key, Value[T](v)}
}

type (
	FieldFunc                 func(v any) Field
	StringFieldFunc           func(v string) Field
	NumberFieldFunc[T Number] func(v T) Field
)

func NewStringFieldFunc(key string) StringFieldFunc {
	return func(v string) Field {
		return StringField(key, v)
	}
}

func NewStringApplyFieldFunc(key string, f func(string) string) StringFieldFunc {
	return func(v string) Field {
		return StringField(key, f(v))
	}
}

func NewNumberFieldFunc[T Number](key string) NumberFieldFunc[T] {
	return func(v T) Field {
		return NumberField(key, v)
	}
}

func NewIntFieldFunc(key string) NumberFieldFunc[int] {
	return NewNumberFieldFunc[int](key)
}

func NewFieldFunc(key string) FieldFunc {
	return func(v any) Field {
		return NamedField(key, v)
	}
}
