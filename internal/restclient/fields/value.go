package fields

import (
	"fmt"
	"strconv"
	"strings"

	"golang.org/x/exp/constraints"
)

type Number interface {
	constraints.Float | constraints.Integer
}

type Integer interface {
	constraints.Integer
}

type PositiveInteger interface {
	constraints.Unsigned
}

type String interface {
	~string
}

type ValueType interface {
	Number | String | bool
}

// Pointer takes a value of type T and returns a pointer to the value.
func Pointer[T ValueType](v T) *T {
	return &v
}

func Int(v int) *int {
	return &v
}

func Int8(v int8) *int8 {
	return &v
}

func Int16(v int16) *int16 {
	return &v
}

func Int32(v int32) *int32 {
	return Pointer(v)
}

func Int64(v int64) *int64 {
	return Pointer(v)
}

func Uint(v uint) *uint {
	return Pointer(v)
}

func Uint8(v uint8) *uint8 {
	return Pointer(v)
}

func Uint16(v uint16) *uint16 {
	return Pointer(v)
}

func Uint32(v uint32) *uint32 {
	return Pointer(v)
}

func Uint64(v uint64) *uint64 {
	return Pointer(v)
}

// Value is a generic function that returns the value of a pointer to a number or string type.
// If the pointer is nil, it returns the zero value of the type.
// Otherwise, it dereferences the pointer and returns the underlying value.
// The type of the pointer must be either a number or a string.
func Value[T ValueType](v *T) T {
	if v == nil {
		var vv T
		return vv
	}
	return *v
}

func PNumber[T Number](i *T) T {
	return Value(i)
}

func PString(s *string) string {
	return Value(s)
}

func PUpperString(s *string) string {
	return strings.ToUpper(Value(s))
}

// PLowerString is a function that takes a pointer to a string and returns the lowercase version of the string.
func PLowerString(s *string) string {
	return strings.ToLower(Value(s))
}

func Stringify[T Number](v T) string {
	return fmt.Sprintf("%v", v)
}

func StringFormat[T Number](format string, v T) string {
	return fmt.Sprintf(format, v)
}

func FloatString[T constraints.Float](v T) string {
	return StringFormat("%f", v)
}

func IntString[T constraints.Integer](v T) string {
	return StringFormat("%d", v)
}

func ToInt(v string) *int {
	if n, err := strconv.Atoi(v); err == nil {
		return &n
	}
	return nil
}

// PValueApply is a generic function that takes a pointer to a value of type T and a function f that operates on values of type T.
// It calls the PValue function to get the value of type T from the pointer, and then applies the function f to the value.
// The result of applying f to the value is returned.
// The type T can be either a Number or a string.
func PValueApply[T ValueType](v *T, f func(T) T) T {
	a := Value(v)
	return f(a)
}

func PValueApplyEqual[T ValueType](v *T, f func(T) T, b T) bool {
	return PValueApply(v, f) == b
}

func PEqual[T ValueType](v *T, b T) bool {
	if v == nil {
		return false
	}
	return *v == b
}

func PNumberCalculation[T Number](v *T, f func(T) T) T {
	return PValueApply(v, f)
}

func PDividedNumber[T Number](v *T, divisor T) T {
	return PNumberCalculation(v, func(v T) T { return v / divisor })
}

// SetValue is a function that sets the value of a field in the given Fields object.
// The type of the value can be a Number or a string.
// If the value is not nil, it is set in the Fields object using the specified key,
// otherwise, it does nothing.
func SetValue[T ValueType](fz Fields, key string, value *T) {
	if value != nil {
		fz.Set(key, *value)
	}
}

// SetValueOrDefault is a generic function that sets the value of a field in the given Fields object.
// The type parameter T can be either Number or string.
// If the value is nil, it sets the default value of type T for the field.
// Otherwise, it sets the provided value for the field.
func SetValueOrDefault[T ValueType](fz Fields, key string, value *T) {
	fz.Set(key, Value(value))
}

func SetValueOrNil[T ValueType](fz Fields, key string, value *T) {
	if value == nil {
		fz.Set(key, nil)
	}
	fz.Set(key, Value(value))
}

// SetAnyValue sets the first non-nil value to the field
func SetAnyValue[T ValueType](fz Fields, key string, values ...*T) {
	for _, value := range values {
		if value != nil {
			fz.Set(key, *value)
			return
		}
	}
}

// SetAnyValueOrDefault sets the first non-nil value to the field, or the default value if all values are nil
func SetAnyValueOrDefault[T ValueType](fz Fields, key string, defaultValue T, values ...*T) {
	for _, value := range values {
		if value != nil {
			fz.Set(key, *value)
			return
		}
	}
	fz.Set(key, defaultValue)
}

func SetNull(fz Fields, key string) {
	fz.Set(key, nil)
}

// SetEmpty sets the value of the specified key in the given Fields object to an empty string.
// It takes a Fields object (fz) and a key string as parameters.
func SetEmpty(fz Fields, key string) {
	fz.Set(key, "")
}
