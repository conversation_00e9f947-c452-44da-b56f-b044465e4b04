package fields

import "time"

type ApplyFunc func(value any) any

func (fs Fields) SetApply(key string, value any, apply ApplyFunc) {
	if value != nil {
		fs.Set(key, apply(value))
	} else {
		fs.Set(key, value)
	}
}

func ApplyTimestamp(value any) any {
	return time.Unix(value.(int64), 0).Format(time.RFC3339)
}

func ApplyTimestampMilli(value any) any {
	return time.UnixMilli(value.(int64)).Format(time.RFC3339)
}

func ApplyTimestampMicro(value any) any {
	return time.UnixMicro(value.(int64)).Format(time.RFC3339)
}
