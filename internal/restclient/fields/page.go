package fields

const (
	PageLimitFieldKey  = "limit"
	PageNumberFieldKey = "page"
	PageSizeFieldKey   = "page_size"

	AgentVersionFieldKey = "agent_version"
)

var (
	LimitField = NewIntFieldFunc(PageLimitFieldKey)
	Unlimited  = LimitField(9999) // CAUTION! maximum value is 9999

	PageNumField  = NewIntFieldFunc("page")
	PageSizeField = NewIntFieldFunc("page_size")

	AgentVersionField = NewFieldFunc("agent_version")
)
