package fields

import "strings"

const (
	IDFieldKey               = "id"
	ExternalUUIDFieldKey     = "external_uuid"
	ExternalNameFieldKey     = "external_name"
	ExternalFlavorFieldKey   = "external_flavor"
	ExternalHostNameFieldKey = "external_hostname"
	ExternalStatusFieldKey   = "external_status"
	ExternalTagsFieldKey     = "external_tags"

	ExternalACKClusterIDFieldKey   = "external_ack_cluster_id"
	ExternalACKClusterNameFieldKey = "external_ack_cluster_name"

	StatusFieldKey = "status"

	CPUFieldKey     = "cpu"
	MemoryFieldKey  = "mem"
	OSFieldKey      = "os"
	OSNameFieldKey  = "os_name"
	ImageIDFieldKey = "image_id"

	VpcFieldKey = "vpc"

	PublicIPFieldKey  = "public_ip"
	PrivateIPFieldKey = "private_ip"

	InstanceIDFieldKey   = "instance_id"
	InstanceNameFieldKey = "instance_name"

	RegionFieldKey         = "region"
	ZoneFieldKey           = "zone"
	VendorFieldKey         = "vendor"
	FactoryFieldKey        = "factory"
	FactoryAccountFieldKey = "account"

	FactoryIDFieldKey   = "factory_id"
	FactoryNameFieldKey = "facroty_name"
	ZoneIDFieldKey      = "zone_id"
	ZoneTypeFieldKey    = "zone_type"
	RegionIDFieldKey    = "region_id"

	ProductFieldKey      = "product"
	ProjectFieldKey      = "project"
	GCPProjectIDFieldKey = "gcp_project_id"

	CreateTimeFieldKey  = "create_time"
	ExpiredTimeFieldKey = "expired_time"
	DescFieldKey        = "desc"
	ChargeTypeFieldKey  = "charge_type"
)

var (
	// IDField is a field with name "id"
	IDField = NewIntFieldFunc(IDFieldKey)

	ExternalUUIDField     = NewStringFieldFunc(ExternalUUIDFieldKey)
	ExternalNameField     = NewFieldFunc(ExternalNameFieldKey)
	ExternalFlavorField   = NewFieldFunc(ExternalFlavorFieldKey)
	ExternalHostNameField = NewFieldFunc(ExternalHostNameFieldKey)

	RawExternalStatusField   = NewStringFieldFunc(ExternalStatusFieldKey)
	LowerExternalStatusField = NewStringApplyFieldFunc(ExternalStatusFieldKey, strings.ToLower)
	UpperExternalStatusField = NewStringApplyFieldFunc(ExternalStatusFieldKey, strings.ToUpper)
	ExternalStatusField      = UpperExternalStatusField

	// ExternalStatusDeleted is a field with name "external_status" and value "DELETED"
	ExternalStatusDeleted    = ExternalStatusField("deleted")
	ExternalStatusTerminated = ExternalStatusField("terminated")
	ExternalStatusRunning    = ExternalStatusField("running")
	ExternalStatusStopped    = ExternalStatusField("stopped")

	ExternalTagsField = NewFieldFunc(ExternalTagsFieldKey)

	RawStatusField   = NewStringFieldFunc(StatusFieldKey)
	LowerStatusField = NewStringApplyFieldFunc(StatusFieldKey, strings.ToLower)
	UpperStatusField = NewStringApplyFieldFunc(StatusFieldKey, strings.ToUpper)
	StatusField      = UpperStatusField

	NumberStatusField = NewIntFieldFunc(StatusFieldKey)

	// virtual filed
	ExternalACKClusterIDField   = NewFieldFunc(ExternalACKClusterIDFieldKey)
	ExternalACKClusterNameField = NewFieldFunc(ExternalACKClusterNameFieldKey)

	// CPUField = NewFieldFunc(CPUFieldKey)
	// MemoryField  = NewFieldFunc(MemoryFieldKey)

	OSField      = NewFieldFunc(OSFieldKey)
	OSNameField  = NewFieldFunc(OSNameFieldKey)
	ImageIDField = NewStringFieldFunc(ImageIDFieldKey)

	VpcField = NewFieldFunc(VpcFieldKey)

	PublicIPField  = NewStringFieldFunc(PublicIPFieldKey)
	PrivateIPField = NewStringFieldFunc(PrivateIPFieldKey)

	InstanceIDField   = NewFieldFunc(InstanceIDFieldKey)
	InstanceNameField = NewFieldFunc(InstanceNameFieldKey)

	RegionField         = NewFieldFunc(RegionFieldKey)
	ZoneField           = NewFieldFunc(ZoneFieldKey)
	ZoneIDField         = NewFieldFunc(ZoneIDFieldKey)
	ZoneTypeField       = NewFieldFunc(ZoneTypeFieldKey)
	VendorField         = NewFieldFunc(VendorFieldKey)
	FactoryField        = NewFieldFunc(FactoryFieldKey)
	FactoryAccountField = NewFieldFunc(FactoryAccountFieldKey)

	FactoryIDField   = NewFieldFunc(FactoryIDFieldKey)
	FactoryNameField = NewFieldFunc(FactoryNameFieldKey)
	RegionIDField    = NewFieldFunc(RegionIDFieldKey)

	ProductField      = NewFieldFunc(ProductFieldKey)
	ProjectField      = NewFieldFunc(ProjectFieldKey)
	GCPProjectIDField = NewFieldFunc(GCPProjectIDFieldKey)

	CreateTimeField  = NewFieldFunc(CreateTimeFieldKey)
	ExpiredTimeField = NewFieldFunc(ExpiredTimeFieldKey)

	DescField       = NewFieldFunc(DescFieldKey)
	ChargeTypeField = NewFieldFunc(ChargeTypeFieldKey)
)

func CPUField[T Number](v T) Field {
	return NumberField(CPUFieldKey, v)
}

func MemoryField[T Number](v T) Field {
	return NumberField(MemoryFieldKey, v)
}
