package fields

import (
	"fmt"
	"strings"
)

// SuffixedField 返回一个带有后缀的字段。
//
//	参数 key 是字段的键名。
//	参数 val 是字段的值。
//	参数 suffix 是字段的后缀。
//	返回一个带有后缀的字段。
func SuffixedField(key string, val any, suffix string) Field {
	return NamedField(key+"__"+suffix, val)
}

// Contains wrap filed with contains filter
//
//	Contains("name", "foo") => Field{Name: "name__contains", Value: "foo"} => name__contains=foo
func Contains(key string, val any) Field {
	return SuffixedField(key, val, "contains")
}

func IContains(key string, val any) Field {
	return SuffixedField(key, val, "icontains")
}

// In wrap filed with in filter
//
//	In("name", "foo") => Field{Name: "name__in", Value: "foo"} => name__in=foo
func In(key string, vals ...any) Field {
	v := make([]string, 0)
	for _, val := range vals {
		v = append(v, fmt.Sprintf("%v", val))
	}
	return SuffixedField(key, strings.Join(v, ","), "in")
}

func Not(key string, val any) Field {
	return SuffixedField(key, val, "not")
}

// WithPrefix wrap filed with prefix filter
//
//	WithPrefix("name", "foo") => Field{Name: "name__startswith", Value: "foo"} => name__startswith=foo
func WithPrefix(key string, val any) Field {
	return SuffixedField(key, val, "startswith")
}

func LessThan(key string, val any) Field {
	return SuffixedField(key, val, "lt")
}

func LessThanEqual(key string, val any) Field {
	return SuffixedField(key, val, "lte")
}

// nullValue wrap filed with prefix filter
//
//	nullValue("name", true) => Field{Name: "name__isnull", Value: true} => name__isnull=true
//	nullValue("name", false) => Field{Name: "name__isnull", Value: false} => name__isnull=false
func nullValue(key string, val bool) Field {
	return SuffixedField(key, val, "isnull")
}

func NotNull(key string) Field {
	return nullValue(key, false)
}

func IsNull(key string) Field {
	return nullValue(key, true)
}
