// Code generated by oma-codegen. DO NOT EDIT.
// source: models/machine.proto

package models

import (
	time "time"
)

// 云主机资源定义，与Go struct Machine对应
type Machine struct {
	ID                int        `json:"id"`                   // 主机唯一标识ID
	ZoneName          string     `json:"zone__name"`           // 可用区名称
	RegionName        string     `json:"region__name"`         // 区域名称
	RegionRegionID    string     `json:"region__region_id"`    // 区域ID
	FactoryName       string     `json:"factory__name"`        // 云厂商名称
	FactoryKMSAccount string     `json:"factory__kms_account"` // 云厂商KMS账户
	ProductName       string     `json:"product__name"`        // 产品名称
	CreateAt          int        `json:"create_at"`            // 创建时间戳
	UpdateAt          int        `json:"update_at"`            // 更新时间戳
	ExternalUUID      string     `json:"external_uuid"`        // 外部UUID
	ExternalName      string     `json:"external_name"`        // 外部名称
	ExternalHostname  string     `json:"external_hostname"`    // 外部主机名
	ExternalStatus    string     `json:"external_status"`      // 外部状态
	ExternalFlavor    string     `json:"external_flavor"`      // 外部规格
	CPU               int64      `json:"cpu"`                  // CPU核心数
	Mem               float64    `json:"mem"`                  // 内存大小(GB)
	OSName            string     `json:"os_name"`              // 操作系统名称
	ChargeType        string     `json:"charge_type"`          // 计费类型
	Status            int64      `json:"status"`               // 状态
	PublicIP          string     `json:"public_ip"`            // 公网IP
	PrivateIP         string     `json:"private_ip"`           // 内网IP
	Subnet            any        `json:"subnet"`               // 子网ID
	UpdateTime        time.Time  `json:"update_time"`          // 更新时间
	CreateTime        *time.Time `json:"create_time"`          // 创建时间
	GCPProjectID      string     `json:"gcp_project_id"`       // GCP项目ID
	ManualProduct     int64      `json:"manual_product"`       // 手动产品ID
	ExternalTags      string     `json:"external_tags"`        // 外部标签
	ExpiredTime       any        `json:"expired_time"`
	ExpiredTimeDesc   any        `json:"expired_time_desc"`
	AgentID           string     `json:"agent_id"`      // Agent ID
	AgentVersion      string     `json:"agent_version"` // Agent版本
	Flavor            any        `json:"flavor"`        // 规格名称
	Zone              int        `json:"zone"`          // 可用区ID
	Product           *int       `json:"product"`       // 产品ID
	Project           *int       `json:"project"`       // 项目ID
	Group             any        `json:"group"`         // 规格名称
	UserGroup         any        `json:"user_group"`    // 规格名称
	Factory           int        `json:"factory"`       // 云厂商ID
	Account           *int       `json:"account"`       // 账户ID
	AccountName       string     `json:"account__name"` // 账户名称
	Desc              string     `json:"desc"`
	Label             []any      `json:"label"` // 标签列表
}

const ResourceMachine ResourceType = "machine"

// GetID 返回Machine的ID
func (r Machine) GetID() int {
	return r.ID
}

// Type 返回资源的类型
func (Machine) Type() ResourceType {
	return ResourceMachine
}
