// Code generated by oma-codegen. DO NOT EDIT.
// source: models/polardb.proto

package models

// PolarDB数据库实例资源定义
type Polar struct {
	ID             int    `json:"id"`              // PolarDB实例唯一标识ID
	RegionName     string `json:"region__name"`    // 区域名称
	FactoryName    string `json:"factory__name"`   // 云厂商名称
	CreateAt       int    `json:"create_at"`       // 创建时间戳
	UpdateAt       int    `json:"update_at"`       // 更新时间戳
	ExternalName   string `json:"external_name"`   // 外部名称
	ExternalUUID   string `json:"external_uuid"`   // 外部UUID
	ExternalStatus string `json:"external_status"` // 外部状态
	ClusterType    string `json:"cluster_type"`    // 集群类型
	Region         *int   `json:"region"`          // 区域ID
	Project        *int   `json:"project"`         // 项目ID
	Factory        int    `json:"factory"`         // 云厂商ID
	Account        *int   `json:"account"`         // 账户ID
	Product        *int   `json:"product"`         // 产品ID
}

const ResourcePolar ResourceType = "polardb"

// GetID 返回Polar的ID
func (r Polar) GetID() int {
	return r.ID
}

// Type 返回资源的类型
func (Polar) Type() ResourceType {
	return ResourcePolar
}

// PolarDB白名单资源定义
type PolarDBWhitelist struct {
	ID        int    `json:"id"`         // 白名单唯一标识ID
	CreateAt  int    `json:"create_at"`  // 创建时间戳
	UpdateAt  int    `json:"update_at"`  // 更新时间戳
	GroupName string `json:"group_name"` // 组名
	IPList    string `json:"ip_list"`    // IP列表
	DB        string `json:"db"`         // 关联的数据库
}

const ResourcePolarDBWhitelist ResourceType = "polardb_whitelist"

// GetID 返回PolarDBWhitelist的ID
func (r PolarDBWhitelist) GetID() int {
	return r.ID
}

// Type 返回资源的类型
func (PolarDBWhitelist) Type() ResourceType {
	return ResourcePolarDBWhitelist
}
