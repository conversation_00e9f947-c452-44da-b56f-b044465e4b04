// Code generated by oma-codegen. DO NOT EDIT.
// source: models/lb_server.proto

package models

import (
	time "time"
)

// 负载均衡服务器组资源定义
type LBServerGroup struct {
	ID         int       `json:"id"`          // 服务器组唯一标识ID
	GroupID    string    `json:"group_id"`    // 服务器组外部ID
	GroupName  string    `json:"group_name"`  // 服务器组名称
	LB         string    `json:"lb"`          // 关联的负载均衡器ID
	CreateTime time.Time `json:"create_time"` // 创建时间
	CreateAt   int       `json:"create_at"`   // 创建时间戳
	UpdateAt   int       `json:"update_at"`   // 更新时间戳
	Factory    int       `json:"factory"`     // 云厂商ID
	Account    *int      `json:"account"`     // 账户ID
}

const ResourceLBServerGroup ResourceType = "lb_server_groups"

// GetID 返回LBServerGroup的ID
func (r LBServerGroup) GetID() int {
	return r.ID
}

// Type 返回资源的类型
func (LBServerGroup) Type() ResourceType {
	return ResourceLBServerGroup
}

// 负载均衡服务器资源定义
type LBServer struct {
	ID          int    `json:"id"`           // 服务器唯一标识ID
	LB          string `json:"lb"`           // 关联的负载均衡器ID
	ServerGroup string `json:"server_group"` // 关联的服务器组ID
	ServerID    string `json:"server_id"`    // 服务器外部ID
	ServerType  string `json:"server_type"`  // 服务器类型
	IP          string `json:"ip"`           // IP地址
	Port        int64  `json:"port"`         // 端口号
	Weight      int64  `json:"weight"`       // 权重
	Desc        string `json:"desc"`         // 描述
	CreateAt    int    `json:"create_at"`    // 创建时间戳
	UpdateAt    int    `json:"update_at"`    // 更新时间戳
}

const ResourceLBServer ResourceType = "lb_server"

// GetID 返回LBServer的ID
func (r LBServer) GetID() int {
	return r.ID
}

// Type 返回资源的类型
func (LBServer) Type() ResourceType {
	return ResourceLBServer
}
