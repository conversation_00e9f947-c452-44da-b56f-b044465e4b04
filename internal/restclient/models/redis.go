// Code generated by oma-codegen. DO NOT EDIT.
// source: models/redis.proto

package models

import (
	time "time"
)

// Redis缓存实例资源定义
type Redis struct {
	ID             int       `json:"id"`              // Redis实例唯一标识ID
	FactoryName    string    `json:"factory__name"`   // 云厂商名称
	AccountName    string    `json:"account__name"`   // 账户名称
	RegionName     string    `json:"region__name"`    // 区域名称
	ProductName    string    `json:"product__name"`   // 产品名称
	CreateAt       int       `json:"create_at"`       // 创建时间戳
	UpdateAt       int       `json:"update_at"`       // 更新时间戳
	ExternalName   string    `json:"external_name"`   // 外部名称
	ExternalUUID   string    `json:"external_uuid"`   // 外部UUID
	ExternalStatus string    `json:"external_status"` // 外部状态
	Available      bool      `json:"available"`       // 是否可用
	Types          int       `json:"types"`           // 类型
	Arch           string    `json:"arch"`            // 架构
	Engine         string    `json:"engine"`          // 引擎
	Version        string    `json:"version"`         // 版本
	Edition        string    `json:"edition"`         // 版本
	Conn           string    `json:"conn"`            // 连接信息
	Port           uint16    `json:"port"`            // 端口号
	PrivateIP      string    `json:"private_ip"`      // 内网IP
	PublicIP       string    `json:"public_ip"`       // 公网IP
	Capacity       int       `json:"capacity"`        // 容量
	QPS            int       `json:"qps"`             // QPS性能
	CreateTime     time.Time `json:"create_time"`     // 创建时间
	Factory        int       `json:"factory"`         // 云厂商ID
	Account        int       `json:"account"`         // 账户ID
	Project        *int      `json:"project"`         // 项目ID
	Product        int       `json:"product"`         // 产品ID
	Region         int       `json:"region"`          // 区域ID
	Tags           []string  `json:"tags"`            // 标签列表
}

const ResourceRedis ResourceType = "redis"

// GetID 返回Redis的ID
func (r Redis) GetID() int {
	return r.ID
}

// Type 返回资源的类型
func (Redis) Type() ResourceType {
	return ResourceRedis
}
