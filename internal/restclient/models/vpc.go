// Code generated by oma-codegen. DO NOT EDIT.
// source: models/vpc.proto

package models

// 专有网络
type VPC struct {
	ID          int    `json:"id"`            // VPC唯一标识ID
	CreateAt    int64  `json:"create_at"`     // 创建时间戳
	UpdateAt    int64  `json:"update_at"`     // 更新时间戳
	VpcID       string `json:"vpc_id"`        // 外部VPC ID
	Name        string `json:"name"`          // VPC名称
	Desc        string `json:"desc"`          // VPC描述
	CidrBlock   string `json:"cidr_block"`    // CIDR块
	Region      *int   `json:"region"`        // 区域ID
	FactoryName string `json:"factory__name"` // 云厂商名称
	Factory     int    `json:"factory"`       // 云厂商ID
	Account     *int   `json:"account"`       // 账户ID
}

const ResourceVPC ResourceType = "vpc"

// GetID 返回VPC的ID
func (r VPC) GetID() int {
	return r.ID
}

// Type 返回资源的类型
func (VPC) Type() ResourceType {
	return ResourceVPC
}
