package models

type ResourceType string

const (
	ResourceMachine        ResourceType = "machine"
	ResourceK8sMachine     ResourceType = "k8s_machine"
	ResourceK8sCluster     ResourceType = "k8s"
	ResourceK8sNodePool    ResourceType = "k8s_nodepool"
	ResourceK8sNode        ResourceType = "k8s_node"
	ResourceFactory        ResourceType = "factory"
	ResourceFactoryAccount ResourceType = "account"
	ResourceProduct        ResourceType = "product"
	ResourceRegion         ResourceType = "region"
	ResourceZone           ResourceType = "zone"
	ResourceProject        ResourceType = "project"
	ResourceMachineFlavor  ResourceType = "flavor"
	ResourceEMRCluster     ResourceType = "emr"

	ResourceMySQL            ResourceType = "mysql"
	ResourceMySQLWhitelist   ResourceType = "mysql_whitelist"
	ResourceRedis            ResourceType = "redis"
	ResourceRedisWhitelist   ResourceType = "redis_whitelist"
	ResourceMongo            ResourceType = "mongo"
	ResourceMongoWhitelist   ResourceType = "mongo_whitelist"
	ResourceHBase            ResourceType = "hbase"
	ResourceHBaseWhitelist   ResourceType = "hbase_whitelist"
	ResourcePolarDB          ResourceType = "polardb"
	ResourcePolarDBWhitelist ResourceType = "polardb_whitelist"

	ResourceBlockDisk ResourceType = "block_disk"
	ResourceElasticIP ResourceType = "elastic_ip"
	ResourceSLS       ResourceType = "sls"
	ResourceLogstore  ResourceType = "logstore"

	ResourceACL      ResourceType = "acl"
	ResourceACLEntry ResourceType = "acl_entry"

	ResourceSLB         ResourceType = "slb"
	ResourceSLBListener ResourceType = "slb_listener"
	ResourceALB         ResourceType = ResourceSLB + "/" + "alb"
	ResourceCLB         ResourceType = ResourceSLB + "/" + "clb"
	ResourceNLB         ResourceType = ResourceSLB + "/" + "nlb"

	ResourceLBServerGroup ResourceType = "lb_server_groups"
	ResourceLBServer      ResourceType = "lb_server"

	ResourceSubnet  ResourceType = "subnet"
	ResourceVpc     ResourceType = "vpc"
	ResourceVSwitch ResourceType = "vswitch"

	ResourceOther ResourceType = "other"

	ResourceGroup                ResourceType = "group"
	ResourceImage                ResourceType = "image"
	ResourceSecurity             ResourceType = "security"
	ResourceRule                 ResourceType = "rule"
	ResourceCert                 ResourceType = "cert"
	ResourceDomain               ResourceType = "domain"
	ResourceDomainRecord         ResourceType = "domain_record"
	ResourceTag                  ResourceType = "tag"
	ResourceWhitelist            ResourceType = "whitelist"
	ResourcePark                 ResourceType = "park"
	ResourceBuilding             ResourceType = "building"
	ResourceOperator             ResourceType = "operator"
	ResourceExportIP             ResourceType = "export_ip"
	ResourceUserGroup            ResourceType = "user_group"
	ResourceRole                 ResourceType = "role"
	ResourceUserroleGroup        ResourceType = "user_role_group"
	ResourceHistory              ResourceType = "history"
	ResourceDmenu                ResourceType = "dmenu"
	ResourceObjectStorage        ResourceType = "object_storage"
	ResourceObjectStorageProvide ResourceType = "object_storage_provider"
	ResourceCdnProvider          ResourceType = "cdn_provider"
	ResourceCdnDomain            ResourceType = "cdn_domain"
	ResourceCdnPath              ResourceType = "cdn_path"
	ResourceCacert               ResourceType = "cacert"
)

type Resourcer interface {
	Type() ResourceType

	// GetID returns the ID of the resource, used by Get.
	GetID() int
}

type ProductResource interface {
	Resourcer

	GetProductID() *int
}
