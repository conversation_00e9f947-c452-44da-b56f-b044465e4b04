// Code generated by oma-codegen. DO NOT EDIT.
// source: models/hbase.proto

package models

// HBase数据库实例资源定义
type HBase struct {
	ID             int    `json:"id"`              // HBase实例唯一标识ID
	RegionName     string `json:"region_name"`     // 区域名称
	FactoryName    string `json:"factory_name"`    // 云厂商名称
	CreateAt       int64  `json:"create_at"`       // 创建时间戳
	UpdateAt       int64  `json:"update_at"`       // 更新时间戳
	ExternalName   string `json:"external_name"`   // 外部名称
	ExternalUUID   string `json:"external_uuid"`   // 外部UUID
	ExternalStatus string `json:"external_status"` // 外部状态
	LocalStatus    int    `json:"local_status"`    // 本地状态
	InstanceType   int    `json:"instance_type"`   // 实例类型
	ResourceType   string `json:"resource_type"`   // 资源类型
	Region         *int   `json:"region"`          // 区域ID
	Project        *int   `json:"project"`         // 项目ID
	Factory        int    `json:"factory"`         // 云厂商ID
	Account        *int   `json:"account"`         // 账户ID
	Product        *int   `json:"product"`         // 产品ID
}

const ResourceHBase ResourceType = "hbase"

// GetID 返回HBase的ID
func (r HBase) GetID() int {
	return r.ID
}

// Type 返回资源的类型
func (HBase) Type() ResourceType {
	return ResourceHBase
}

// HBase白名单资源定义
type HBaseWhitelist struct {
	ID        int    `json:"id"`         // 白名单唯一标识ID
	CreateAt  int    `json:"create_at"`  // 创建时间戳
	UpdateAt  int    `json:"update_at"`  // 更新时间戳
	GroupName string `json:"group_name"` // 组名
	IPList    string `json:"ip_list"`    // IP列表
	DB        string `json:"db"`         // 关联的数据库
}

const ResourceHBaseWhitelist ResourceType = "hbase_whitelist"

// GetID 返回HBaseWhitelist的ID
func (r HBaseWhitelist) GetID() int {
	return r.ID
}

// Type 返回资源的类型
func (HBaseWhitelist) Type() ResourceType {
	return ResourceHBaseWhitelist
}
