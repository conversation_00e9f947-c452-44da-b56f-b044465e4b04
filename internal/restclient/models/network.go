// Code generated by oma-codegen. DO NOT EDIT.
// source: models/network.proto

package models

// 子网资源定义
type Subnet struct {
	ID               int    `json:"id"`                 // 子网唯一标识ID
	VpcVpcName       string `json:"vpc__vpc_name"`      // VPC名称
	VpcVpcID         string `json:"vpc__vpc_id"`        // VPC ID
	VpcVpcFactory    string `json:"vpc__vpc_factory"`   // VPC云厂商
	FactoryName      string `json:"factory__name"`      // 云厂商名称
	CreateAt         int    `json:"create_at"`          // 创建时间戳
	UpdateAt         int    `json:"update_at"`          // 更新时间戳
	SubnetID         string `json:"subnet_id"`          // 子网外部ID
	Name             string `json:"name"`               // 子网名称
	AliasName        string `json:"alias_name"`         // 别名
	Desc             string `json:"desc"`               // 描述
	CidrBlock        string `json:"cidr_block"`         // CIDR块
	IsDefault        bool   `json:"is_default"`         // 是否默认子网
	AvailableIPCount int    `json:"available_ip_count"` // 可用IP数量
	ResourceAutoSync int    `json:"resource_auto_sync"` // 资源自动同步
	Vpc              int    `json:"vpc"`                // VPC ID
	Zone             any    `json:"zone"`
}

const ResourceSubnet ResourceType = "subnet"

// GetID 返回Subnet的ID
func (r Subnet) GetID() int {
	return r.ID
}

// Type 返回资源的类型
func (Subnet) Type() ResourceType {
	return ResourceSubnet
}

// 虚拟交换机资源定义（阿里云）
type VSwitch struct {
	ID            int    `json:"id"`              // 虚拟交换机唯一标识ID
	CreateAt      int64  `json:"create_at"`       // 创建时间戳
	UpdateAt      int64  `json:"update_at"`       // 更新时间戳
	VswitchID     string `json:"vswitch_id"`      // 虚拟交换机外部ID
	Name          string `json:"name"`            // 虚拟交换机名称
	VpcID         string `json:"vpc_id"`          // VPC ID
	Desc          string `json:"desc"`            // 描述
	CidrBlock     string `json:"cidr_block"`      // CIDR块
	IsDefault     bool   `json:"is_default"`      // 是否默认虚拟交换机
	ShareType     string `json:"share_type"`      // 共享类型
	Status        string `json:"status"`          // 状态
	NetworkCidrID string `json:"network_cidr_id"` // 网络CIDR ID
	Region        *int   `json:"region"`          // 区域ID
	Zone          *int   `json:"zone"`            // 可用区ID
	FactoryName   string `json:"factory__name"`   // 云厂商名称
	Factory       int    `json:"factory"`         // 云厂商ID
	Account       *int   `json:"account"`         // 账户ID
}

const ResourceVSwitch ResourceType = "vswitch"

// GetID 返回VSwitch的ID
func (r VSwitch) GetID() int {
	return r.ID
}

// Type 返回资源的类型
func (VSwitch) Type() ResourceType {
	return ResourceVSwitch
}
