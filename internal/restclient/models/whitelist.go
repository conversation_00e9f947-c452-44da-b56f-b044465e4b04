// Code generated by oma-codegen. DO NOT EDIT.
// source: models/whitelist.proto

package models

// MySQL白名单资源定义
type MySQLWhitelist struct {
	ID        int    `json:"id"`         // 白名单唯一标识ID
	CreateAt  int64  `json:"create_at"`  // 创建时间戳
	UpdateAt  int64  `json:"update_at"`  // 更新时间戳
	GroupName string `json:"group_name"` // 组名
	IPType    string `json:"ip_type"`    // IP类型
	IPList    string `json:"ip_list"`    // IP列表
	DB        string `json:"db"`         // 关联的数据库
}

const ResourceMySQLWhitelist ResourceType = "mysql_whitelist"

// GetID 返回MySQLWhitelist的ID
func (r MySQLWhitelist) GetID() int {
	return r.ID
}

// Type 返回资源的类型
func (MySQLWhitelist) Type() ResourceType {
	return ResourceMySQLWhitelist
}

// Redis白名单资源定义
type RedisWhitelist struct {
	ID        int    `json:"id"`         // 白名单唯一标识ID
	CreateAt  int64  `json:"create_at"`  // 创建时间戳
	UpdateAt  int64  `json:"update_at"`  // 更新时间戳
	GroupName string `json:"group_name"` // 组名
	IPType    string `json:"ip_type"`    // IP类型
	IPList    string `json:"ip_list"`    // IP列表
	DB        string `json:"db"`         // 关联的数据库
}

const ResourceRedisWhitelist ResourceType = "redis_whitelist"

// GetID 返回RedisWhitelist的ID
func (r RedisWhitelist) GetID() int {
	return r.ID
}

// Type 返回资源的类型
func (RedisWhitelist) Type() ResourceType {
	return ResourceRedisWhitelist
}

// MongoDB白名单资源定义
type MongoWhitelist struct {
	ID        int    `json:"id"`         // 白名单唯一标识ID
	CreateAt  int64  `json:"create_at"`  // 创建时间戳
	UpdateAt  int64  `json:"update_at"`  // 更新时间戳
	GroupName string `json:"group_name"` // 组名
	IPList    string `json:"ip_list"`    // IP列表
	DB        string `json:"db"`         // 关联的数据库
}

const ResourceMongoWhitelist ResourceType = "mongo_whitelist"

// GetID 返回MongoWhitelist的ID
func (r MongoWhitelist) GetID() int {
	return r.ID
}

// Type 返回资源的类型
func (MongoWhitelist) Type() ResourceType {
	return ResourceMongoWhitelist
}
