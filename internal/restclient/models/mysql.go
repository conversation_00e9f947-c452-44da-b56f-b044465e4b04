// Code generated by oma-codegen. DO NOT EDIT.
// source: models/mysql.proto

package models

import (
	time "time"
)

// MySQL数据库实例资源定义
type MySQL struct {
	ID             int       `json:"id"`              // MySQL实例唯一标识ID
	RegionName     string    `json:"region__name"`    // 区域名称
	FactoryName    string    `json:"factory__name"`   // 云厂商名称
	AccountName    string    `json:"account__name"`   // 账户名称
	ProductName    string    `json:"product__name"`   // 产品名称
	CreateAt       int       `json:"create_at"`       // 创建时间戳
	UpdateAt       int       `json:"update_at"`       // 更新时间戳
	ExternalName   string    `json:"external_name"`   // 外部名称
	ExternalUUID   string    `json:"external_uuid"`   // 外部UUID
	ExternalStatus string    `json:"external_status"` // 外部状态
	LocalStatus    int       `json:"local_status"`    // 本地状态
	Conn           string    `json:"conn"`            // 连接信息
	Port           *int      `json:"port"`            // 端口号
	CPU            *int      `json:"cpu"`             // CPU数量
	Mem            *int      `json:"mem"`             // 内存大小(MB)
	Disk           *int      `json:"disk"`            // 磁盘大小(GB)
	IOPS           *int      `json:"iops"`            // IOPS
	Version        string    `json:"version"`         // MySQL版本
	FlavorName     string    `json:"flavor_name"`     // 规格名称
	CreateTime     time.Time `json:"create_time"`     // 创建时间
	Region         int       `json:"region"`          // 区域ID
	Subnet         any       `json:"subnet"`
	Factory        int       `json:"factory"` // 云厂商ID
	Account        *int      `json:"account"` // 账户ID
	Product        *int      `json:"product"` // 产品ID
	Project        []int     `json:"project"` // 项目列表
	Tags           []string  `json:"tags"`    // 标签列表
	Label          []any     `json:"label"`   // 标签列表
	Sg             []any     `json:"sg"`      // 安全组列表
}

const ResourceMySQL ResourceType = "mysql"

// GetID 返回MySQL的ID
func (r MySQL) GetID() int {
	return r.ID
}

// Type 返回资源的类型
func (MySQL) Type() ResourceType {
	return ResourceMySQL
}
