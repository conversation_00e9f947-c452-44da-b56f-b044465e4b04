// Code generated by oma-codegen. DO NOT EDIT.
// source: models/image.proto

package models

// 镜像资源定义
type Image struct {
	ID             int    `json:"id"`                // 镜像唯一标识ID
	RegionName     string `json:"region__name"`      // 区域名称
	RegionRegionID string `json:"region__region_id"` // 区域ID
	FactoryName    string `json:"factory__name"`     // 云厂商名称
	CreateAt       int64  `json:"create_at"`         // 创建时间戳
	UpdateAt       int64  `json:"update_at"`         // 更新时间戳
	ExternalID     string `json:"external_id"`       // 外部ID
	ExternalName   string `json:"external_name"`     // 外部名称
	ExternalDesc   string `json:"external_desc"`     // 外部描述
	ExternalOsname string `json:"external_osname"`   // 外部操作系统名称
	ImageType      string `json:"image_type"`        // 镜像类型
	LocalDesc      string `json:"local_desc"`        // 本地描述
	ProjectID      string `json:"project_id"`        // 项目ID
	Region         int    `json:"region"`            // 区域ID
	Factory        int    `json:"factory"`           // 云厂商ID
}

const ResourceImage ResourceType = "image"

// GetID 返回Image的ID
func (r Image) GetID() int {
	return r.ID
}

// Type 返回资源的类型
func (Image) Type() ResourceType {
	return ResourceImage
}
