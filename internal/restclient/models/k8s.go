// Code generated by oma-codegen. DO NOT EDIT.
// source: models/k8s.proto

package models

// K8sCluster 代表 Kubernetes 集群
type K8SCluster struct {
	ID             int    `json:"id"`              // 主键ID
	ClusterID      string `json:"cluster_id"`      // 集群ID
	ClusterName    string `json:"cluster_name"`    // 集群名称
	ClusterVersion string `json:"cluster_version"` // 集群版本
	Factory        int    `json:"factory"`         // 工厂ID
	Account        int    `json:"account"`         // 账号ID
	FactoryName    string `json:"factory_name"`    // 工厂名称
	AccountName    string `json:"account_name"`    // 账号名称
	CreatedAt      int    `json:"created_at"`      // 创建时间
	UpdatedAt      int    `json:"updated_at"`      // 更新时间
}

const ResourceK8SCluster ResourceType = "k8s"

// GetID 返回K8SCluster的ID
func (r K8SCluster) GetID() int {
	return r.ID
}

// Type 返回资源的类型
func (K8SCluster) Type() ResourceType {
	return ResourceK8SCluster
}

// K8sNodePool 代表 Kubernetes 节点池
type K8SNodePool struct {
	ID          int    `json:"id"`
	PoolID      string `json:"pool_id"`      // 节点池 ID
	PoolName    string `json:"pool_name"`    // 节点池名称
	Cluster     string `json:"cluster"`      // 集群ID
	ClusterName string `json:"cluster_name"` // 集群名称
	CreatedAt   int    `json:"created_at"`   // 创建时间
	UpdatedAt   int    `json:"updated_at"`   // 更新时间
}

const ResourceK8SNodePool ResourceType = "k8s_nodepool"

// GetID 返回K8SNodePool的ID
func (r K8SNodePool) GetID() int {
	return r.ID
}

// Type 返回资源的类型
func (K8SNodePool) Type() ResourceType {
	return ResourceK8SNodePool
}

// K8sNode 代表 Kubernetes 节点
type K8SNode struct {
	ID           int    `json:"id"`
	InstanceID   string `json:"instance_id"`   // 实例 ID
	InstanceName string `json:"instance_name"` // 实例名称
	Cluster      string `json:"cluster"`       // 集群ID
	ClusterName  string `json:"cluster_name"`  // 集群名称
	Pool         string `json:"pool"`          // 节点池ID
	PoolName     string `json:"pool_name"`     // 节点池名称
	CreatedAt    int    `json:"created_at"`    // 创建时间
	UpdatedAt    int    `json:"updated_at"`    // 更新时间
}

const ResourceK8SNode ResourceType = "k8s_node"

// GetID 返回K8SNode的ID
func (r K8SNode) GetID() int {
	return r.ID
}

// Type 返回资源的类型
func (K8SNode) Type() ResourceType {
	return ResourceK8SNode
}
