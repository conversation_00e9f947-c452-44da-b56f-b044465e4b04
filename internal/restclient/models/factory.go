// Code generated by oma-codegen. DO NOT EDIT.
// source: models/factory.proto

package models

// 云厂商资源定义
type Factory struct {
	ID         int    `json:"id"`          // 云厂商唯一标识ID
	CreateAt   int64  `json:"create_at"`   // 创建时间戳
	UpdateAt   int64  `json:"update_at"`   // 更新时间戳
	Name       string `json:"name"`        // 云厂商名称
	KeyName    string `json:"key_name"`    // 密钥名称
	Channel    string `json:"channel"`     // 渠道
	Desc       string `json:"desc"`        // 描述
	AccessID   string `json:"access_id"`   // 访问ID
	AccessKey  string `json:"access_key"`  // 访问密钥
	Rolearn    string `json:"rolearn"`     // 角色ARN
	KMSAccount string `json:"kms_account"` // KMS账户
}

const ResourceFactory ResourceType = "factory"

// GetID 返回Factory的ID
func (r Factory) GetID() int {
	return r.ID
}

// Type 返回资源的类型
func (Factory) Type() ResourceType {
	return ResourceFactory
}

// 云厂商账户资源定义
type FactoryAccount struct {
	ID         int    `json:"id"`          // 账户唯一标识ID
	CreateAt   int64  `json:"create_at"`   // 创建时间戳
	UpdateAt   int64  `json:"update_at"`   // 更新时间戳
	Name       string `json:"name"`        // 账户名称
	KeyName    string `json:"key_name"`    // 密钥名称
	Channel    string `json:"channel"`     // 渠道
	Desc       string `json:"desc"`        // 描述
	AccessID   string `json:"access_id"`   // 访问ID
	AccessKey  string `json:"access_key"`  // 访问密钥
	Rolearn    string `json:"rolearn"`     // 角色ARN
	KMSAccount string `json:"kms_account"` // KMS账户
	Factory    int    `json:"factory"`     // 关联的云厂商ID
}

const ResourceFactoryAccount ResourceType = "account"

// GetID 返回FactoryAccount的ID
func (r FactoryAccount) GetID() int {
	return r.ID
}

// Type 返回资源的类型
func (FactoryAccount) Type() ResourceType {
	return ResourceFactoryAccount
}
