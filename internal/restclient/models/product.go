// Code generated by oma-codegen. DO NOT EDIT.
// source: models/product.proto

package models

// 产品资源定义
type Product struct {
	ID         int    `json:"id"`          // 产品唯一标识ID
	ExternalID string `json:"external_id"` // 外部ID
	Name       string `json:"name"`        // 产品名称
	Desc       string `json:"desc"`        // 产品描述
	UpdateAt   int64  `json:"update_at"`   // 更新时间戳
	CreateAt   int64  `json:"create_at"`   // 创建时间戳
}

const ResourceProduct ResourceType = "product"

// GetID 返回Product的ID
func (r Product) GetID() int {
	return r.ID
}

// Type 返回资源的类型
func (Product) Type() ResourceType {
	return ResourceProduct
}
