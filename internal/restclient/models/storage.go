// Code generated by oma-codegen. DO NOT EDIT.
// source: models/storage.proto

package models

import (
	time "time"
)

// 块存储磁盘资源定义
type BlockDisk struct {
	ID               int    `json:"id"`                 // 磁盘唯一标识ID
	CreateAt         int64  `json:"create_at"`          // 创建时间戳
	UpdateAt         int64  `json:"update_at"`          // 更新时间戳
	DiskID           string `json:"disk_id"`            // 磁盘外部ID
	Name             string `json:"name"`               // 磁盘名称
	Size             int    `json:"size"`               // 磁盘大小(GB)
	DiskType         string `json:"disk_type"`          // 磁盘类型
	InstanceID       string `json:"instance_id"`        // 绑定的实例ID
	LastBindInstance string `json:"last_bind_instance"` // 上次绑定的实例ID
	Status           string `json:"status"`             // 磁盘状态
	Region           string `json:"region"`             // 区域
	Zone             string `json:"zone"`               // 可用区
	Category         string `json:"category"`           // 类别
	Tags             string `json:"tags"`               // 标签
	CreateTime       string `json:"create_time"`        // 创建时间
	Factory          int    `json:"factory"`            // 云厂商ID
	FactoryName      string `json:"factory_name"`       // 云厂商名称
	Account          *int   `json:"account"`            // 账户ID
	AccountName      string `json:"account_name"`       // 账户名称
}

const ResourceBlockDisk ResourceType = "block_disk"

// GetID 返回BlockDisk的ID
func (r BlockDisk) GetID() int {
	return r.ID
}

// Type 返回资源的类型
func (BlockDisk) Type() ResourceType {
	return ResourceBlockDisk
}

// 日志服务资源定义
type SLS struct {
	ID             int       `json:"id"`              // SLS项目唯一标识ID
	FactoryName    string    `json:"factory__name"`   // 云厂商名称
	AccountName    string    `json:"account__name"`   // 账户名称
	RegionName     string    `json:"region__name"`    // 区域名称
	CreateAt       int64     `json:"create_at"`       // 创建时间戳
	UpdateAt       int64     `json:"update_at"`       // 更新时间戳
	ExternalName   string    `json:"external_name"`   // 外部名称
	ExternalUUID   string    `json:"external_uuid"`   // 外部UUID
	ExternalStatus string    `json:"external_status"` // 外部状态
	Desc           string    `json:"desc"`            // 描述
	CreateTime     time.Time `json:"create_time"`     // 创建时间
	Region         int       `json:"region"`          // 区域ID
	Factory        int       `json:"factory"`         // 云厂商ID
	Account        *int      `json:"account"`         // 账户ID
	Product        *int      `json:"product"`         // 产品ID
}

const ResourceSLS ResourceType = "sls"

// GetID 返回SLS的ID
func (r SLS) GetID() int {
	return r.ID
}

// Type 返回资源的类型
func (SLS) Type() ResourceType {
	return ResourceSLS
}

// 日志库资源定义
type LogStore struct {
	ID             int    `json:"id"`                // 日志库唯一标识ID
	Name           string `json:"name"`              // 日志库名称
	Shards         int    `json:"shards"`            // 分片数量
	SLSProject     int    `json:"sls_project"`       // SLS项目ID
	SLSProjectName string `json:"sls_project__name"` // SLS项目名称
	Factory        int    `json:"factory"`           // 云厂商ID
	Account        int    `json:"account"`           // 账户ID
	CreateAt       int    `json:"create_at"`         // 创建时间戳
	UpdateAt       int    `json:"update_at"`         // 更新时间戳
	TTL            *int   `json:"ttl"`               // 日志保留时间(天)
	HotTTL         *int   `json:"hot_ttl"`           // 热数据保留时间(天)
}

const ResourceLogStore ResourceType = "logstore"

// GetID 返回LogStore的ID
func (r LogStore) GetID() int {
	return r.ID
}

// Type 返回资源的类型
func (LogStore) Type() ResourceType {
	return ResourceLogStore
}
