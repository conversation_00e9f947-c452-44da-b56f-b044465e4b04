// Code generated by oma-codegen. DO NOT EDIT.
// source: models/mongo.proto

package models

// MongoDB数据库实例资源定义
type Mongo struct {
	ID             int    `json:"id"`               // MongoDB实例唯一标识ID
	RegionName     string `json:"region__name"`     // 区域名称
	CreateAt       int64  `json:"create_at"`        // 创建时间戳
	UpdateAt       int64  `json:"update_at"`        // 更新时间戳
	ExternalName   string `json:"external_name"`    // 外部名称
	ExternalUUID   string `json:"external_uuid"`    // 外部UUID
	ExternalStatus string `json:"external_status"`  // 外部状态
	Status         int64  `json:"status"`           // 状态
	DBType         string `json:"db_type"`          // 数据库类型
	ReplicaSetName string `json:"replica_set_name"` // 副本集名称
	Disk           int64  `json:"disk"`             // 磁盘大小(GB)
	CPU            int64  `json:"cpu"`              // CPU数量
	Mem            int64  `json:"mem"`              // 内存大小(MB)
	IOPS           int64  `json:"iops"`             // IOPS
	Connections    int64  `json:"connections"`      // 连接数
	Version        string `json:"version"`          // MongoDB版本
	FlavorName     string `json:"flavor_name"`      // 规格名称
	PrimaryConn    string `json:"primary_conn"`     // 主节点连接信息
	PrimaryPort    int    `json:"primary_port"`     // 主节点端口
	SecondaryConn  string `json:"secondary_conn"`   // 从节点连接信息
	SecondaryPort  int    `json:"secondary_port"`   // 从节点端口
	ReadonlyConn   string `json:"readonly_conn"`    // 只读节点连接信息
	ReadonlyPort   int    `json:"readonly_port"`    // 只读节点端口
	ShardConn      string `json:"shard_conn"`       // 分片连接信息
	MongosConn     string `json:"mongos_conn"`      // Mongos连接信息
	MongosPort     int    `json:"mongos_port"`      // Mongos端口
	CreateTime     string `json:"create_time"`      // 创建时间
	Region         int    `json:"region"`           // 区域ID
	Factory        int    `json:"factory"`          // 云厂商ID
	FactoryName    string `json:"factory__name"`    // 云厂商名称
	Account        *int   `json:"account"`          // 账户ID
	AccountName    string `json:"account__name"`    // 账户名称
	Project        *int   `json:"project"`          // 项目ID
	Product        *int   `json:"product"`          // 产品ID
	Label          []any  `json:"label"`            // 标签列表
}

const ResourceMongo ResourceType = "mongo"

// GetID 返回Mongo的ID
func (r Mongo) GetID() int {
	return r.ID
}

// Type 返回资源的类型
func (Mongo) Type() ResourceType {
	return ResourceMongo
}
