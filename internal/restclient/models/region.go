// Code generated by oma-codegen. DO NOT EDIT.
// source: models/region.proto

package models

// 云区域资源定义
type Region struct {
	ID             int    `json:"id"`              // 区域唯一标识ID
	Factory        int    `json:"factory"`         // 云厂商ID
	FactoryName    string `json:"factory__name"`   // 云厂商名称
	Name           string `json:"name"`            // 区域名称
	RegionID       string `json:"region_id"`       // 区域外部ID
	VendorDistrict string `json:"vendor_district"` // 供应商区域
	CreateAt       int    `json:"create_at"`       // 创建时间戳
	UpdateAt       int    `json:"update_at"`       // 更新时间戳
	Desc           string `json:"desc"`            // 描述
}

const ResourceRegion ResourceType = "region"

// GetID 返回Region的ID
func (r Region) GetID() int {
	return r.ID
}

// Type 返回资源的类型
func (Region) Type() ResourceType {
	return ResourceRegion
}
