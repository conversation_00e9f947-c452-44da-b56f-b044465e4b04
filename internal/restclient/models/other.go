// Code generated by oma-codegen. DO NOT EDIT.
// source: models/other.proto

package models

// 其他资源类型定义，用于表示不属于特定类别的通用资源
type Other struct {
	ID             int    `json:"id"`              // 资源唯一标识ID
	RegionName     string `json:"region__name"`    // 区域名称
	FactoryName    string `json:"factory__name"`   // 云厂商名称
	CreateAt       int    `json:"create_at"`       // 创建时间戳
	UpdateAt       int    `json:"update_at"`       // 更新时间戳
	ExternalName   string `json:"external_name"`   // 外部资源名称
	ExternalUUID   string `json:"external_uuid"`   // 外部资源UUID
	ExternalStatus string `json:"external_status"` // 外部资源状态
	LocalStatus    int    `json:"local_status"`    // 本地状态
	InstanceType   int    `json:"instance_type"`   // 实例类型
	ResourceType   string `json:"resource_type"`   // 资源类型
	Region         *int   `json:"region"`          // 区域ID
	Project        *int   `json:"project"`         // 项目ID
	Factory        int    `json:"factory"`         // 云厂商ID
	Account        *int   `json:"account"`         // 账户ID
	Product        *int   `json:"product"`         // 产品ID
}

const ResourceOther ResourceType = "other"

// GetID 返回Other的ID
func (r Other) GetID() int {
	return r.ID
}

// Type 返回资源的类型
func (Other) Type() ResourceType {
	return ResourceOther
}
