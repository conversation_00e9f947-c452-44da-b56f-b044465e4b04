// Code generated by oma-codegen. DO NOT EDIT.
// source: models/security.proto

package models

// 安全组资源定义
type Security struct {
	ID              int    `json:"id"`               // 安全组唯一标识ID
	Name            string `json:"name"`             // 安全组名称
	VpcVpcID        string `json:"vpc__vpc_id"`      // VPC外部ID
	VpcName         string `json:"vpc__name"`        // VPC名称
	RulesCount      int64  `json:"rules_count"`      // 规则数量
	SecurityID      string `json:"security_id"`      // 安全组外部ID
	EcsCount        int64  `json:"ecs_count"`        // 关联的ECS数量
	AvailableAmount int64  `json:"available_amount"` // 可用数量
	GroupType       string `json:"group_type"`       // 组类型
	ServiceManaged  *bool  `json:"service_managed"`  // 是否服务托管
	Desc            string `json:"desc"`             // 描述
	Vpc             int64  `json:"vpc"`              // VPC ID
	Factory         int64  `json:"factory"`          // 云厂商ID
	FactoryName     string `json:"factory__name"`    // 云厂商名称
	Account         *int64 `json:"account"`          // 账户ID
	AccountName     string `json:"account__name"`    // 账户名称
	CreateAt        int64  `json:"create_at"`        // 创建时间戳
	UpdateAt        int64  `json:"update_at"`        // 更新时间戳
}

const ResourceSecurity ResourceType = "security"

// GetID 返回Security的ID
func (r Security) GetID() int {
	return r.ID
}

// Type 返回资源的类型
func (Security) Type() ResourceType {
	return ResourceSecurity
}
