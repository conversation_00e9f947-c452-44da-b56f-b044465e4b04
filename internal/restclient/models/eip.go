// Code generated by oma-codegen. DO NOT EDIT.
// source: models/eip.proto

package models

// 弹性IP资源定义
type ElasticIP struct {
	ID             int    `json:"id"`              // EIP唯一标识ID
	CreateAt       int    `json:"create_at"`       // 创建时间戳
	UpdateAt       int    `json:"update_at"`       // 更新时间戳
	Name           string `json:"name"`            // EIP名称
	Version        string `json:"version"`         // 版本
	ISP            string `json:"isp"`             // 网络供应商
	ExternalUUID   string `json:"external_uuid"`   // 外部UUID
	ExternalStatus string `json:"external_status"` // 外部状态
	State          string `json:"state"`           // 状态
	PublicIP       string `json:"public_ip"`       // 公网IP地址
	BindID         string `json:"bind_id"`         // 绑定资源ID
	Product        *int   `json:"product"`         // 产品ID
	BindType       string `json:"bind_type"`       // 绑定类型
	LastBindID     string `json:"last_bind_id"`    // 上次绑定资源ID
	LastBindType   string `json:"last_bind_type"`  // 上次绑定类型
	Factory        int    `json:"factory"`         // 云厂商ID
	FactoryName    string `json:"factory__name"`   // 云厂商名称
	Account        *int   `json:"account"`         // 账户ID
	AccountName    string `json:"account__name"`   // 账户名称
	CreateTime     string `json:"create_time"`     // 创建时间
}

const ResourceElasticIP ResourceType = "eip"

// GetID 返回ElasticIP的ID
func (r ElasticIP) GetID() int {
	return r.ID
}

// Type 返回资源的类型
func (ElasticIP) Type() ResourceType {
	return ResourceElasticIP
}
