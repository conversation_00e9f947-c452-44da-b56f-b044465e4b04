// Code generated by oma-codegen. DO NOT EDIT.
// source: models/flavor.proto

package models

// 机器规格资源定义
type Flavor struct {
	ID          int     `json:"id"`           // 规格唯一标识ID
	FactoryName string  `json:"factory_name"` // 云厂商名称
	Name        string  `json:"name"`         // 规格名称
	Desc        string  `json:"desc"`         // 描述
	FlavorID    *string `json:"flavor_id"`    // 规格外部ID
	CPU         int     `json:"cpu"`          // CPU核心数
	Mem         int     `json:"mem"`          // 内存大小(MB)
	Disk        int     `json:"disk"`         // 磁盘大小(GB)
	Price       int     `json:"price"`        // 价格
	Factory     int     `json:"factory"`      // 云厂商ID
	CreateAt    int     `json:"create_at"`    // 创建时间戳
	UpdateAt    int     `json:"update_at"`    // 更新时间戳
}

const ResourceFlavor ResourceType = "flavor"

// GetID 返回Flavor的ID
func (r Flavor) GetID() int {
	return r.ID
}

// Type 返回资源的类型
func (Flavor) Type() ResourceType {
	return ResourceFlavor
}
