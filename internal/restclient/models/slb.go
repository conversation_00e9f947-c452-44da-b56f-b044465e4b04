// Code generated by oma-codegen. DO NOT EDIT.
// source: models/slb.proto

package models

import (
	time "time"
)

// 负载均衡器和监听器资源定义
type SLBListener struct {
	ID             int    `json:"id"`              // 监听器唯一标识ID
	ExternalName   string `json:"external_name"`   // 外部名称
	ExternalStatus string `json:"external_status"` // 外部状态
	Proto          string `json:"proto"`           // 协议
	Port           int    `json:"port"`            // 端口
	PortForward    int    `json:"port_forward"`    // 转发端口
	AclUUID        string `json:"acl_uuid"`        // ACL UUID
	AclType        string `json:"acl_type"`        // ACL类型
	AclStatus      string `json:"acl_status"`      // ACL状态
	Scheduler      string `json:"scheduler"`       // 调度算法
	LB             string `json:"lb"`              // 关联的负载均衡器
	CreateAt       int64  `json:"create_at"`       // 创建时间戳
	UpdateAt       int64  `json:"update_at"`       // 更新时间戳
}

const ResourceSLBListener ResourceType = "slb_listener"

// GetID 返回SLBListener的ID
func (r SLBListener) GetID() int {
	return r.ID
}

// Type 返回资源的类型
func (SLBListener) Type() ResourceType {
	return ResourceSLBListener
}

// EIP 弹性IP信息
type EIPInfo struct {
	ExternalUUID string `json:"external_uuid"` // 外部UUID
	PublicIP     string `json:"public_ip"`     // 公网IP
	ISP          string `json:"isp"`           // 网络供应商
}

// SLB 负载均衡器
type SLB struct {
	ID                    int           `json:"id"`                       // SLB唯一标识ID
	SlbID                 string        `json:"slb_id"`                   // SLB外部ID (external_uuid)
	RegionName            string        `json:"region__name"`             // 区域名称
	RegionRegionID        string        `json:"region__region_id"`        // 区域ID
	FactoryName           string        `json:"factory__name"`            // 云厂商名称
	AccountName           string        `json:"account__name"`            // 账户名称
	ProductName           string        `json:"product__name"`            // 产品名称
	ProductAdmin          string        `json:"product__admin"`           // 产品管理员
	ProductAdminFirstname string        `json:"product__admin_firstname"` // 产品管理员名字
	CreateAt              int64         `json:"create_at"`                // 创建时间戳
	UpdateAt              int64         `json:"update_at"`                // 更新时间戳
	LBType                string        `json:"lb_type"`                  // 负载均衡器类型
	ExternalName          string        `json:"external_name"`            // 外部名称
	Address               string        `json:"address"`                  // 地址
	ExternalStatus        string        `json:"external_status"`          // 外部状态
	NetworkType           string        `json:"network_type"`             // 网络类型
	IPVersion             string        `json:"ip_version"`               // IP版本
	AddressType           string        `json:"address_type"`             // 地址类型
	GCPProjectID          int64         `json:"gcp_project_id"`           // GCP项目ID
	CreateTime            time.Time     `json:"create_time"`              // 创建时间
	Vpc                   int64         `json:"vpc"`                      // VPC ID
	VpcVpcID              string        `json:"vpc__vpc_id"`              // VPC外部ID
	VpcName               string        `json:"vpc__name"`                // VPC名称
	Region                *int          `json:"region"`                   // 区域ID
	Factory               int           `json:"factory"`                  // 云厂商ID
	Account               *int          `json:"account"`                  // 账户ID
	Product               *int          `json:"product"`                  // 产品ID
	Project               *int          `json:"project"`                  // 项目ID
	Label                 []any         `json:"label"`                    // 标签列表
	Listeners             []SLBListener `json:"listeners"`                // 监听器列表
	EIPs                  []EIPInfo     `json:"eips"`                     // 弹性IP列表
}

const ResourceSLB ResourceType = "slb"

// GetID 返回SLB的ID
func (r SLB) GetID() int {
	return r.ID
}

// Type 返回资源的类型
func (SLB) Type() ResourceType {
	return ResourceSLB
}
