// Code generated by oma-codegen. DO NOT EDIT.
// source: models/emr.proto

package models

import (
	time "time"
)

// EMR集群资源定义
type EMRCluster struct {
	ID          int        `json:"id"`           // EMR集群唯一标识ID
	ClusterID   string     `json:"cluster_id"`   // 集群外部ID
	Name        string     `json:"name"`         // 集群名称
	Version     string     `json:"version"`      // 集群版本
	Factory     int64      `json:"factory"`      // 云厂商ID
	Account     int64      `json:"account"`      // 账户ID
	Product     int64      `json:"product"`      // 产品ID
	RegionID    string     `json:"region_id"`    // 区域ID
	FactoryName string     `json:"factory_name"` // 云厂商名称
	AccountName string     `json:"account_name"` // 账户名称
	CreateTime  *time.Time `json:"create_time"`  // 创建时间
	CreateAt    int        `json:"create_at"`    // 创建时间戳
	UpdateAt    int        `json:"update_at"`    // 更新时间戳
}

const ResourceEMRCluster ResourceType = "emr"

// GetID 返回EMRCluster的ID
func (r EMRCluster) GetID() int {
	return r.ID
}

// Type 返回资源的类型
func (EMRCluster) Type() ResourceType {
	return ResourceEMRCluster
}
