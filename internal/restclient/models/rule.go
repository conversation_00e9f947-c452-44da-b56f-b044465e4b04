// Code generated by oma-codegen. DO NOT EDIT.
// source: models/rule.proto

package models

import (
	time "time"
)

type Rule struct {
	ID              int       `json:"id"`                // 规则唯一标识ID
	Security        int       `json:"security"`          // 安全组ID
	SecurityGroupID string    `json:"security_group_id"` // 安全组外部ID
	Name            string    `json:"name"`              // 规则名称
	RuleID          string    `json:"rule_id"`           // 规则外部ID
	Policy          string    `json:"policy"`            // 策略（允许/拒绝）
	Direction       string    `json:"direction"`         // 方向（入站/出站）
	Protocol        string    `json:"protocol"`          // 协议（TCP/UDP/ICMP)
	Priority        int       `json:"priority"`          // 优先级
	IP              string    `json:"ip"`                // IP地址
	Port            string    `json:"port"`              // 端口号
	DestPort        string    `json:"dest_port"`         // 目标端口号
	CreateTime      time.Time `json:"create_time"`       // 创建时间
	CreateAt        int       `json:"create_at"`         // 创建时间戳
	UpdateAt        int       `json:"update_at"`         // 更新时间戳
}

const ResourceRule ResourceType = "rule"

// GetID 返回Rule的ID
func (r Rule) GetID() int {
	return r.ID
}

// Type 返回资源的类型
func (Rule) Type() ResourceType {
	return ResourceRule
}
