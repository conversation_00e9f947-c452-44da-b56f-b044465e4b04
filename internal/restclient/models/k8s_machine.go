// Code generated by oma-codegen. DO NOT EDIT.
// source: models/k8s_machine.proto

package models

type K8SMachine struct {
	ID           int    `json:"id"`
	Vendor       string `json:"vendor"`
	AccountName  string `json:"account_name"`
	InstanceID   string `json:"instance_id"`
	InstanceName string `json:"instance_name"`
	ClusterID    string `json:"cluster_id"`
	ClusterName  string `json:"cluster_name"`
	CreatedAt    int    `json:"created_at"`
	UpdatedAt    int    `json:"updated_at"`
}

const ResourceK8SMachine ResourceType = "k8s_machine"

// GetID 返回K8SMachine的ID
func (r K8SMachine) GetID() int {
	return r.ID
}

// Type 返回资源的类型
func (K8SMachine) Type() ResourceType {
	return ResourceK8SMachine
}
