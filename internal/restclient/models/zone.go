// Code generated by oma-codegen. DO NOT EDIT.
// source: models/zone.proto

package models

// 可用区资源定义
type Zone struct {
	ID          int    `json:"id"`            // 可用区唯一标识ID
	Name        string `json:"name"`          // 可用区名称
	ZoneID      string `json:"zone_id"`       // 可用区外部ID
	ZoneType    string `json:"zone_type"`     // 可用区类型
	Factory     int    `json:"factory"`       // 云厂商ID
	FactoryName string `json:"factory__name"` // 云厂商名称
	Region      int    `json:"region"`        // 区域ID
	RegionName  string `json:"region__name"`  // 区域名称
	Desc        string `json:"desc"`          // 描述
	CreateAt    int64  `json:"create_at"`     // 创建时间戳
	UpdateAt    int64  `json:"update_at"`     // 更新时间戳
}

const ResourceZone ResourceType = "zone"

// GetID 返回Zone的ID
func (r Zone) GetID() int {
	return r.ID
}

// Type 返回资源的类型
func (Zone) Type() ResourceType {
	return ResourceZone
}
