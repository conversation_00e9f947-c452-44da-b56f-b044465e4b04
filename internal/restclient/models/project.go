// Code generated by oma-codegen. DO NOT EDIT.
// source: models/project.proto

package models

// 项目资源定义
type Project struct {
	ID               int    `json:"id"`                  // 项目唯一标识ID
	ProductName      string `json:"product__name"`       // 产品名称
	AdminUsername    string `json:"admin__username"`     // 管理员用户名
	AdminFirstName   string `json:"admin__first_name"`   // 管理员名字
	OpsUserUsername  string `json:"ops_user__username"`  // 运维用户用户名
	OpsUserFirstName string `json:"ops_user_first_name"` // 运维用户名字
	CreateAt         int64  `json:"create_at"`           // 创建时间戳
	UpdateAt         int64  `json:"update_at"`           // 更新时间戳
	Name             string `json:"name"`                // 项目名称
	Desc             string `json:"desc"`                // 项目描述
	ProjectID        string `json:"project_id"`          // 项目外部ID
	Admin            int64  `json:"admin"`               // 管理员ID
	OpsUser          int64  `json:"ops_user"`            // 运维用户ID
	Product          int64  `json:"product"`             // 产品ID
	Tags             int64  `json:"tags"`                // 标签ID
	Subnet           any    `json:"subnet"`              // 子网ID
}

const ResourceProject ResourceType = "project"

// GetID 返回Project的ID
func (r Project) GetID() int {
	return r.ID
}

// Type 返回资源的类型
func (Project) Type() ResourceType {
	return ResourceProject
}
