// Code generated by oma-codegen. DO NOT EDIT.
// source: models/acl.proto

package models

// 访问控制列表资源定义
type ACL struct {
	ID                   int    `json:"id"`                     // ACL唯一标识ID
	CreateAt             int64  `json:"create_at"`              // 创建时间戳
	UpdateAt             int64  `json:"update_at"`              // 更新时间戳
	AclID                string `json:"acl_id"`                 // ACL外部ID
	AclName              string `json:"acl_name"`               // ACL名称
	IPVersion            string `json:"ip_version"`             // IP版本
	ResourceGroupID      string `json:"resource_group_id"`      // 资源组ID
	AclStatus            string `json:"acl_status"`             // ACL状态
	ConfigManagedEnabled bool   `json:"config_managed_enabled"` // 配置管理是否启用
	EntryCounts          int64  `json:"entry_counts"`           // 条目数量
	CreateTime           string `json:"create_time"`            // 创建时间
	Factory              *int   `json:"factory"`                // 云厂商ID
	Account              *int   `json:"account"`                // 账户ID
}

const ResourceACL ResourceType = "acl"

// GetID 返回ACL的ID
func (r ACL) GetID() int {
	return r.ID
}

// Type 返回资源的类型
func (ACL) Type() ResourceType {
	return ResourceACL
}

// ACL条目资源定义
type ACLEntry struct {
	ID          int    `json:"id"`          // ACL条目唯一标识ID
	Entry       string `json:"entry"`       // 条目内容
	Status      string `json:"status"`      // 状态
	Description string `json:"description"` // 描述
	Acl         string `json:"acl"`         // 关联的ACL
	CreateAt    int64  `json:"create_at"`   // 创建时间戳
	UpdateAt    int64  `json:"update_at"`   // 更新时间戳
}

const ResourceACLEntry ResourceType = "acl_entry"

// GetID 返回ACLEntry的ID
func (r ACLEntry) GetID() int {
	return r.ID
}

// Type 返回资源的类型
func (ACLEntry) Type() ResourceType {
	return ResourceACLEntry
}
