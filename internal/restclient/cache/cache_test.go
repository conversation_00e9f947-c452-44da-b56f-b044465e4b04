package cache

import (
	"testing"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
)

func TestRegionCacher(t *testing.T) {
	c := NewRegionCacher("test",
		fields.FactoryField(1),
	)

	c<PERSON>("foo", &models.Region{Name: "foo"})

	m := c.Load("foo")
	if m == nil {
		t.<PERSON>rror("expect foo, got nil")
	} else {
		if v, ok := m.(*models.Region); ok {
			t.Logf("m is %+v, type of %T", v, v)
		} else {
			t.<PERSON>("expect type of *models.Region, got %T", m)
		}
	}
}
