package cache

import (
	"fmt"
	"slices"
	"sync"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
)

type RegionCacher struct {
	namespace string
	// buckets   map[string]models.Region
	buckets sync.Map

	ttl int64
	// lastRefreshTime   int64
	refreshConditions []fields.Field
}

func NewRegionCacher(ns string, conds ...fields.Field) *RegionCacher {
	return &RegionCacher{
		namespace: ns,
		// buckets:           make(map[string]models.Region),
		buckets:           sync.Map{},
		ttl:               60,
		refreshConditions: conds,
	}
}

func (c *RegionCacher) Cache(key string, value any) {
	c.buckets.Store(key, value)
}

func (c *RegionCacher) Load(key string) any {
	v, ok := c.buckets.Load(key)
	if ok {
		return v
	}
	return nil
}

func (c *Region<PERSON>acher) Drop(key string) {
	c.buckets.Delete(key)
}

func (c *Region<PERSON>acher) Refresh() error {
	records, err := restclient.ListAll[models.Region](c.refreshConditions...)
	if err != nil {
		return err
	}

	for _, r := range records.Results {
		c.Cache(r.RegionID, r)
	}

	return nil
}

func (c *RegionCacher) RefreshKey(key string) error {
	conds := append(c.refreshConditions, fields.StringField("region_id", key))

	var r models.Region
	has, err := restclient.Find(&r, conds...)
	if err != nil {
		return err
	}

	if !has {
		return fmt.Errorf("%s does not exists", key)
	}

	c.Cache(key, r)

	return nil
}

func (c *RegionCacher) Size() int {
	count := 0
	c.buckets.Range(func(_, _ any) bool {
		count++
		return true
	})
	return count
}

func (c *RegionCacher) Keys() []string {
	// keys := make([]string, 0, len(c.buckets))
	// for k := range c.buckets {
	// 	keys = append(keys, k)
	// }

	// slices.Sort(keys)
	// return keys

	keys := make([]string, 0)

	c.buckets.Range(func(key, _ any) bool {
		keys = append(keys, key.(string))
		return true
	})

	slices.Sort(keys)
	return keys
}
