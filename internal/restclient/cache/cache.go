package cache

// type CacheOption func(Cacher)

type Cacher interface {
	// Cache the value with the key
	Cache(key string, value any)

	// Refresh the cache
	Refresh() error

	// Refresh the cache
	RefreshKey(key string) error

	// Load get value with the key
	Load(key string) any

	// Drop deletes the cache with the key
	Drop(key string)

	// WithOption(CacheOption) Cacher

	Size() int

	Keys() []string
}
