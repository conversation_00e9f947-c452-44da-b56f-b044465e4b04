package cache

import (
	"sync"
	"time"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
)

type ZoneLoader struct {
	rwlock sync.RWMutex
	zones  map[int]models.Zone
	ttl    time.Duration

	// last loaded time
	last int64
}

var defaultZoneLoader = NewZoneLoader(time.Hour)

func NewZoneLoader(ttl time.Duration) *ZoneLoader {
	return &ZoneLoader{
		rwlock: sync.RWMutex{},
		ttl:    ttl,
	}
}

func (l *ZoneLoader) Load() error {
	l.rwlock.Lock()
	defer l.rwlock.Unlock()

	outdated := time.Since(time.Unix(l.last, 0)) >= l.ttl

	if (l.last == 0) || outdated {
		zones, err := loadZones()
		if err != nil {
			return err
		}

		l.zones = zones
		l.last = time.Now().Unix()
	}

	return nil
}

func loadZones() (map[int]models.Zone, error) {
	page, err := restclient.List[models.Zone](
		fields.Unlimited,
	)
	if err != nil {
		return nil, err
	}

	zoneMap := make(map[int]models.Zone, 0)

	// FIXME: 应该区分vendor
	for _, z := range page.Results {
		zoneMap[z.GetID()] = z
	}

	return zoneMap, nil
}

func (l *ZoneLoader) Get(zoneID string) *models.Zone {
	l.Load()

	l.rwlock.RLock()
	defer l.rwlock.RUnlock()

	for _, zone := range l.zones {
		if zone.ZoneID == zoneID {
			return &zone
		}
	}
	return nil
}

func (l *ZoneLoader) GetWithFactory(zoneID string, factory int) *models.Zone {
	l.Load()

	l.rwlock.RLock()
	defer l.rwlock.RUnlock()

	for _, zone := range l.zones {
		if zone.Factory == factory && zone.ZoneID == zoneID {
			return &zone
		}
	}
	return nil
}

func GetZone(zoneID string) *models.Zone {
	return defaultZoneLoader.Get(zoneID)
}

func GetZoneWithFactory(zoneID string, factory int) *models.Zone {
	return defaultZoneLoader.GetWithFactory(zoneID, factory)
}

func GetAllZones() map[int]models.Zone {
	defaultZoneLoader.Load()

	defaultZoneLoader.rwlock.RLock()
	defer defaultZoneLoader.rwlock.RUnlock()

	return defaultZoneLoader.zones
}

type ZoneFilter func(*models.Zone) bool

func ZoneFilterFactory(factory string) ZoneFilter {
	return func(zone *models.Zone) bool {
		return zone.FactoryName == factory
	}
}

func ZoneFilterFactoryID(id int) ZoneFilter {
	return func(zone *models.Zone) bool {
		return zone.Factory == id
	}
}

func ListZones(opts ...ZoneFilter) []models.Zone {
	defaultZoneLoader.Load()

	defaultZoneLoader.rwlock.RLock()
	defer defaultZoneLoader.rwlock.RUnlock()

	zones := make([]models.Zone, 0)
outer:
	for _, zone := range defaultZoneLoader.zones {
		for _, opt := range opts {
			if !opt(&zone) {
				continue outer
			}
		}

		zones = append(zones, zone)
	}

	return zones
}

func ListFactoryZones(fid int) []models.Zone {
	return ListZones(ZoneFilterFactoryID(fid))
}

func GetFactoryZone(factory int, zid string) *models.Zone {
	defaultZoneLoader.Load()

	for _, zone := range defaultZoneLoader.zones {
		if zone.Factory == factory && zone.ZoneID == zid {
			return &zone
		}
	}

	return nil
}

func GetFactoryZoneName(factory int, name string) *models.Zone {
	defaultZoneLoader.Load()

	for _, zone := range defaultZoneLoader.zones {
		if zone.Factory == factory && zone.Name == name {
			return &zone
		}
	}

	return nil
}
