package cache

import (
	"sync"
	"time"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
)

type FactoryLoader struct {
	lock      sync.RWMutex
	Factories map[string]models.Factory
	TTL       time.Duration

	// last loaded time
	last int64
}

func NewFactoryLoader(ttl time.Duration) *FactoryLoader {
	return &FactoryLoader{
		lock: sync.RWMutex{},
		TTL:  ttl,
	}
}

var defaultFactoryLoader = NewFactoryLoader(time.Minute * 10)

func (l *FactoryLoader) Get(key string) *models.Factory {
	l.Load()

	f, ok := l.Factories[key]
	if ok {
		return &f
	}
	return nil
}

func (l *FactoryLoader) GetByName(name string) *models.Factory {
	l.Load()

	for _, f := range l.Factories {
		if f.Name == name {
			return &f
		}
	}
	return nil
}

func (l *FactoryLoader) Load() error {
	l.lock.Lock()
	defer l.lock.Unlock()

	outdated := time.Since(time.Unix(l.last, 0)) >= l.TTL

	if l.last == 0 || outdated {
		factories, err := loadFactories()
		if err != nil {
			return err
		}

		l.Factories = factories
		l.last = time.Now().Unix()
	}

	return nil
}

func loadFactories() (map[string]models.Factory, error) {
	page, err := restclient.List[models.Factory](
		// filter.NotNull("kms_account"),
		fields.Unlimited,
	)
	if err != nil {
		return nil, err
	}

	factoryNameMap := make(map[string]models.Factory)

	for _, f := range page.Results {
		factoryNameMap[f.KeyName] = f
		// fmt.Printf("append %s => %+v\n", f.KeyName, f)
	}

	return factoryNameMap, nil
}

func GetFactory(key string) *models.Factory {
	return defaultFactoryLoader.Get(key)
}

func GetFactoryByName(name string) *models.Factory {
	return defaultFactoryLoader.GetByName(name)
}
