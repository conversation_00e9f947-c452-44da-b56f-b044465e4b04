package restclient

import (
	"fmt"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
)

func GetAccount(fid int, accountKey string) (*models.FactoryAccount, error) {
	var account models.FactoryAccount

	has, err := Find(&account,
		fields.NamedField("key_name", accountKey),
		fields.FactoryField(fid),
	)
	if err != nil {
		return nil, err
	}

	if !has {
		return nil, fmt.Errorf("account %s not found", accountKey)
	}

	fmt.Printf("account %+v\n", account)

	return &account, nil
}
