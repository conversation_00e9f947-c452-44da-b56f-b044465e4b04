package elkeid

import (
	"bytes"
	_ "embed"
	"encoding/json"
	"errors"
	"io"
	"net/http"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/config"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/util"
)

//go:embed elkeid.sh
var scriptContent string

func NewRegistryPOSTRequest(payload any) (*http.Request, error) {
	bs, _ := json.Marshal(payload)
	body := bytes.NewReader(bs)

	req, err := http.NewRequest(http.MethodPost, config.Config().Registry.BaseUrl, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	req.Header.Add(config.Config().Registry.TokenName, config.Config().Registry.Token)

	return req, nil
}

func NewRegistryGETRequest(params map[string]any) (*http.Request, error) {
	remoteUrl, err := util.BuildURL(config.Config().Registry.BaseUrl, params)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest(http.MethodGet, remoteUrl, nil)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	req.Header.Add(config.Config().Registry.TokenName, config.Config().Registry.Token)

	return req, nil
}

func CreateJob(hosts ...string) (job string, err error) {
	payload := fields.Fields{
		"targets":      hosts,
		"cmd":          "command",
		"script_type":  "bash",
		"setup_script": scriptContent,
		"timeout":      10,
		"expires":      "3m",
	}

	req, err := NewRegistryPOSTRequest(&payload)
	if err != nil {
		return "", err
	}

	res, err := http.DefaultClient.Do(req)
	if err != nil {
		return "", err
	}
	defer res.Body.Close()

	output, err := io.ReadAll(res.Body)
	if err != nil {
		return "", err
	}

	result := new(JobResponse)
	if err := json.Unmarshal(output, result); err != nil {
		return "", err
	}
	if result.JobID == "" {
		return "", errors.New(string(output))
	}

	return result.JobID, nil
}

func GetJob(id string, conds ...fields.Field) (*JobDetailResponse, error) {
	conds = append(conds, fields.StringField("job_id", id))
	req, err := NewRegistryGETRequest(fields.NewFields(conds...))
	if err != nil {
		return nil, err
	}

	resp, err := http.DefaultClient.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	output, _ := io.ReadAll(resp.Body)
	results := new(JobDetailResponse)
	if err := json.Unmarshal(output, results); err != nil {
		return nil, err
	}

	return results, nil
}
