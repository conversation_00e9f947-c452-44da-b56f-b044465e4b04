service_name=elkeid-agent

if command -v systemctl > /dev/null 2>&1; then
    systemctl list-units --all | grep ${service_name} | awk '{print $4}'
elif command -v service > /dev/null 2>&1; then
    output=`service --status-all 2>/dev/null | grep ${service_name}`
    if echo $output | grep -i running > /dev/null 2>&1; then
        echo 'running'
    else
        echo $output | awk '{print $3}'
    fi
fi