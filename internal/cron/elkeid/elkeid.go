package elkeid

import (
	"context"
	"slices"
	"strings"
	"sync"
	"time"

	"gitlab.lilithgame.com/yunwei/pkg/logger"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
)

func Run() error {
	conds := fields.FieldList(
		fields.ExternalStatusRunning,
		fields.NamedField("is_k8s_node", false),
		fields.In("agent_version", "v0.1.10rc6", "0.1.10-rc.7"), // TODO: dirty code
		fields.NotNull("agent_id"),
		fields.NumberField("product__id", 35),
		fields.FactoryField(30),
		fields.FactoryAccountField(51),
	)

	wg := new(sync.WaitGroup)

	for p := 1; ; p++ {
		conds = append(conds, fields.NumberField("page", p))
		page, err := restclient.ListN[models.Machine](50, conds...)
		if err != nil {
			return err
		}
		logger.Debug("total machine to handle", "count", page.Count, "pages", page.TotalPages)

		targets := make([]string, 0)
		machines := make(map[string]*models.Machine)
		for _, machine := range page.Results {
			if agentId := machine.AgentID; agentId != "" {
				targets = append(targets, machine.AgentID)
				machines[machine.AgentID] = &machine
			}
		}

		wg.Add(1)
		go func() error {
			defer wg.Done()

			// 1. fire a job
			id, err := CreateJob(targets...)
			if err != nil {
				return err
			}
			logger.Info("job created", "job_id", id)
			defer func() {
				logger.Info("job finish", "job_id", id)
			}()

			// 2. watch job status and update machine's fields(elkeid related)
			// TODO: hard code
			ctx, cancel := context.WithTimeout(context.Background(), time.Minute*3)
			defer cancel()

			watchAndUpdate(ctx, id, machines)

			return nil
		}()

		if p == page.TotalPages {
			break
		}
	}

	wg.Wait()

	return nil
}

func watchAndUpdate(ctx context.Context, id string, machines map[string]*models.Machine) {
	tick := time.NewTicker(time.Second * 10)
	defer tick.Stop()

	handles := make(map[string]string)

	for {
		select {
		case <-ctx.Done():
			return

		case <-tick.C:
			pending := make([]string, 0)

			for page := 1; ; page++ {
				conds := fields.FieldList(fields.NumberField("page", page))
				job, err := GetJob(id, conds...)
				if err != nil {
					logger.Error("fetch job detail failed", "job", id, "error", err)
					return
				}

				for _, result := range job.Results {
					if slices.Contains([]string{"success", "fail", "expired", "timeout"}, result.Status) {
						status := strings.TrimSpace(result.Result)
						machine := machines[result.Target]

						if _, hanlded := handles[result.Target]; hanlded {
							continue
						}

						// update it
						restclient.PostSubResource[models.Machine]("update_elkeid", machine.GetID(), fields.Fields{
							"elkeid_installed": status != "",
							"elkeid_status":    status,
						})
						handles[result.Target] = machine.ExternalUUID

						logger.Debug("machine elkeid status updated",
							"machine", machine.ExternalName,
							"instance_id", machine.ExternalUUID,
						)
					} else {
						pending = append(pending, result.Target)
					}
				}

				if job.Page == job.TotalPages {
					break
				}
			}

			if len(pending) == 0 {
				return
			}
		}
	}
}
