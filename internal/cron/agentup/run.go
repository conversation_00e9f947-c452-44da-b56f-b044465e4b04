package agentup

import (
	"context"
	_ "embed"
	"sync"
	"time"

	"gitlab.lilithgame.com/yunwei/pkg/logger"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/models"
)

func Run() error {
	conds := fields.FieldList(
		fields.ExternalStatusRunning,
		fields.NamedField("is_k8s_node", false),
		fields.NotNull("agent_id"),
		fields.In("agent_version", "v0.1.7rc5"), // TODO: dirty code
		fields.NumberField("product__id", 4),
		fields.FactoryField(1),
		// fields.FactoryAccountField(48),
	)

	ipAsTarget := true

	wg := new(sync.WaitGroup)

	var err error
	for p := 1; ; p++ {
		conds = append(conds, fields.NumberField("page", p))
		page, lerr := restclient.ListN[models.Machine](50, conds...)
		if lerr != nil {
			err = lerr
			break
		}
		logger.Debug("total machine to handle", "count", page.Count, "pages", page.TotalPages)

		// targets := make([]string, 0)
		targets := make([]models.Machine, 0)
		for _, machine := range page.Results {
			if ipAsTarget {
				if privateIP := machine.PrivateIP; privateIP != "" {
					targets = append(targets, machine)
				}
			} else {
				if agentId := machine.AgentID; agentId != "" {
					targets = append(targets, machine)
				}
			}
		}

		if len(targets) == 0 {
			continue
		}

		wg.Add(1)
		go func() error {
			defer wg.Done()

			// 1. fire a job
			id, err := CreateJob(ipAsTarget, targets...)
			if err != nil {
				logger.Error("create job failed", "error", err)
				return err
			}
			logger.Info("job created", "job_id", id)
			defer func() {
				logger.Info("job finish", "job_id", id)
			}()

			// 2. watch job status and update machine's fields(elkeid related)
			// TODO: hard code
			ctx, cancel := context.WithTimeout(context.Background(), time.Minute*3)
			defer cancel()

			watch(ctx, id)

			return nil
		}()

		if p == page.TotalPages {
			break
		}
	}

	wg.Wait()

	return err
}

func watch(ctx context.Context, id string) {
	tick := time.NewTicker(time.Second * 5)
	defer tick.Stop()

	for {
		select {
		case <-ctx.Done():
			return

		case <-tick.C:
			conds := fields.FieldList(fields.StringField("status", "created"))
			job, err := GetJob(id, conds...)
			if err != nil {
				logger.Error("fetch job detail failed", "job", id, "error", err)
				return
			}

			if job.Total == 0 {
				return
			}
		}
	}
}
