package agentup

type JobResponse struct {
	JobID string `json:"job_id"`
}

type JobDetailResponseResult struct {
	ID          int    `json:"id"`
	Updated     int    `json:"updated"`
	Created     int    `json:"created"`
	JobID       string `json:"job_id"`
	Status      string `json:"status"`
	Cmd         string `json:"cmd"`
	Target      string `json:"target"`
	ScriptType  string `json:"script_type"`
	Script      string `json:"script"`
	Args        string `json:"args"`
	Expires     int    `json:"expires"`
	Timeout     int    `json:"timeout"`
	Priority    int    `json:"priority"`
	Trigger     string `json:"trigger"`
	TriggerType string `json:"trigger_type"`
	Reason      string `json:"reason"`
	Result      string `json:"result"`
}

type JobDetailResponse struct {
	Page       int                       `json:"page"`
	Results    []JobDetailResponseResult `json:"results"`
	Total      int                       `json:"total"`
	TotalPages int                       `json:"total_pages"`
}
