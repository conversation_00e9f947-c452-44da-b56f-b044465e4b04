package app

import (
	"os"

	"github.com/urfave/cli/v2"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/app/commands"
)

var Version = "v0.1.0"

func Run() error {
	app := &cli.App{
		Name:      "syncer",
		Version:   Version,
		Usage:     "syncer is a tool to sync assets from clouds to oma",
		UsageText: "syncer command [arguments...]",
		Commands:  commands.Commands,
	}

	return app.Run(os.Args)
}
