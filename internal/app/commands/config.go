package commands

import (
	"encoding/json"
	"fmt"
	"os"

	"github.com/BurntSushi/toml"
	"github.com/urfave/cli/v2"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/config"
)

// CommandConfig returns a *cli.Command* for config stuffs
func CommandConfig() *cli.Command {
	return &cli.Command{
		Name:        "config",
		Usage:       "config stuffs",
		UsageText:   "config <command> [options]",
		Subcommands: configSubCommands(),
	}
}

func configSubCommands() []*cli.Command {
	return []*cli.Command{
		{
			Name:      "list",
			UsageText: "list [options]",
			Flags: []cli.Flag{
				&cli.StringFlag{
					Name:        "format",
					Aliases:     []string{"f"},
					Value:       "toml",
					DefaultText: "toml",
					Usage:       "output as specify format, supports: json, toml",
				},
			},
			Action: func(cx *cli.Context) error {
				format := cx.String("format")

				c := config.Config()

				switch format {
				case "toml":
					return toml.NewEncoder(os.Stdout).Encode(c)

				case "json":
					v, err := json.MarshalIndent(c, "", " ")
					if err != nil {
						fmt.Fprintf(os.Stderr, "marshal config failed, %v", err)
						os.Exit(1)
					}
					fmt.Printf("%s\n", v)

				default:
					fmt.Println("unknown format", format)
				}

				return nil
			},
		},
	}
}
