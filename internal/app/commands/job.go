package commands

import (
	"fmt"

	"github.com/urfave/cli/v2"
	"gitlab.lilithgame.com/yunwei/pkg/logger"

	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/api"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/config"
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/job"
)

// CommandJob returns a *cli.Command for running syncer job
func CommandJob() *cli.Command {
	return &cli.Command{
		Name:      "run",
		Usage:     "run syncer",
		UsageText: "syncer run",
		Flags: []cli.Flag{
			&cli.BoolFlag{
				Name:        "job",
				Value:       true,
				DefaultText: "true",
				Usage:       "enable syncer job",
				Aliases:     []string{"j"},
			},
			&cli.UintFlag{
				Name:        "port",
				Value:       8080,
				DefaultText: "8080",
				Usage:       "api's http port",
				Aliases:     []string{"p"},
			},
		},
		Before: func(*cli.Context) error {
			logger.InitFileLogger(config.Config().Log.Path, config.Config().Log.Level)
			return nil
		},
		Action: jobAction,
	}
}

func jobAction(ctx *cli.Context) error {
	if ctx.Bool("job") {
		go job.JobStart()
		logger.Info("job started")
	} else {
		logger.Info("job disabled")
	}

	port := ctx.Uint("port")
	logger.Debugf("%s %s, starting http server at [:%d]...", ctx.App.Name, ctx.App.Version, port)

	fmt.Printf("%s %s\n", ctx.App.Name, ctx.App.Version)
	api.Serve(fmt.Sprintf(":%d", port))

	return nil
}
