syntax = "proto3";

package oma.options;

option go_package = "gitlab.lilithgames.com/yunwei/aiops/protoc-gen-oma/options;options";

import "google/protobuf/descriptor.proto";

extend google.protobuf.MessageOptions {
  bool generate_methods = 1000; // Custom option to generate methods for the message
  string resource_type = 1001;   // Custom option to specify the resource name
}

extend google.protobuf.FieldOptions {
  string go_type = 1000; // Custom Go type for int fields
}

extend google.protobuf.MethodOptions {
  bool int_return_method = 1000; // Custom option to generate methods that return int
}
