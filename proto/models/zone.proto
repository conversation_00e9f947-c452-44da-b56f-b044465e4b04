syntax = "proto3";

package v1;

option go_package = "internal/restclient/models";

import "options/annotation.proto";
import "types/types.proto";

/**
 * 可用区资源定义
 */
message Zone {
  option (oma.options.generate_methods) = true;
  option (oma.options.resource_type) = "zone";

  oma.types.Int id = 1;                    // 可用区唯一标识ID
  string name = 2;                 // 可用区名称
  string zone_id = 3;              // 可用区外部ID
  string zone_type = 4;            // 可用区类型
  oma.types.Int factory = 5;               // 云厂商ID
  string factory__name = 6;         // 云厂商名称
  oma.types.Int region = 7;                // 区域ID
  string region__name = 8;          // 区域名称
  string desc = 9;                 // 描述
  int64 create_at = 10;            // 创建时间戳
  int64 update_at = 11;            // 更新时间戳
}
