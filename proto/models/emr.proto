syntax = "proto3";

package v1;

option go_package = "internal/restclient/models";

import "options/annotation.proto";
import "types/types.proto";

/**
 * EMR集群资源定义
 */
message EMRCluster {
  option (oma.options.generate_methods) = true;
  option (oma.options.resource_type) = "emr";

  oma.types.Int id = 1;                    // EMR集群唯一标识ID
  string cluster_id = 2;           // 集群外部ID
  string name = 3;                 // 集群名称
  string version = 4;              // 集群版本
  int64 factory = 5;               // 云厂商ID
  int64 account = 6;               // 账户ID
  int64 product = 7;               // 产品ID
  string region_id = 8;            // 区域ID
  string factory_name = 9;         // 云厂商名称
  string account_name = 10;        // 账户名称
  optional int64 create_time = 11 [(oma.options.go_type) = "time.Time"];         // 创建时间
  oma.types.Int create_at = 12;            // 创建时间戳
  oma.types.Int update_at = 13;            // 更新时间戳
}
