syntax = "proto3";

package v1;

option go_package = "internal/restclient/models";

import "options/annotation.proto";
import "types/types.proto";

/**
 * 访问控制列表资源定义
 */
message ACL {
  option (oma.options.generate_methods) = true; // 生成方法选项
  option (oma.options.resource_type) = "acl";

  oma.types.Int id = 1;                    // ACL唯一标识ID
  int64 create_at = 2;             // 创建时间戳
  int64 update_at = 3;             // 更新时间戳
  string acl_id = 4;               // ACL外部ID
  string acl_name = 5;             // ACL名称
  string ip_version = 6;           // IP版本
  string resource_group_id = 7;    // 资源组ID
  string acl_status = 8;           // ACL状态
  bool config_managed_enabled = 9; // 配置管理是否启用
  int64 entry_counts = 10;         // 条目数量
  string create_time = 11;         // 创建时间
  optional oma.types.Int factory = 12;              // 云厂商ID
  optional oma.types.Int account = 13;              // 账户ID
}

/**
 * ACL条目资源定义
 */
message ACLEntry {
  option (oma.options.generate_methods) = true; // 生成方法选项
  option (oma.options.resource_type) = "acl_entry";

  oma.types.Int id = 1;                    // ACL条目唯一标识ID
  string entry = 2;                // 条目内容
  string status = 3;               // 状态
  string description = 4;          // 描述
  string acl = 5;                  // 关联的ACL
  int64 create_at = 6;             // 创建时间戳
  int64 update_at = 7;             // 更新时间戳
}
