syntax = "proto3";

package v1;

option go_package = "internal/restclient/models";

import "options/annotation.proto";
import "types/types.proto";

/**
 * 负载均衡服务器组资源定义
 */
message LBServerGroup {
  option (oma.options.generate_methods) = true;
  option (oma.options.resource_type) = "lb_server_groups";

  oma.types.Int id = 1;                    // 服务器组唯一标识ID
  string group_id = 2;             // 服务器组外部ID
  string group_name = 3;           // 服务器组名称
  string lb = 4;      // 关联的负载均衡器ID
  oma.types.Time create_time = 5;          // 创建时间
  oma.types.Int create_at = 6;             // 创建时间戳
  oma.types.Int update_at = 7;             // 更新时间戳
  oma.types.Int factory = 8;               // 云厂商ID
  optional oma.types.Int account = 9;               // 账户ID
}

/**
 * 负载均衡服务器资源定义
 */
message LBServer {
  option (oma.options.generate_methods) = true;
  option (oma.options.resource_type) = "lb_server";

  oma.types.Int id = 1;                    // 服务器唯一标识ID
  string lb = 2;      // 关联的负载均衡器ID
  string server_group = 3;             // 关联的服务器组ID
  string server_id = 4;            // 服务器外部ID
  string server_type = 5;          // 服务器类型
  string ip = 6;                   // IP地址
  int64 port = 7;                  // 端口号
  int64 weight = 8;                // 权重
  string desc = 9;                 // 描述
  int64 create_at = 10 [(oma.options.go_type) = "int"];            // 创建时间戳
  int64 update_at = 11 [(oma.options.go_type) = "int"];            // 更新时间戳
}
