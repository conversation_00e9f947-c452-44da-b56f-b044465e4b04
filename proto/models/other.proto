syntax = "proto3";

package v1;

option go_package = "internal/restclient/models";
import "options/annotation.proto";
import "types/types.proto";

/**
 * 其他资源类型定义，用于表示不属于特定类别的通用资源
 */
message Other {
  option (oma.options.generate_methods) = true;
  option (oma.options.resource_type) = "other";

  oma.types.Int id = 1;                       // 资源唯一标识ID
  string region__name = 2;             // 区域名称
  string factory__name = 3;            // 云厂商名称
  oma.types.Int create_at = 4;                // 创建时间戳
  oma.types.Int update_at = 5;                // 更新时间戳
  string external_name = 6;            // 外部资源名称
  string external_uuid = 7;            // 外部资源UUID
  string external_status = 8;          // 外部资源状态
  oma.types.Int local_status = 9;             // 本地状态
  oma.types.Int instance_type = 10;           // 实例类型
  string resource_type = 11;           // 资源类型
  optional oma.types.Int region = 12;         // 区域ID
  optional oma.types.Int project = 13;        // 项目ID
  oma.types.Int factory = 14;                 // 云厂商ID
  optional oma.types.Int account = 15;        // 账户ID
  optional oma.types.Int product = 16;        // 产品ID
}
