syntax = "proto3";

package v1;

option go_package = "internal/restclient/models";
import "options/annotation.proto";
import "types/types.proto";

/**
 * 云主机资源定义，与Go struct Machine对应
 */
message Machine {
  option (oma.options.generate_methods) = true;
  option (oma.options.resource_type) = "machine";

  oma.types.Int id = 1;                       // 主机唯一标识ID
  string zone__name = 2;               // 可用区名称
  string region__name = 3;             // 区域名称
  string region__region_id = 4;        // 区域ID
  string factory__name = 5;            // 云厂商名称
  string factory__kms_account = 6;     // 云厂商KMS账户
  string product__name = 7;            // 产品名称
  oma.types.Int create_at = 8;                // 创建时间戳
  oma.types.Int update_at = 9;                // 更新时间戳
  string external_uuid = 10;          // 外部UUID
  string external_name = 11;          // 外部名称
  string external_hostname = 12;      // 外部主机名
  string external_status = 13;        // 外部状态
  string external_flavor = 14;        // 外部规格
  int64 cpu = 15;                     // CPU核心数
  double mem = 16;                    // 内存大小(GB)
  string os_name = 17;                // 操作系统名称
  string charge_type = 18;            // 计费类型
  int64 status = 19;                  // 状态
  string public_ip = 20;              // 公网IP
  string private_ip = 21;             // 内网IP
  oma.types.Any subnet = 22;                // 子网ID
  oma.types.Time update_time = 23;            // 更新时间
  optional oma.types.Time create_time = 24;            // 创建时间
  string gcp_project_id = 25;         // GCP项目ID
  int64 manual_product = 26;          // 手动产品ID
  string external_tags = 27;          // 外部标签
  oma.types.Any expired_time = 28;
  oma.types.Any expired_time_desc = 29;
  string agent_id = 30;               // Agent ID
  string agent_version = 31;          // Agent版本
  oma.types.Any flavor = 32;                  // 规格名称
  oma.types.Int zone = 33;                    // 可用区ID
  optional oma.types.Int product = 34;                 // 产品ID
  optional oma.types.Int project = 35;                 // 项目ID
  oma.types.Any group = 36;                  // 规格名称
  oma.types.Any user_group = 37;                  // 规格名称
  oma.types.Int factory = 38;                 // 云厂商ID
  optional oma.types.Int account = 39;                 // 账户ID
  string account__name = 40;           // 账户名称
  string desc = 41;
  repeated oma.types.Any label = 42;         // 标签列表
}
