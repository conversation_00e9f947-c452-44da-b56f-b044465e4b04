syntax = "proto3";

package v1;

option go_package = "internal/restclient/models";
import "options/annotation.proto";
import "types/types.proto";

/**
 * 子网资源定义
 */
message Subnet {
  option (oma.options.generate_methods) = true;
  option (oma.options.resource_type) = "subnet";

  oma.types.Int id = 1;                    // 子网唯一标识ID
  string vpc__vpc_name = 2;         // VPC名称
  string vpc__vpc_id = 3;           // VPC ID
  string vpc__vpc_factory = 4;      // VPC云厂商
  string factory__name = 5;         // 云厂商名称
  oma.types.Int create_at = 6;             // 创建时间戳
  oma.types.Int update_at = 7;             // 更新时间戳
  string subnet_id = 8;            // 子网外部ID
  string name = 9;                 // 子网名称
  string alias_name = 10;          // 别名
  string desc = 11;                // 描述
  string cidr_block = 12;          // CIDR块
  bool is_default = 13;            // 是否默认子网
  oma.types.Int available_ip_count = 14;   // 可用IP数量
  oma.types.Int resource_auto_sync = 15;   // 资源自动同步
  oma.types.Int vpc = 16;                  // VPC ID
  oma.types.Any zone = 17;
}

/**
 * 虚拟交换机资源定义（阿里云）
 */
message VSwitch {
  option (oma.options.generate_methods) = true;
  option (oma.options.resource_type) = "vswitch";

  oma.types.Int id = 1;                    // 虚拟交换机唯一标识ID
  int64 create_at = 2;             // 创建时间戳
  int64 update_at = 3;             // 更新时间戳
  string vswitch_id = 4;           // 虚拟交换机外部ID
  string name = 5;                 // 虚拟交换机名称
  string vpc_id = 6;               // VPC ID
  string desc = 7;                 // 描述
  string cidr_block = 8;           // CIDR块
  bool is_default = 9;             // 是否默认虚拟交换机
  string share_type = 10;          // 共享类型
  string status = 11;              // 状态
  string network_cidr_id = 12;     // 网络CIDR ID
  optional oma.types.Int region = 13;               // 区域ID
  optional oma.types.Int zone = 14;                 // 可用区ID
  string factory__name = 15;        // 云厂商名称
  oma.types.Int factory = 16;              // 云厂商ID
  optional oma.types.Int account = 17;              // 账户ID
}
