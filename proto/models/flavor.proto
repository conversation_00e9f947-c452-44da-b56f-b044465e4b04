syntax = "proto3";

package v1;

option go_package = "internal/restclient/models";
import "options/annotation.proto";
import "types/types.proto";

/**
 * 机器规格资源定义
 */
message Flavor {
  option (oma.options.generate_methods) = true;
  option (oma.options.resource_type) = "flavor";

  oma.types.Int id = 1;                    // 规格唯一标识ID
  string factory_name = 2;         // 云厂商名称
  string name = 3;                 // 规格名称
  string desc = 4;                 // 描述
  optional string flavor_id = 5;            // 规格外部ID
  oma.types.Int cpu = 6;                   // CPU核心数
  oma.types.Int mem = 7;                   // 内存大小(MB)
  oma.types.Int disk = 8;                  // 磁盘大小(GB)
  oma.types.Int price = 9;                 // 价格
  oma.types.Int factory = 10;              // 云厂商ID
  oma.types.Int create_at = 11;            // 创建时间戳
  oma.types.Int update_at = 12;            // 更新时间戳
}
