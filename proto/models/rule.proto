syntax = "proto3";

package v1;

option go_package = "internal/restclient/models";
import "options/annotation.proto";
import "types/types.proto";

message Rule {
  option (oma.options.generate_methods) = true;
  option (oma.options.resource_type) = "rule";

  oma.types.Int id = 1;            // 规则唯一标识ID
  oma.types.Int security = 2; // 安全组ID
  string security_group_id = 3; // 安全组外部ID
  string name = 4;               // 规则名称
  string rule_id = 5;           // 规则外部ID
  string policy = 6;             // 策略（允许/拒绝）
  string direction = 7;          // 方向（入站/出站）
  string protocol = 8;           // 协议（TCP/UDP/ICMP)
  oma.types.Int priority = 9; // 优先级
  string ip = 10;                 // IP地址
  string port = 11;                 // 端口号
  string dest_port = 12;             // 目标端口号
  oma.types.Time create_time = 13; // 创建时间
  oma.types.Int create_at = 14;     // 创建时间戳
  oma.types.Int update_at = 15;     // 更新时间戳
}
