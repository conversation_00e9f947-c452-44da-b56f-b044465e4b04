syntax = "proto3";

package v1;

option go_package = "internal/restclient/models";

import "options/annotation.proto";
import "types/types.proto";

/**
 * 产品资源定义
 */
message Product {
  option (oma.options.generate_methods) = true;
  option (oma.options.resource_type) = "product";

  oma.types.Int id = 1;                    // 产品唯一标识ID
  string external_id = 2;          // 外部ID
  string name = 3;                 // 产品名称
  string desc = 4;                 // 产品描述
  int64 update_at = 5;             // 更新时间戳
  int64 create_at = 6;             // 创建时间戳
}