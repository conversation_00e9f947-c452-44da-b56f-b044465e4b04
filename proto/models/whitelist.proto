syntax = "proto3";

package v1;

option go_package = "internal/restclient/models";

import "options/annotation.proto";
import "types/types.proto";

/**
 * MySQL白名单资源定义
 */
message MySQLWhitelist {
  option (oma.options.generate_methods) = true;
  option (oma.options.resource_type) = "mysql_whitelist";

  oma.types.Int id = 1;                    // 白名单唯一标识ID
  int64 create_at = 2;             // 创建时间戳
  int64 update_at = 3;             // 更新时间戳
  string group_name = 4;           // 组名
  string ip_type = 5;              // IP类型
  string ip_list = 6;              // IP列表
  string db = 7;                   // 关联的数据库
}

/**
 * Redis白名单资源定义
 */
message RedisWhitelist {
  option (oma.options.generate_methods) = true;
  option (oma.options.resource_type) = "redis_whitelist";

  oma.types.Int id = 1;                    // 白名单唯一标识ID
  int64 create_at = 2;             // 创建时间戳
  int64 update_at = 3;             // 更新时间戳
  string group_name = 4;           // 组名
  string ip_type = 5;              // IP类型
  string ip_list = 6;              // IP列表
  string db = 7;                   // 关联的数据库
}

/**
 * MongoDB白名单资源定义
 */
message MongoWhitelist {
  option (oma.options.generate_methods) = true;
  option (oma.options.resource_type) = "mongo_whitelist";

  oma.types.Int id = 1;                    // 白名单唯一标识ID
  int64 create_at = 2;             // 创建时间戳
  int64 update_at = 3;             // 更新时间戳
  string group_name = 4;           // 组名
  string ip_list = 5;              // IP列表
  string db = 6;                   // 关联的数据库
}
