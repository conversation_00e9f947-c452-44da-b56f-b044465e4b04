syntax = "proto3";

package v1;

option go_package = "internal/restclient/models";

import "options/annotation.proto";
import "types/types.proto";

/**
 * Redis缓存实例资源定义
 */
message Redis {
  option (oma.options.generate_methods) = true;
  option (oma.options.resource_type) = "redis";

  oma.types.Int id = 1;                    // Redis实例唯一标识ID
  string factory__name = 2;         // 云厂商名称
  string account__name = 3;         // 账户名称
  string region__name = 4;          // 区域名称
  string product__name = 5;         // 产品名称
  oma.types.Int create_at = 6;             // 创建时间戳
  oma.types.Int update_at = 7;             // 更新时间戳
  string external_name = 8;        // 外部名称
  string external_uuid = 9;        // 外部UUID
  string external_status = 10;     // 外部状态
  bool available = 11;             // 是否可用
  oma.types.Int types = 12;                // 类型
  string arch = 13;                // 架构
  string engine = 14;              // 引擎
  string version = 15;             // 版本
  string edition = 16;             // 版本
  string conn = 17;                // 连接信息
  int64 port = 18 [(oma.options.go_type) = "uint16"];                 // 端口号
  string private_ip = 19;          // 内网IP
  string public_ip = 20;           // 公网IP
  oma.types.Int capacity = 21;             // 容量
  oma.types.Int qps = 22;                  // QPS性能
  oma.types.Time create_time = 23;         // 创建时间
  oma.types.Int factory = 24;              // 云厂商ID
  oma.types.Int account = 25;              // 账户ID
  optional oma.types.Int project = 26;              // 项目ID
  oma.types.Int product = 27;              // 产品ID
  oma.types.Int region = 28;               // 区域ID
  repeated string tags = 29;      // 标签列表
}
