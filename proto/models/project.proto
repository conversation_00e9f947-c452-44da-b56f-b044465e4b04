syntax = "proto3";

package v1;

option go_package = "internal/restclient/models";

import "options/annotation.proto";
import "types/types.proto";

/**
 * 项目资源定义
 */
message Project {
  option (oma.options.generate_methods) = true;
  option (oma.options.resource_type) = "project";

  oma.types.Int id = 1;                    // 项目唯一标识ID
  string product__name = 2;         // 产品名称
  string admin__username = 3;       // 管理员用户名
  string admin__first_name = 4;     // 管理员名字
  string ops_user__username = 5;    // 运维用户用户名
  string ops_user_first_name = 6;  // 运维用户名字
  int64 create_at = 7;             // 创建时间戳
  int64 update_at = 8;             // 更新时间戳
  string name = 9;                 // 项目名称
  string desc = 10;                // 项目描述
  string project_id = 11;          // 项目外部ID
  int64 admin = 12;                // 管理员ID
  int64 ops_user = 13;             // 运维用户ID
  int64 product = 14;              // 产品ID
  int64 tags = 15;                 // 标签ID
  oma.types.Any subnet = 16;              // 子网ID
}
