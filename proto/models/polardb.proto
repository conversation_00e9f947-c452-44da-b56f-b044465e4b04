syntax = "proto3";

package v1;

option go_package = "internal/restclient/models";
import "options/annotation.proto";
import "types/types.proto";

/**
 * PolarDB数据库实例资源定义
 */
message Polar {
  option (oma.options.generate_methods) = true;
  option (oma.options.resource_type) = "polardb";

  oma.types.Int id = 1;                    // PolarDB实例唯一标识ID
  string region__name = 2;          // 区域名称
  string factory__name = 3;         // 云厂商名称
  oma.types.Int create_at = 4;             // 创建时间戳
  oma.types.Int update_at = 5;             // 更新时间戳
  string external_name = 6;        // 外部名称
  string external_uuid = 7;        // 外部UUID
  string external_status = 8;      // 外部状态
  string cluster_type = 9;         // 集群类型
  optional oma.types.Int region = 10;               // 区域ID
  optional oma.types.Int project = 11;              // 项目ID
  oma.types.Int factory = 12;              // 云厂商ID
  optional oma.types.Int account = 13;              // 账户ID
  optional oma.types.Int product = 14;              // 产品ID
}

/**
 * PolarDB白名单资源定义
 */
message PolarDBWhitelist {
  option (oma.options.generate_methods) = true;
  option (oma.options.resource_type) = "polardb_whitelist";

  oma.types.Int id = 1;                    // 白名单唯一标识ID
  oma.types.Int create_at = 2;             // 创建时间戳
  oma.types.Int update_at = 3;             // 更新时间戳
  string group_name = 4;           // 组名
  string ip_list = 5;              // IP列表
  string db = 6;                   // 关联的数据库
}
