syntax = "proto3";

package v1;

option go_package = "internal/restclient/models";
import "options/annotation.proto";
import "types/types.proto";

/**
 * 云区域资源定义
 */
message Region {
  option (oma.options.generate_methods) = true;
  option (oma.options.resource_type) = "region";

  oma.types.Int id = 1;                    // 区域唯一标识ID
  oma.types.Int factory = 2;               // 云厂商ID
  string factory__name = 3;         // 云厂商名称
  string name = 4;                 // 区域名称
  string region_id = 5;            // 区域外部ID
  string vendor_district = 6;      // 供应商区域
  oma.types.Int create_at = 7;             // 创建时间戳
  oma.types.Int update_at = 8;             // 更新时间戳
  string desc = 9;                 // 描述
}