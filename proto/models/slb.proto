syntax = "proto3";

package v1;

option go_package = "internal/restclient/models";

import "options/annotation.proto";
import "types/types.proto";

/**
 * 负载均衡器和监听器资源定义
 */
message SLBListener {
  option (oma.options.generate_methods) = true;
  option (oma.options.resource_type) = "slb_listener";

  oma.types.Int id = 1;                    // 监听器唯一标识ID
  string external_name = 2;        // 外部名称
  string external_status = 3;      // 外部状态
  string proto = 4;                // 协议
  oma.types.Int port = 5;                  // 端口
  oma.types.Int port_forward = 6;          // 转发端口
  string acl_uuid = 7;             // ACL UUID
  string acl_type = 8;             // ACL类型
  string acl_status = 9;           // ACL状态
  string scheduler = 10;           // 调度算法
  string lb = 11;                  // 关联的负载均衡器
  int64 create_at = 12;            // 创建时间戳
  int64 update_at = 13;            // 更新时间戳
}

// EIP 弹性IP信息
message EIPInfo {
  string external_uuid = 1;        // 外部UUID
  string public_ip = 2;            // 公网IP
  string isp = 3;                  // 网络供应商
}

// SLB 负载均衡器
message SLB {
  option (oma.options.generate_methods) = true;
  option (oma.options.resource_type) = "slb";

  oma.types.Int id = 1;                    // SLB唯一标识ID
  string slb_id = 2;               // SLB外部ID (external_uuid)
  string region__name = 3;          // 区域名称
  string region__region_id = 4;     // 区域ID
  string factory__name = 5;         // 云厂商名称
  string account__name = 6;         // 账户名称
  string product__name = 7;         // 产品名称
  string product__admin = 8;        // 产品管理员
  string product__admin_firstname = 9; // 产品管理员名字
  int64 create_at = 10;            // 创建时间戳
  int64 update_at = 11;            // 更新时间戳
  string lb_type = 12;             // 负载均衡器类型
  string external_name = 13;       // 外部名称
  string address = 14;             // 地址
  string external_status = 15;     // 外部状态
  string network_type = 16;        // 网络类型
  string ip_version = 17;          // IP版本
  string address_type = 18;        // 地址类型
  int64 gcp_project_id = 19;       // GCP项目ID
  int64 create_time = 20 [(oma.options.go_type) = "time.Time"];         // 创建时间
  int64 vpc = 21;                  // VPC ID
  string vpc__vpc_id = 22;          // VPC外部ID
  string vpc__name = 23;            // VPC名称
  optional oma.types.Int region = 24;               // 区域ID
  oma.types.Int factory = 25;              // 云厂商ID
  optional oma.types.Int account = 26;              // 账户ID
  optional oma.types.Int product = 27;              // 产品ID
  optional oma.types.Int project = 28;              // 项目ID

  // repeated 字段移到末尾
  repeated oma.types.Any label = 29;      // 标签列表
  repeated SLBListener listeners = 30; // 监听器列表
  repeated EIPInfo eips = 31;      // 弹性IP列表
}
