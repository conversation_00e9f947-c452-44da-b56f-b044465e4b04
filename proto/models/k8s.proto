syntax = "proto3";

package v1;

option go_package = "internal/restclient/models";

import "options/annotation.proto";
import "types/types.proto";

// K8sCluster 代表 Kubernetes 集群
message K8sCluster {
  option (oma.options.generate_methods) = true;
  option (oma.options.resource_type) = "k8s";

  oma.types.Int id = 1;                 // 主键ID
  string cluster_id = 2;                // 集群ID
  string cluster_name = 3;              // 集群名称
  string cluster_version = 4;           // 集群版本
  oma.types.Int factory = 5;            // 工厂ID
  oma.types.Int account = 6;            // 账号ID
  string factory_name = 7;              // 工厂名称
  string account_name = 8;              // 账号名称
  oma.types.Int created_at = 9;         // 创建时间
  oma.types.Int updated_at = 10;        // 更新时间
}

// K8sNodePool 代表 Kubernetes 节点池
message K8sNodePool {
  option (oma.options.generate_methods) = true;
  option (oma.options.resource_type) = "k8s_nodepool";

  oma.types.Int id = 1;
  string pool_id = 2;                   // 节点池 ID
  string pool_name = 3;                 // 节点池名称
  string cluster = 4;                   // 集群ID
  string cluster_name = 5;              // 集群名称
  oma.types.Int created_at = 6;         // 创建时间
  oma.types.Int updated_at = 7;         // 更新时间
}

// K8sNode 代表 Kubernetes 节点
message K8sNode {
  option (oma.options.generate_methods) = true;
  option (oma.options.resource_type) = "k8s_node";

  oma.types.Int id = 1;
  string instance_id = 2;                // 实例 ID
  string instance_name = 3;              // 实例名称
  string cluster = 4;                    // 集群ID
  string cluster_name = 5;               // 集群名称
  string pool = 6;                       // 节点池ID
  string pool_name = 7;                  // 节点池名称
  oma.types.Int created_at = 8;          // 创建时间
  oma.types.Int updated_at = 9;          // 更新时间
}