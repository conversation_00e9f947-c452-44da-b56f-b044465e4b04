syntax = "proto3";

package v1;

option go_package = "internal/restclient/models";

import "options/annotation.proto";
import "types/types.proto";

/**
 * MongoDB数据库实例资源定义
 */
message Mongo {
  option (oma.options.generate_methods) = true;
  option (oma.options.resource_type) = "mongo";

  oma.types.Int id = 1;                    // MongoDB实例唯一标识ID
  string region__name = 2;          // 区域名称
  int64 create_at = 3;             // 创建时间戳
  int64 update_at = 4;             // 更新时间戳
  string external_name = 5;        // 外部名称
  string external_uuid = 6;        // 外部UUID
  string external_status = 7;      // 外部状态
  int64 status = 8;                // 状态
  string db_type = 9;              // 数据库类型
  string replica_set_name = 10;    // 副本集名称
  int64 disk = 11;                 // 磁盘大小(GB)
  int64 cpu = 12;                  // CPU数量
  int64 mem = 13;                  // 内存大小(MB)
  int64 iops = 14;                 // IOPS
  int64 connections = 15;          // 连接数
  string version = 16;             // MongoDB版本
  string flavor_name = 17;         // 规格名称
  string primary_conn = 18;        // 主节点连接信息
  oma.types.Int primary_port = 19;         // 主节点端口
  string secondary_conn = 20;      // 从节点连接信息
  oma.types.Int secondary_port = 21;       // 从节点端口
  string readonly_conn = 22;       // 只读节点连接信息
  oma.types.Int readonly_port = 23;        // 只读节点端口
  string shard_conn = 24;          // 分片连接信息
  string mongos_conn = 25;         // Mongos连接信息
  oma.types.Int mongos_port = 26;          // Mongos端口
  string create_time = 27;         // 创建时间
  oma.types.Int region = 28;               // 区域ID
  oma.types.Int factory = 29;              // 云厂商ID
  string factory__name = 30;        // 云厂商名称
  optional oma.types.Int account = 31;              // 账户ID
  string account__name = 32;        // 账户名称
  optional oma.types.Int project = 33;              // 项目ID
  optional oma.types.Int product = 34;              // 产品ID
  repeated oma.types.Any label = 35;      // 标签列表
}
