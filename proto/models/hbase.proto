syntax = "proto3";

package v1;

option go_package = "internal/restclient/models";

import "options/annotation.proto";
import "types/types.proto";

/**
 * HBase数据库实例资源定义
 */
message HBase {
  option (oma.options.generate_methods) = true;
  option (oma.options.resource_type) = "hbase";

  oma.types.Int id = 1;                    // HBase实例唯一标识ID
  string region_name = 2;          // 区域名称
  string factory_name = 3;         // 云厂商名称
  int64 create_at = 4;             // 创建时间戳
  int64 update_at = 5;             // 更新时间戳
  string external_name = 6;        // 外部名称
  string external_uuid = 7;        // 外部UUID
  string external_status = 8;      // 外部状态
  oma.types.Int local_status = 9;          // 本地状态
  oma.types.Int instance_type = 10;        // 实例类型
  string resource_type = 11;       // 资源类型
  optional oma.types.Int region = 12;               // 区域ID
  optional oma.types.Int project = 13;              // 项目ID
  oma.types.Int factory = 14;              // 云厂商ID
  optional oma.types.Int account = 15;              // 账户ID
  optional oma.types.Int product = 16;              // 产品ID
}

/**
 * HBase白名单资源定义
 */
message HBaseWhitelist {
  option (oma.options.generate_methods) = true;
  option (oma.options.resource_type) = "hbase_whitelist";

  oma.types.Int id = 1;                    // 白名单唯一标识ID
  oma.types.Int create_at = 2;             // 创建时间戳
  oma.types.Int update_at = 3;             // 更新时间戳
  string group_name = 4;           // 组名
  string ip_list = 5;              // IP列表
  string db = 6;                   // 关联的数据库
}
