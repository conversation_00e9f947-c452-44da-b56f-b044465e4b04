syntax = "proto3";

package v1;

option go_package = "internal/restclient/models";

import "options/annotation.proto";
import "types/types.proto";

/**
 * 弹性IP资源定义
 */
message ElasticIP {
  option (oma.options.generate_methods) = true;
  option (oma.options.resource_type) = "eip";

  oma.types.Int id = 1;                    // EIP唯一标识ID
  oma.types.Int create_at = 2;             // 创建时间戳
  oma.types.Int update_at = 3;             // 更新时间戳
  string name = 4;                 // EIP名称
  string version = 5;              // 版本
  string isp = 6;                  // 网络供应商
  string external_uuid = 7;        // 外部UUID
  string external_status = 8;      // 外部状态
  string state = 9;                // 状态
  string public_ip = 10;           // 公网IP地址
  string bind_id = 11;             // 绑定资源ID
  optional oma.types.Int product = 12;              // 产品ID
  string bind_type = 13;           // 绑定类型
  string last_bind_id = 14;        // 上次绑定资源ID
  string last_bind_type = 15;      // 上次绑定类型
  oma.types.Int factory = 16;              // 云厂商ID
  string factory__name = 17;        // 云厂商名称
  optional oma.types.Int account = 18;              // 账户ID
  string account__name = 19;        // 账户名称
  string create_time = 20;         // 创建时间
}
