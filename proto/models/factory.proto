syntax = "proto3";

package v1;

option go_package = "internal/restclient/models";
import "options/annotation.proto";
import "types/types.proto";

/**
 * 云厂商资源定义
 */
message Factory {
  option (oma.options.generate_methods) = true;
  option (oma.options.resource_type) = "factory";

  oma.types.Int id = 1;          // 云厂商唯一标识ID
  int64 create_at = 2;             // 创建时间戳
  int64 update_at = 3;             // 更新时间戳
  string name = 4;                 // 云厂商名称
  string key_name = 5;             // 密钥名称
  string channel = 6;              // 渠道
  string desc = 7;                 // 描述
  string access_id = 8;            // 访问ID
  string access_key = 9;           // 访问密钥
  string rolearn = 10;             // 角色ARN
  string kms_account = 11;         // KMS账户
}

/**
 * 云厂商账户资源定义
 */
message FactoryAccount {
  option (oma.options.generate_methods) = true;
  option (oma.options.resource_type) = "account";

  oma.types.Int id = 1;                    // 账户唯一标识ID
  int64 create_at = 2;             // 创建时间戳
  int64 update_at = 3;             // 更新时间戳
  string name = 4;                 // 账户名称
  string key_name = 5;             // 密钥名称
  string channel = 6;              // 渠道
  string desc = 7;                 // 描述
  string access_id = 8;            // 访问ID
  string access_key = 9;           // 访问密钥
  string rolearn = 10;             // 角色ARN
  string kms_account = 11;         // KMS账户
  oma.types.Int factory = 12;              // 关联的云厂商ID
}
