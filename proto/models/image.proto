syntax = "proto3";

package v1;

option go_package = "internal/restclient/models";
import "options/annotation.proto";
import "types/types.proto";

/**
 * 镜像资源定义
 */
message Image {
  option (oma.options.generate_methods) = true;
  option (oma.options.resource_type) = "image";

  oma.types.Int id = 1;                    // 镜像唯一标识ID
  string region__name = 2;          // 区域名称
  string region__region_id = 3;     // 区域ID
  string factory__name = 4;         // 云厂商名称
  int64 create_at = 5;             // 创建时间戳
  int64 update_at = 6;             // 更新时间戳
  string external_id = 7;          // 外部ID
  string external_name = 8;        // 外部名称
  string external_desc = 9;        // 外部描述
  string external_osname = 10;     // 外部操作系统名称
  string image_type = 11;          // 镜像类型
  string local_desc = 12;          // 本地描述
  string project_id = 13;          // 项目ID
  oma.types.Int region = 14;               // 区域ID
  oma.types.Int factory = 15;              // 云厂商ID
}
