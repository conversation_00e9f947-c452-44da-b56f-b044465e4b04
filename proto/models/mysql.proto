syntax = "proto3";

package models.v1;

option go_package = "internal/restclient/models";

import "options/annotation.proto";
import "types/types.proto";

/**
 * MySQL数据库实例资源定义
 */
message MySQL {
  option (oma.options.generate_methods) = true;
  option (oma.options.resource_type) = "mysql";

  oma.types.Int id = 1;                    // MySQL实例唯一标识ID
  string region__name = 2;          // 区域名称
  string factory__name = 3;         // 云厂商名称
  string account__name = 4;         // 账户名称
  string product__name = 5;         // 产品名称
  oma.types.Int create_at = 6;             // 创建时间戳
  oma.types.Int update_at = 7;             // 更新时间戳
  string external_name = 8;        // 外部名称
  string external_uuid = 9;        // 外部UUID
  string external_status = 10;     // 外部状态
  oma.types.Int local_status = 11;         // 本地状态
  string conn = 12;                // 连接信息
  optional oma.types.Int port = 13;                 // 端口号
  optional oma.types.Int cpu = 14;                  // CPU数量
  optional oma.types.Int mem = 15;                  // 内存大小(MB)
  optional oma.types.Int disk = 16;                 // 磁盘大小(GB)
  optional oma.types.Int iops = 17;                 // IOPS
  string version = 18;             // MySQL版本
  string flavor_name = 19;         // 规格名称
  oma.types.Time create_time = 20;         // 创建时间
  oma.types.Int region = 21;               // 区域ID
  oma.types.Any subnet = 22;
  oma.types.Int factory = 23;              // 云厂商ID
  optional oma.types.Int account = 24;              // 账户ID
  optional oma.types.Int product = 25;              // 产品ID
  repeated oma.types.Int project = 26;    // 项目列表
  repeated string tags = 27;       // 标签列表
  repeated oma.types.Any label = 28;      // 标签列表
  repeated oma.types.Any sg = 29; // 安全组列表
}
