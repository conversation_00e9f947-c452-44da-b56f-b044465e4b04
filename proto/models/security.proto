syntax = "proto3";

package v1;

option go_package = "internal/restclient/models";

import "options/annotation.proto";
import "types/types.proto";

/**
 * 安全组资源定义
 */
message Security {
  option (oma.options.generate_methods) = true;
  option (oma.options.resource_type) = "security";

  oma.types.Int id = 1;                    // 安全组唯一标识ID
  string name = 2;                 // 安全组名称
  string vpc__vpc_id = 3;           // VPC外部ID
  string vpc__name = 4;             // VPC名称
  int64 rules_count = 5;           // 规则数量
  string security_id = 6;          // 安全组外部ID
  int64 ecs_count = 7;             // 关联的ECS数量
  int64 available_amount = 8;      // 可用数量
  string group_type = 9;           // 组类型
  optional bool service_managed = 10;       // 是否服务托管
  string desc = 11;                // 描述
  int64 vpc = 12;                  // VPC ID
  int64 factory = 13;              // 云厂商ID
  string factory__name = 14;        // 云厂商名称
  optional int64 account = 15;              // 账户ID
  string account__name = 16;        // 账户名称
  int64 create_at = 17;            // 创建时间戳
  int64 update_at = 18;            // 更新时间戳
}
