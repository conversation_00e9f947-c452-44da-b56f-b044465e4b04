syntax = "proto3";

package v1;

option go_package = "internal/restclient/models";

import "options/annotation.proto";
import "types/types.proto";

/**
 * 专有网络
 */
message VPC {
  option (oma.options.generate_methods) = true;
  option (oma.options.resource_type) = "vpc";

  oma.types.Int id = 1;            // VPC唯一标识ID
  int64 create_at = 2;     // 创建时间戳
  int64 update_at = 3;     // 更新时间戳
  string vpc_id = 4;       // 外部VPC ID
  string name = 5;         // VPC名称
  string desc = 6;         // VPC描述
  string cidr_block = 7;   // CIDR块
  optional oma.types.Int region = 8;        // 区域ID
  string factory__name = 9; // 云厂商名称
  oma.types.Int factory = 10;      // 云厂商ID
  optional oma.types.Int account = 11;      // 账户ID
}
