syntax = "proto3";

package v1;

option go_package = "internal/restclient/models";
import "options/annotation.proto";
import "types/types.proto";

/**
 * 块存储磁盘资源定义
 */
message BlockDisk {
  option (oma.options.generate_methods) = true;
  option (oma.options.resource_type) = "block_disk";

  oma.types.Int id = 1;                    // 磁盘唯一标识ID
  int64 create_at = 2;             // 创建时间戳
  int64 update_at = 3;             // 更新时间戳
  string disk_id = 4;              // 磁盘外部ID
  string name = 5;                 // 磁盘名称
  oma.types.Int size = 6;                  // 磁盘大小(GB)
  string disk_type = 7;            // 磁盘类型
  string instance_id = 8;          // 绑定的实例ID
  string last_bind_instance = 9;   // 上次绑定的实例ID
  string status = 10;              // 磁盘状态
  string region = 11;              // 区域
  string zone = 12;                // 可用区
  string category = 13;            // 类别
  string tags = 14;                // 标签
  string create_time = 15;         // 创建时间
  oma.types.Int factory = 16;              // 云厂商ID
  string factory_name = 17;        // 云厂商名称
  optional oma.types.Int account = 18;              // 账户ID
  string account_name = 19;        // 账户名称
}

/**
 * 日志服务资源定义
 */
message SLS {
  option (oma.options.generate_methods) = true;
  option (oma.options.resource_type) = "sls";

  oma.types.Int id = 1;                    // SLS项目唯一标识ID
  string factory__name = 2;         // 云厂商名称
  string account__name = 3;         // 账户名称
  string region__name = 4;          // 区域名称
  int64 create_at = 5;             // 创建时间戳
  int64 update_at = 6;             // 更新时间戳
  string external_name = 7;        // 外部名称
  string external_uuid = 8;        // 外部UUID
  string external_status = 9;      // 外部状态
  string desc = 10;                // 描述
  oma.types.Time create_time = 11;         // 创建时间
  oma.types.Int region = 12;               // 区域ID
  oma.types.Int factory = 13;              // 云厂商ID
  optional oma.types.Int account = 14;              // 账户ID
  optional oma.types.Int product = 15;              // 产品ID
}

/**
 * 日志库资源定义
 */
message LogStore {
  option (oma.options.generate_methods) = true;
  option (oma.options.resource_type) = "logstore";

  oma.types.Int id = 1;                    // 日志库唯一标识ID
  string name = 2;                 // 日志库名称
  oma.types.Int shards = 3;                // 分片数量
  oma.types.Int sls_project = 4;           // SLS项目ID
  string sls_project__name = 5;     // SLS项目名称
  oma.types.Int factory = 6;               // 云厂商ID
  oma.types.Int account = 7;               // 账户ID
  oma.types.Int create_at = 8;             // 创建时间戳
  oma.types.Int update_at = 9;             // 更新时间戳
  optional oma.types.Int ttl = 10;                  // 日志保留时间(天)
  optional oma.types.Int hot_ttl = 11;              // 热数据保留时间(天)
}
