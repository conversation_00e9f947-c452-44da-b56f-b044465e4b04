syntax = "proto3";

package v1;

option go_package = "internal/restclient/models";
import "options/annotation.proto";
import "types/types.proto";

message K8sMachine {
  option (oma.options.generate_methods) = true;
  option (oma.options.resource_type) = "k8s_machine";

  oma.types.Int id = 1;
  string vendor = 2;
  string account_name = 3;
  string instance_id = 4;
  string instance_name = 5;
  string cluster_id = 6;
  string cluster_name = 7;
  oma.types.Int created_at = 8;
  oma.types.Int updated_at = 9;
}