syntax = "proto3";

package oma.types;

option go_package = "gitlab.lilithgames.com/yunwei/aiops/protoc-gen-oma/types;types";

message Any {
  string type = 1; // The type of the Any message, e.g., "oma.types.Any"
  bytes value = 2; // The serialized value of the Any message
}

// Go built-in type wrappers for use in other proto files
message Int {
  int64 value = 1;
}

message Bytes{
  bytes value = 1;
}

message Time {
  int64 seconds = 1; // Seconds since epoch
  int32 nanos = 2;   // Nanoseconds offset within the second
}